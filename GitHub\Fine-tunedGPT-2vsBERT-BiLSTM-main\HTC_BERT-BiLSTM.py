#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
层次文本分类 (Hierarchical Text Classification) - BERT-BiLSTM 模型实现

该脚本实现了一个基于BERT和双向LSTM的层次文本分类模型，用于同时预测文本的类别和超类别。
模型结构：BERT编码器 + 双向LSTM + 全连接层
训练原理：多任务学习，同时优化类别分类和超类别分类任务

Created on Thu Aug  1 23:51:24 2024
@author: eedisgpu

Created on Tue Jul 16 17:41:30 2024
@author: Bo<PERSON><PERSON>
"""

# 导入必要的库
import os
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'

import pandas as pd          # 数据处理和分析
import numpy as np           # 数值计算
import nltk                  # 自然语言处理工具包
import re                    # 正则表达式
import string                # 字符串处理
import tensorflow as tf      # 深度学习框架
from sklearn.model_selection import train_test_split  # 数据集划分
from sklearn.metrics import accuracy_score, f1_score  # 评估指标
from nltk.corpus import stopwords                     # 停用词
from nltk.tokenize import word_tokenize               # 分词器
from transformers import BertTokenizer, TFBertModel   # BERT模型和分词器

# GPU配置和内存管理
# 检查GPU是否可用并设置内存增长模式，避免显存占满
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        # 为每个GPU设置内存增长模式，按需分配显存而不是一次性占满
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        logical_gpus = tf.config.experimental.list_logical_devices('GPU')
        print(f"检测到 {len(gpus)} 个物理GPU, {len(logical_gpus)} 个逻辑GPU")
    except RuntimeError as e:
        print(f"GPU配置错误: {e}")

# 下载NLTK所需的数据包
# punkt: 用于句子和单词分割
# stopwords: 英文停用词列表
nltk.download('punkt')
nltk.download('stopwords')

# 文本预处理函数
def preprocess_text(text):
    """
    文本预处理函数，执行以下步骤：
    1. 转换为小写 - 统一文本格式，减少词汇表大小
    2. 移除标点符号 - 去除噪声，专注于语义内容
    3. 分词 - 将文本分割为单词列表
    4. 移除停用词 - 去除"the", "and", "is"等无意义词汇

    参数:
        text (str): 原始文本
    返回:
        str: 预处理后的文本
    """
    # 转换为小写，统一文本格式
    text = text.lower()
    # 使用正则表达式移除所有标点符号
    text = re.sub(f'[{string.punctuation}]', '', text)
    # 使用NLTK进行分词
    tokens = word_tokenize(text)
    # 获取英文停用词集合并过滤
    stop_words = set(stopwords.words('english'))
    tokens = [word for word in tokens if word not in stop_words]
    return ' '.join(tokens)

# 加载数据集
# 假设CSV文件包含'text'列（原始文本）、'category'列（细分类别）和'super_category'列（超类别）
data = pd.read_csv('train_40k_Adapted.csv')

# 对所有文本进行预处理
# apply函数将preprocess_text应用到每一行文本上
data['processed_text'] = data['text'].apply(preprocess_text)

# 加载BERT分词器
# 使用预训练的bert-base-uncased模型的分词器
# uncased表示不区分大小写，适合我们已经小写化的文本
tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')

# 文本编码函数 - 将文本转换为BERT输入格式
def encode_texts(texts, tokenizer, max_length=128):
    """
    将文本列表编码为BERT模型所需的输入格式

    参数:
        texts: 文本列表
        tokenizer: BERT分词器
        max_length: 最大序列长度，超过则截断，不足则填充

    返回:
        dict: 包含input_ids和attention_mask的字典
        - input_ids: 词汇ID序列
        - attention_mask: 注意力掩码（1表示真实token，0表示填充token）
    """
    return tokenizer(
        texts.tolist(),           # 转换为列表格式
        max_length=max_length,    # 设置最大长度为128（BERT常用长度）
        padding='max_length',     # 填充到最大长度
        truncation=True,          # 超长文本进行截断
        return_tensors='tf'       # 返回TensorFlow张量格式
    )

# 对预处理后的文本进行BERT编码
encoded_texts = encode_texts(data['processed_text'], tokenizer)

# 数据集划分
# 将数据按7:3比例划分为训练集和测试集
# 同时划分输入特征(编码后的文本)和两个目标变量(类别和超类别)
X_train, X_test, y_train_cat, y_test_cat, y_train_super_cat, y_test_super_cat = train_test_split(
    encoded_texts['input_ids'].numpy(),  # 输入特征：BERT编码后的token ID
    data['category'],                     # 目标1：细分类别标签
    data['super_category'],              # 目标2：超类别标签
    test_size=0.3,                       # 测试集占30%
    random_state=42                      # 随机种子，确保结果可复现
)

# 创建标签映射
# 获取所有唯一的类别标签，用于后续的标签编码
category_labels = list(data['category'].unique())           # 细分类别列表
super_category_labels = list(data['super_category'].unique()) # 超类别列表

print(f"细分类别数量: {len(category_labels)}")
print(f"超类别数量: {len(super_category_labels)}")

# 将文本标签映射为数值索引
# 机器学习模型需要数值输入，因此将类别名称转换为索引
y_train_cat_idx = y_train_cat.apply(lambda x: category_labels.index(x))
y_test_cat_idx = y_test_cat.apply(lambda x: category_labels.index(x) if x in category_labels else -1)

y_train_super_cat_idx = y_train_super_cat.apply(lambda x: super_category_labels.index(x))
y_test_super_cat_idx = y_test_super_cat.apply(lambda x: super_category_labels.index(x) if x in super_category_labels else -1)

# 处理测试集中的未见标签
# 移除测试集中包含训练集未见过标签的样本，避免预测时出错
valid_test_indices = (y_test_cat_idx != -1) & (y_test_super_cat_idx != -1)
X_test = X_test[valid_test_indices]
y_test_cat_idx = y_test_cat_idx[valid_test_indices]
y_test_super_cat_idx = y_test_super_cat_idx[valid_test_indices]

# 转换为独热编码（One-hot Encoding）
# 将整数标签转换为独热向量，适合多分类任务的softmax输出
y_train_cat = tf.keras.utils.to_categorical(y_train_cat_idx, num_classes=len(category_labels))
y_test_cat = tf.keras.utils.to_categorical(y_test_cat_idx, num_classes=len(category_labels))

y_train_super_cat = tf.keras.utils.to_categorical(y_train_super_cat_idx, num_classes=len(super_category_labels))
y_test_super_cat = tf.keras.utils.to_categorical(y_test_super_cat_idx, num_classes=len(super_category_labels))

# BERT-BiLSTM模型构建函数
def create_fine_tuned_bert_model(num_categories, num_super_categories):
    """
    创建BERT-BiLSTM层次分类模型

    模型架构说明：
    1. BERT编码器：提取文本的深层语义特征
    2. 双向LSTM：捕获序列的长期依赖关系和上下文信息
    3. 全局最大池化：从序列中提取最重要的特征
    4. 双输出层：同时预测细分类别和超类别

    训练原理：
    - 多任务学习：同时优化两个分类任务，利用任务间的相关性
    - 迁移学习：使用预训练BERT模型，利用大规模语料的知识
    - 层次结构：超类别和细分类别存在层次关系，模型可以学习这种结构

    参数:
        num_categories: 细分类别数量
        num_super_categories: 超类别数量

    返回:
        tf.keras.Model: 编译后的模型
    """
    # 定义输入层
    # input_ids: BERT分词后的token ID序列，形状为(batch_size, 128)
    input_ids = tf.keras.layers.Input(shape=(128,), dtype=tf.int32, name='input_ids')
    # attention_mask: 注意力掩码，区分真实token和填充token
    attention_mask = tf.keras.layers.Input(shape=(128,), dtype=tf.int32, name='attention_mask')

    # 加载预训练BERT模型
    # bert-base-uncased: 12层Transformer，768维隐藏层，12个注意力头
    bert_model = TFBertModel.from_pretrained('bert-base-uncased')
    # 获取BERT的最后一层隐藏状态，形状为(batch_size, 128, 768)
    bert_output = bert_model(input_ids, attention_mask=attention_mask).last_hidden_state

    # 添加双向LSTM层
    # 双向LSTM可以同时捕获前向和后向的序列信息
    # 128个隐藏单元，return_sequences=True保持序列维度
    lstm_out = tf.keras.layers.Bidirectional(
        tf.keras.layers.LSTM(128, return_sequences=True)
    )(bert_output)

    # 全局最大池化层
    # 从每个特征维度中选择最大值，将序列压缩为固定长度向量
    # 输出形状：(batch_size, 256) [128*2，因为是双向LSTM]
    lstm_out = tf.keras.layers.GlobalMaxPooling1D()(lstm_out)

    # 输出层 - 多任务学习
    # 细分类别输出：使用softmax激活函数进行多分类
    category_output = tf.keras.layers.Dense(
        num_categories,
        activation='softmax',
        name='category_output'
    )(lstm_out)

    # 超类别输出：同样使用softmax激活函数
    super_category_output = tf.keras.layers.Dense(
        num_super_categories,
        activation='softmax',
        name='super_category_output'
    )(lstm_out)

    # 构建模型
    model = tf.keras.Model(
        inputs=[input_ids, attention_mask],
        outputs=[category_output, super_category_output]
    )

    # 编译模型
    # Adam优化器：自适应学习率，适合BERT微调
    # 学习率2e-5：BERT微调的经典学习率，避免破坏预训练权重
    # 分类交叉熵损失：适合多分类任务
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=2e-5),
        loss='categorical_crossentropy',  # 多分类损失函数
        metrics=['accuracy']              # 监控准确率
    )
    return model

# 获取类别数量并创建模型
num_categories = len(category_labels)
num_super_categories = len(super_category_labels)
model = create_fine_tuned_bert_model(num_categories, num_super_categories)

# 打印模型结构
print("\n模型结构概览:")
model.summary()

# 创建注意力掩码
# 注意力掩码告诉模型哪些位置是真实的token（1），哪些是填充的（0）
# 这对BERT模型正确处理变长序列至关重要
attention_masks = (X_train != 0).astype(int)

print(f"\n开始训练模型...")
print(f"训练集大小: {X_train.shape[0]}")
print(f"特征维度: {X_train.shape[1]}")

# 模型训练
# 训练参数详解：
# - epochs=3: 训练轮数，BERT微调通常2-4轮即可，避免过拟合
# - batch_size=16: 批次大小，平衡内存使用和训练稳定性
# - validation_split=0.2: 从训练集中分出20%作为验证集
history = model.fit(
    [X_train, attention_masks],  # 输入：token IDs和注意力掩码
    {
        'category_output': y_train_cat,           # 细分类别标签
        'super_category_output': y_train_super_cat # 超类别标签
    },
    validation_split=0.2,  # 验证集比例
    epochs=3,              # 训练轮数
    batch_size=16,         # 批次大小
    verbose=1              # 显示训练进度
)

print("\n模型训练完成，开始评估...")

# 模型评估
# 为测试集创建注意力掩码
attention_masks_test = (X_test != 0).astype(int)

# 进行预测
# 模型返回两个输出：细分类别概率和超类别概率
y_pred_cat, y_pred_super_cat = model.predict([X_test, attention_masks_test])

# 将概率转换为类别索引
# argmax函数找到概率最大的类别索引
y_pred_cat = np.argmax(y_pred_cat, axis=1)
y_pred_super_cat = np.argmax(y_pred_super_cat, axis=1)

# 将测试集的独热编码标签转换回类别索引
y_test_cat = np.argmax(y_test_cat, axis=1)
y_test_super_cat = np.argmax(y_test_super_cat, axis=1)

# 评估指标计算
def hierarchical_f1_score(y_true_cat, y_pred_cat, y_true_super_cat, y_pred_super_cat):
    """
    层次F1分数计算函数

    层次分类的评估需要考虑两个层级的预测都正确才算完全正确
    这个指标衡量模型在层次结构上的整体性能

    参数:
        y_true_cat: 真实的细分类别
        y_pred_cat: 预测的细分类别
        y_true_super_cat: 真实的超类别
        y_pred_super_cat: 预测的超类别

    返回:
        float: 层次F1分数
    """
    # 只有当细分类别和超类别都预测正确时，才认为预测完全正确
    correct_predictions = (y_true_cat == y_pred_cat) & (y_true_super_cat == y_pred_super_cat)
    # 计算层次F1分数
    hierarchical_f1 = f1_score(correct_predictions, [True] * len(correct_predictions), average='weighted')
    return hierarchical_f1

# 计算各项评估指标
print("\n计算评估指标...")

# 细分类别性能指标
category_accuracy = accuracy_score(y_test_cat, y_pred_cat)
category_f1 = f1_score(y_test_cat, y_pred_cat, average='weighted')

# 超类别性能指标
super_category_accuracy = accuracy_score(y_test_super_cat, y_pred_super_cat)
super_category_f1 = f1_score(y_test_super_cat, y_pred_super_cat, average='weighted')

# 层次分类性能指标
hierarchical_f1 = hierarchical_f1_score(y_test_cat, y_pred_cat, y_test_super_cat, y_pred_super_cat)

# 输出结果
print("\n" + "="*50)
print("       BERT-BiLSTM 层次文本分类模型 - GPU版本")
print("="*50)
print(f'细分类别准确率 (Category Accuracy): {category_accuracy:.4f}')
print(f'细分类别F1分数 (Category F1 Score): {category_f1:.4f}')
print(f'超类别准确率 (Super-Category Accuracy): {super_category_accuracy:.4f}')
print(f'超类别F1分数 (Super-Category F1 Score): {super_category_f1:.4f}')
print(f'层次F1分数 (Hierarchical F1 Score): {hierarchical_f1:.4f}')
print("="*50)

# 模型性能分析
print("\n模型性能分析:")
print(f"- 测试样本数量: {len(y_test_cat)}")
print(f"- 细分类别数量: {num_categories}")
print(f"- 超类别数量: {num_super_categories}")
print(f"- 模型参数量: {model.count_params():,}")

# 训练历史可视化建议
print("\n建议:")
print("1. 可以通过history对象查看训练过程中的损失和准确率变化")
print("2. 如果过拟合，可以减少训练轮数或添加正则化")
print("3. 如果欠拟合，可以增加训练轮数或调整学习率")
print("4. 可以尝试不同的BERT模型变体（如RoBERTa、DistilBERT）")

"""
模型训练原理总结：

1. 预训练模型微调 (Fine-tuning)：
   - 使用预训练的BERT模型作为特征提取器
   - 在特定任务数据上进行微调，保留通用语言知识
   - 学习率设置较小(2e-5)，避免破坏预训练权重

2. 多任务学习 (Multi-task Learning)：
   - 同时训练细分类别和超类别两个任务
   - 共享底层特征表示，利用任务间的相关性
   - 提高模型的泛化能力和鲁棒性

3. 层次分类 (Hierarchical Classification)：
   - 利用类别间的层次结构关系
   - 超类别为细分类别的上级概念
   - 层次约束有助于提高分类一致性

4. 序列建模增强：
   - BERT提供双向上下文理解
   - BiLSTM进一步捕获长期依赖关系
   - 全局最大池化提取关键特征

5. 训练策略：
   - 批次大小16：平衡内存使用和梯度稳定性
   - 3个训练轮次：避免过拟合，适合微调任务
   - 验证集监控：及时发现过拟合问题
"""

