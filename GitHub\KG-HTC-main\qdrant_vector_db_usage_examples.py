#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新设计的 QdrantVectorDB 类使用示例

本文件展示了如何使用新的 QdrantVectorDB 类的各种功能：
1. 基本初始化和配置
2. CSV数据加载和处理
3. 文本向量化和查询
4. 数据持久化和备份
5. 与现有代码的集成

特性：
- 内存模式运行，支持本地持久化存储
- 动态payload结构，根据CSV文件列名自动构建
- 单一集合设计，使用text字段进行向量化
- 向后兼容现有接口

作者：AI Assistant
日期：2025-01-28
"""

import os
import sys
import pandas as pd
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.qdrant_vector_db import QdrantVectorDB

def example_1_basic_usage():
    """
    示例1：基本使用方法
    """
    print("=== 示例1：基本使用方法 ===")
    
    # 初始化向量数据库
    vector_db = QdrantVectorDB(
        collection_name="example_basic",  # 集合名称
        use_memory=True,                  # 使用内存模式
        storage_path="./vector_storage",  # 本地存储路径
        text_field="text",               # 用于向量化的文本字段名
        auto_persist=True                # 自动持久化
    )
    
    # 添加单个文本
    result = vector_db.add_text(
        text="人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
        metadata={
            "category": "technology",
            "subcategory": "artificial_intelligence",
            "language": "chinese"
        }
    )
    print(f"添加文本结果: {result}")
    
    # 查询相似文本
    query_result = vector_db.query(
        query_text="机器学习和深度学习",
        n_results=5
    )
    
    print(f"查询结果数量: {len(query_result['documents'][0])}")
    for i, (doc, metadata, distance) in enumerate(zip(
        query_result['documents'][0],
        query_result['metadatas'][0], 
        query_result['distances'][0]
    )):
        print(f"  {i+1}. 相似度: {1-distance:.3f}")
        print(f"     文本: {doc[:50]}...")
        print(f"     元数据: {metadata}")
    
    # 获取统计信息
    stats = vector_db.get_statistics()
    print(f"数据库统计: {stats}")

def example_2_csv_loading():
    """
    示例2：从CSV文件加载数据
    """
    print("\n=== 示例2：从CSV文件加载数据 ===")
    
    # 创建示例CSV数据
    sample_data = {
        'Title': [
            'NASA火星探测任务',
            '深度学习在医疗诊断中的应用',
            '气候变化对生态系统的影响',
            '量子计算的最新进展',
            '可再生能源技术发展'
        ],
        'Text': [
            'NASA的火星探测任务旨在寻找火星上生命存在的证据，通过先进的探测器和科学仪器收集数据。',
            '深度学习技术在医疗影像诊断中展现出巨大潜力，能够帮助医生更准确地识别疾病。',
            '全球气候变化正在对各种生态系统产生深远影响，包括物种迁移和栖息地变化。',
            '量子计算技术的发展为解决复杂计算问题提供了新的可能性，在密码学和优化领域有重要应用。',
            '太阳能、风能等可再生能源技术的快速发展为实现碳中和目标提供了重要支撑。'
        ],
        'Cat1': ['earth_science', 'computer_science', 'earth_science', 'computer_science', 'earth_science'],
        'Cat2': ['space_science', 'artificial_intelligence', 'climate_science', 'quantum_computing', 'energy'],
        'Cat3': ['mars_exploration', 'medical_ai', 'ecology', 'quantum_algorithms', 'renewable_energy']
    }
    
    # 保存为CSV文件
    df = pd.DataFrame(sample_data)
    csv_path = "example_data.csv"
    df.to_csv(csv_path, index=False, encoding='utf-8')
    print(f"创建示例CSV文件: {csv_path}")
    
    # 初始化向量数据库
    vector_db = QdrantVectorDB(
        collection_name="example_csv",
        use_memory=True,
        auto_persist=True
    )
    
    # 加载CSV数据
    load_result = vector_db.load_csv_data(
        csv_path=csv_path,
        text_column="Text",  # 指定文本列，也可以让系统自动检测
        encoding='utf-8'
    )
    
    print(f"CSV加载结果: {load_result}")
    
    # 查询不同类别的数据
    print("\n查询不同类别的数据:")
    
    # 查询计算机科学相关内容
    cs_result = vector_db.query(
        query_text="人工智能和机器学习",
        n_results=3,
        where={"Cat1": "computer_science"}  # 使用元数据过滤
    )
    print(f"计算机科学相关结果: {len(cs_result['documents'][0])} 个")
    
    # 查询地球科学相关内容
    es_result = vector_db.query(
        query_text="环境和气候",
        n_results=3,
        where={"Cat1": "earth_science"}
    )
    print(f"地球科学相关结果: {len(es_result['documents'][0])} 个")
    
    # 清理临时文件
    if os.path.exists(csv_path):
        os.remove(csv_path)

def example_3_backward_compatibility():
    """
    示例3：向后兼容性 - 使用旧接口
    """
    print("\n=== 示例3：向后兼容性 - 使用旧接口 ===")
    
    # 使用旧的初始化方式
    vector_db = QdrantVectorDB(
        collection_name="example_compat",
        use_memory=True
    )
    
    # 使用旧的batch_add接口
    titles = [
        "深度学习基础",
        "自然语言处理",
        "计算机视觉"
    ]
    
    texts = [
        "深度学习是机器学习的一个子领域，使用多层神经网络来学习数据的表示。",
        "自然语言处理是人工智能的一个分支，专注于计算机与人类语言之间的交互。",
        "计算机视觉是一个跨学科领域，研究如何使计算机获得对数字图像或视频的高层次理解。"
    ]
    
    metadatas = [
        {"level": "Category1", "topic": "machine_learning"},
        {"level": "Category2", "topic": "nlp"},
        {"level": "Category3", "topic": "computer_vision"}
    ]
    
    # 批量添加数据
    vector_db.batch_add(titles, texts, metadatas)
    print("使用旧接口批量添加数据完成")
    
    # 使用旧的查询接口
    print("\n使用旧的查询接口:")
    
    # 按title查询
    title_result = vector_db.query_by_title("深度学习")
    print(f"Title查询结果: {len(title_result['documents'][0])} 个")
    
    # 按text查询
    text_result = vector_db.query_by_text("神经网络")
    print(f"Text查询结果: {len(text_result['documents'][0])} 个")
    
    # 分级查询
    l1_result = vector_db.query_l1("机器学习")
    l2_result = vector_db.query_l2("自然语言")
    l3_result = vector_db.query_l3("图像识别")
    
    print(f"L1查询结果: {len(l1_result['documents'][0])} 个")
    print(f"L2查询结果: {len(l2_result['documents'][0])} 个")
    print(f"L3查询结果: {len(l3_result['documents'][0])} 个")

def example_4_persistence_and_backup():
    """
    示例4：数据持久化和备份
    """
    print("\n=== 示例4：数据持久化和备份 ===")
    
    storage_path = "./example_storage"
    
    # 第一阶段：创建数据
    print("第一阶段：创建和保存数据")
    vector_db1 = QdrantVectorDB(
        collection_name="example_persist",
        storage_path=storage_path,
        auto_persist=True
    )
    
    # 增量添加数据
    new_data = [
        {
            "text": "区块链技术是一种分布式账本技术，具有去中心化、不可篡改等特点。",
            "category": "blockchain",
            "difficulty": "intermediate"
        },
        {
            "text": "物联网连接了各种设备和传感器，实现了智能化的数据收集和处理。",
            "category": "iot", 
            "difficulty": "beginner"
        }
    ]
    
    update_result = vector_db1.incremental_update(new_data)
    print(f"增量更新结果: {update_result}")
    
    # 创建备份
    backup_path = vector_db1.backup_data()
    print(f"数据备份到: {backup_path}")
    
    # 第二阶段：重新加载数据
    print("\n第二阶段：重新加载持久化数据")
    vector_db2 = QdrantVectorDB(
        collection_name="example_persist",
        storage_path=storage_path,
        auto_persist=True
    )
    
    # 验证数据是否正确恢复
    stats = vector_db2.get_statistics()
    print(f"恢复后的统计信息: {stats}")
    
    # 查询恢复的数据
    query_result = vector_db2.query("区块链和物联网", n_results=5)
    print(f"查询恢复的数据: {len(query_result['documents'][0])} 个结果")

def example_5_integration_with_pipeline():
    """
    示例5：与Pipeline类集成
    """
    print("\n=== 示例5：与Pipeline类集成 ===")
    
    # 模拟Pipeline类的配置
    config = {
        "data_name": "integration_test",
        "qdrant_host": None,  # 使用内存模式
        "qdrant_port": 6333,
        "qdrant_use_memory": True,
        "template": {
            "sys": "prompts/system/nasa/llm_graph.txt",
            "user": "prompts/user/nasa/llm_graph.txt"
        },
        "query_params": {
            "l2_top_k": 15,
            "l3_top_k": 50
        }
    }
    
    # 直接使用QdrantVectorDB（模拟Pipeline中的使用方式）
    vector_db = QdrantVectorDB(
        host=config.get("qdrant_host"),
        port=config.get("qdrant_port", 6333),
        collection_name=config.get("data_name", "default"),
        use_memory=config.get("qdrant_use_memory", True)
    )
    
    print("✓ 成功集成到Pipeline配置中")
    
    # 模拟添加分类标签数据
    labels_l1 = ["earth_science", "computer_science", "life_science"]
    labels_l2 = ["atmosphere", "artificial_intelligence", "biology"]
    labels_l3 = ["aerosols", "machine_learning", "genetics"]
    
    # 添加L1标签
    vector_db.batch_add(
        titles=labels_l1,
        texts=labels_l1,
        metadatas=[{"level": "Category1"}] * len(labels_l1)
    )
    
    # 添加L2标签
    vector_db.batch_add(
        titles=labels_l2,
        texts=labels_l2,
        metadatas=[{"level": "Category2"}] * len(labels_l2)
    )
    
    # 添加L3标签
    vector_db.batch_add(
        titles=labels_l3,
        texts=labels_l3,
        metadatas=[{"level": "Category3"}] * len(labels_l3)
    )
    
    print("✓ 成功添加分层标签数据")
    
    # 测试分层查询（Pipeline中使用的功能）
    test_query = "artificial intelligence and machine learning"
    
    l2_results = vector_db.query_l2(test_query, n_results=3)
    l3_results = vector_db.query_l3(test_query, n_results=5)
    
    print(f"L2查询结果: {l2_results['documents'][0]}")
    print(f"L3查询结果: {l3_results['documents'][0]}")
    
    print("✓ Pipeline集成测试完成")

def main():
    """
    运行所有示例
    """
    print("🚀 QdrantVectorDB 使用示例")
    print("=" * 50)
    
    try:
        example_1_basic_usage()
        example_2_csv_loading()
        example_3_backward_compatibility()
        example_4_persistence_and_backup()
        example_5_integration_with_pipeline()
        
        print("\n🎉 所有示例运行完成！")
        
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
