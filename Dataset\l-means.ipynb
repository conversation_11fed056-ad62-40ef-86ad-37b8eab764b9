{"cells": [{"cell_type": "code", "execution_count": 3, "id": "ef6a2306", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在从PubChem获取 1000 个分子...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1000 [00:29<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["警告：仅成功获取 0 个分子。\n", "分子获取完成。\n", "未能获取任何分子，程序退出。\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# -*- coding: utf-8 -*-\n", "\"\"\"\n", "分子聚类分析脚本 (高级版)\n", "\n", "该脚本将执行以下步骤：\n", "1. 从PubChem下载分子。\n", "2. 使用RDKit为每个分子生成分子指纹。\n", "3. 使用可选的聚类算法 (K-Means, AgglomerativeClustering, DBSCAN) 对分子进行聚类。\n", "4. 分析并展示聚类结果，包括：\n", "    a. 定量分析：计算簇内和簇间的平均Tanimoto相似度。\n", "    b. 可视化分析：绘制每个簇的代表性分子结构图。\n", "\n", "**依赖库安装:**\n", "在运行此脚本之前，请确保已安装以下Python库：\n", "pip install rdkit pubchempy scikit-learn numpy tqdm matplotlib\n", "\"\"\"\n", "\n", "import numpy as np\n", "import pubchempy as pcp\n", "from rdkit import Chem\n", "from rdkit.Chem import AllChem, Draw\n", "from rdkit.DataStructs import TanimotoSimilarity\n", "from sklearn.cluster import KMeans, AgglomerativeClustering, DBSCAN\n", "from tqdm import tqdm\n", "import random\n", "import matplotlib.pyplot as plt\n", "from scipy.spatial.distance import pdist, squareform\n", "\n", "# --- 1. 参数定义 ---\n", "# 注意：对于10000个分子，AgglomerativeClustering和相似度计算会非常慢。\n", "# 为了快速演示，建议将数量减少到1000-2000。\n", "NUM_MOLECULES_TO_FETCH = 1000   # 希望获取的分子数量\n", "FINGERPRINT_RADIUS = 2          # Morgan指纹的半径\n", "FINGERPRINT_BITS = 2048         # Morgan指纹的位数\n", "\n", "# --- 选择聚类算法 ---\n", "# 可选项: 'KMEANS', 'AGGLOMERATIVE', 'DBSCAN'\n", "ALGORITHM = 'KMEANS'\n", "\n", "# --- 算法特定参数 ---\n", "# K-Means 和 Agglomerative\n", "NUM_CLUSTERS = 8\n", "# DBSCAN\n", "DBSCAN_EPS = 0.4        # 需要根据数据进行调整\n", "DBSCAN_MIN_SAMPLES = 5  # 需要根据数据进行调整\n", "\n", "\n", "# --- 2. 从PubChem获取分子 (与之前版本相同) ---\n", "def fetch_molecules(num_molecules):\n", "    \"\"\"从PubChem随机获取指定数量的分子SMILES表示。\"\"\"\n", "    print(f\"正在从PubChem获取 {num_molecules} 个分子...\")\n", "    smiles_list = []\n", "    max_cid = 200000000\n", "    random_cids = random.sample(range(1, max_cid), k=int(num_molecules * 1.5))\n", "    pbar = tqdm(total=num_molecules)\n", "    batch_size = 100\n", "    for i in range(0, len(random_cids), batch_size):\n", "        if len(smiles_list) >= num_molecules: break\n", "        batch_cids = random_cids[i:i+batch_size]\n", "        try:\n", "            compounds = pcp.get_compounds(batch_cids)\n", "            for compound in compounds:\n", "                if compound.canonical_smiles:\n", "                    smiles_list.append(compound.canonical_smiles)\n", "                    pbar.update(1)\n", "                    if len(smiles_list) >= num_molecules: break\n", "        except Exception:\n", "            pass\n", "    pbar.close()\n", "    if len(smiles_list) < num_molecules:\n", "        print(f\"警告：仅成功获取 {len(smiles_list)} 个分子。\")\n", "    print(\"分子获取完成。\")\n", "    return smiles_list\n", "\n", "# --- 3. 生成分子指纹 (与之前版本相同) ---\n", "def generate_fingerprints(smiles_list):\n", "    \"\"\"将SMILES列表转换为Morgan分子指纹列表。\"\"\"\n", "    print(\"正在生成分子指纹...\")\n", "    fingerprints = []\n", "    valid_smiles = []\n", "    for smiles in tqdm(smiles_list):\n", "        mol = Chem.Mo<PERSON>rom<PERSON>(smiles)\n", "        if mol is not None:\n", "            fp = AllChem.GetMorganFingerprintAsBitVect(mol, FINGERPRINT_RADIUS, nBits=FINGERPRINT_BITS)\n", "            fingerprints.append(fp)\n", "            valid_smiles.append(smiles)\n", "    print(\"分子指纹生成完成。\")\n", "    return valid_smiles, fingerprints\n", "\n", "# --- 4. 执行聚类 ---\n", "def run_clustering(fingerprints_np, algorithm, **kwargs):\n", "    \"\"\"根据选择的算法执行聚类。\"\"\"\n", "    print(f\"正在使用 {algorithm} 算法进行聚类...\")\n", "    if algorithm == 'KMEANS':\n", "        model = KMeans(n_clusters=kwargs['num_clusters'], random_state=42, n_init=10)\n", "        model.fit(fingerprints_np)\n", "    elif algorithm == 'AGGLOMERATIVE':\n", "        # Tanimoto距离 = 1 - <PERSON><PERSON><PERSON>相似度\n", "        # pdist需要一个1D数组\n", "        tanimoto_dist_condensed = pdist(fingerprints_np, metric='jaccard')\n", "        model = AgglomerativeClustering(n_clusters=kwargs['num_clusters'], metric='precomputed', linkage='average')\n", "        model.fit(squareform(tanimoto_dist_condensed))\n", "    elif algorithm == 'DBSCAN':\n", "        tanimoto_dist_condensed = pdist(fingerprints_np, metric='jaccard')\n", "        model = DBSCAN(eps=kwargs['eps'], min_samples=kwargs['min_samples'], metric='precomputed')\n", "        model.fit(squareform(tanimoto_dist_condensed))\n", "    else:\n", "        raise ValueError(\"未知的算法选择！\")\n", "    print(\"聚类完成。\")\n", "    return model\n", "\n", "# --- 5. 定量分析 ---\n", "def analyze_similarity(fingerprints, labels):\n", "    \"\"\"计算并报告簇内和簇间的Tanimoto相似度。\"\"\"\n", "    print(\"\\n--- 聚类质量定量分析 (Tanimoto相似度) ---\")\n", "    unique_labels = sorted(list(set(labels)))\n", "    if -1 in unique_labels: # DBSCAN的噪声点\n", "        unique_labels.remove(-1)\n", "\n", "    # 计算簇内相似度\n", "    intra_cluster_similarities = {}\n", "    for label in unique_labels:\n", "        indices = np.where(labels == label)[0]\n", "        if len(indices) < 2:\n", "            intra_cluster_similarities[label] = 1.0 # 单个分子的簇\n", "            continue\n", "        \n", "        cluster_fps = [fingerprints[i] for i in indices]\n", "        total_similarity = 0\n", "        pair_count = 0\n", "        for i in range(len(cluster_fps)):\n", "            for j in range(i + 1, len(cluster_fps)):\n", "                total_similarity += TanimotoSimilarity(cluster_fps[i], cluster_fps[j])\n", "                pair_count += 1\n", "        intra_cluster_similarities[label] = total_similarity / pair_count\n", "        print(f\"簇 {label} 的平均簇内相似度: {intra_cluster_similarities[label]:.4f}\")\n", "\n", "    # 计算簇间相似度\n", "    inter_cluster_similarity_sum = 0\n", "    inter_cluster_pair_count = 0\n", "    for i in range(len(unique_labels)):\n", "        for j in range(i + 1, len(unique_labels)):\n", "            label1, label2 = unique_labels[i], unique_labels[j]\n", "            indices1 = np.where(labels == label1)[0]\n", "            indices2 = np.where(labels == label2)[0]\n", "            \n", "            fps1 = [fingerprints[i] for i in indices1]\n", "            fps2 = [fingerprints[j] for j in indices2]\n", "\n", "            total_similarity = 0\n", "            pair_count = 0\n", "            for fp1 in fps1:\n", "                for fp2 in fps2:\n", "                    total_similarity += TanimotoSimilarity(fp1, fp2)\n", "                    pair_count += 1\n", "            \n", "            avg_sim = total_similarity / pair_count if pair_count > 0 else 0\n", "            inter_cluster_similarity_sum += avg_sim\n", "            inter_cluster_pair_count += 1\n", "\n", "    avg_inter_sim = inter_cluster_similarity_sum / inter_cluster_pair_count if inter_cluster_pair_count > 0 else 0\n", "    print(f\"\\n所有簇之间的平均簇间相似度: {avg_inter_sim:.4f}\")\n", "\n", "# --- 6. 可视化分析 ---\n", "def visualize_clusters(smiles_list, fingerprints, labels, model):\n", "    \"\"\"为每个簇找到代表性分子并绘制其结构图。\"\"\"\n", "    print(\"\\n--- 生成代表性分子结构图 ---\")\n", "    unique_labels = sorted(list(set(labels)))\n", "    is_noise = -1 in unique_labels\n", "    if is_noise:\n", "        unique_labels.remove(-1)\n", "        print(f\"发现 {np.sum(labels == -1)} 个噪声点 (在DBSCAN中)，将不予显示。\")\n", "\n", "    if not unique_labels:\n", "        print(\"没有发现有效的簇。\")\n", "        return\n", "\n", "    representative_mols = []\n", "    cluster_titles = []\n", "\n", "    for label in unique_labels:\n", "        indices = np.where(labels == label)[0]\n", "        cluster_fps = [fingerprints[i] for i in indices]\n", "        \n", "        # 寻找代表性分子 (medoid: 与簇内其他所有点平均相似度最高的点)\n", "        medoid_idx = -1\n", "        max_avg_sim = -1\n", "        \n", "        if len(indices) == 1:\n", "            medoid_idx = 0\n", "        else:\n", "            for i in range(len(cluster_fps)):\n", "                avg_sim = np.mean([TanimotoSimilarity(cluster_fps[i], cluster_fps[j]) for j in range(len(cluster_fps)) if i != j])\n", "                if avg_sim > max_avg_sim:\n", "                    max_avg_sim = avg_sim\n", "                    medoid_idx = i\n", "                    \n", "        rep_smiles = smiles_list[indices[medoid_idx]]\n", "        representative_mols.append(Chem.MolFromSmiles(rep_smiles))\n", "        cluster_titles.append(f\"Cluster {label} ({len(indices)} mols)\")\n", "\n", "    # 绘图\n", "    img = Draw.MolsToGridImage(representative_mols,\n", "                               molsPerRow=4,\n", "                               subImgSize=(300, 300),\n", "                               legends=cluster_titles)\n", "    \n", "    # Matplotlib显示\n", "    # MolsToGridImage返回一个PIL图像，可以直接在Jupyter Notebook中显示\n", "    # 为了在脚本中显示，我们用matplotlib\n", "    plt.figure(figsize=(15, 15))\n", "    plt.imshow(img)\n", "    plt.axis('off')\n", "    plt.title(f'{ALGORITHM} 聚类代表性分子', fontsize=20)\n", "    plt.show()\n", "\n", "\n", "# --- 7. 主执行流程 ---\n", "if __name__ == \"__main__\":\n", "    all_smiles = fetch_molecules(NUM_MOLECULES_TO_FETCH)\n", "    \n", "    if not all_smiles:\n", "        print(\"未能获取任何分子，程序退出。\")\n", "    else:\n", "        valid_smiles, fps_list = generate_fingerprints(all_smiles)\n", "        fps_np = np.array([list(fp) for fp in fps_list])\n", "        \n", "        if fps_np.shape[0] > 0:\n", "            # 执行聚类\n", "            model = run_clustering(fps_np, ALGORITHM, \n", "                                   num_clusters=NUM_CLUSTERS, \n", "                                   eps=DBSCAN_EPS, \n", "                                   min_samples=DBSCAN_MIN_SAMPLES)\n", "            labels = model.labels_\n", "            \n", "            # 分析和展示结果\n", "            analyze_similarity(fps_list, labels)\n", "            visualize_clusters(valid_smiles, fps_list, labels, model)\n", "            \n", "        else:\n", "            print(\"未能生成任何有效的分子指纹，程序退出。\")"]}], "metadata": {"kernelspec": {"display_name": "py310", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}