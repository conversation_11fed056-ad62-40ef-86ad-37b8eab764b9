import pandas as pd
import numpy as np
from pathlib import Path
import sys

# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

def preprocess_nasa_data(input_path: str, output_path: str):
    """
    预处理NASA数据集，转换为KG-HTC所需格式
    
    Args:
        input_path: NASA原始数据路径
        output_path: 处理后数据保存路径
    """
    print("开始预处理NASA数据集...")
    
    # 读取原始数据
    df = pd.read_csv(input_path)
    print(f"原始数据集大小: {len(df)} 条记录")
    
    # 字段映射和重命名
    df_processed = df.rename(columns={
        'Title': 'Title',
        'Abstract': 'Text',
        'Keyword_1_Category': 'Cat1',
        'Keyword_1_Topic': 'Cat2', 
        'Keyword_1_Term': 'Cat3'
    })
    
    # 选择需要的字段
    required_columns = ['Title', 'Text', 'Cat1', 'Cat2', 'Cat3']
    df_processed = df_processed[required_columns]
    
    # 数据清洗
    print("进行数据清洗...")
    
    # 1. 移除空值
    initial_count = len(df_processed)
    df_processed = df_processed.dropna(subset=['Title', 'Text', 'Cat1', 'Cat2', 'Cat3'])
    print(f"移除空值后: {len(df_processed)} 条记录 (移除了 {initial_count - len(df_processed)} 条)")
    
    # 2. 移除空字符串
    for col in ['Title', 'Text', 'Cat1', 'Cat2', 'Cat3']:
        df_processed = df_processed[df_processed[col].str.strip() != '']
    
    # 3. 标准化分类标签
    def standardize_category(text):
        """标准化分类标签"""
        if pd.isna(text):
            return "unknown"
        return str(text).strip().lower()
    
    df_processed['Cat1'] = df_processed['Cat1'].apply(standardize_category)
    df_processed['Cat2'] = df_processed['Cat2'].apply(standardize_category)
    df_processed['Cat3'] = df_processed['Cat3'].apply(standardize_category)
    
    # 4. 移除unknown标签
    df_processed = df_processed[
        (df_processed['Cat1'] != 'unknown') & 
        (df_processed['Cat2'] != 'unknown') & 
        (df_processed['Cat3'] != 'unknown')
    ]
    
    # 5. 文本长度过滤
    df_processed = df_processed[df_processed['Text'].str.len() >= 10]  # 至少10个字符
    df_processed = df_processed[df_processed['Text'].str.len() <= 5000]  # 最多5000个字符
    
    print(f"数据清洗完成: {len(df_processed)} 条记录")
    
    # 统计信息
    print("\n=== 数据集统计信息 ===")
    print(f"一级分类数量: {df_processed['Cat1'].nunique()}")
    print(f"二级分类数量: {df_processed['Cat2'].nunique()}")
    print(f"三级分类数量: {df_processed['Cat3'].nunique()}")
    
    print("\n=== 一级分类分布 ===")
    print(df_processed['Cat1'].value_counts().head(10))
    
    print("\n=== 二级分类分布 ===")
    print(df_processed['Cat2'].value_counts().head(10))
    
    # 保存处理后的数据
    df_processed.to_csv(output_path, index=False)
    print(f"\n处理后的数据已保存到: {output_path}")
    
    return df_processed

def create_train_test_split(df: pd.DataFrame, test_size: float = 0.3, random_state: int = 42):
    """
    创建训练集和测试集
    
    Args:
        df: 预处理后的数据框
        test_size: 测试集比例
        random_state: 随机种子
    """
    from sklearn.model_selection import train_test_split
    
    # 分层抽样，确保各类别在训练集和测试集中的分布一致
    train_df, test_df = train_test_split(
        df, 
        test_size=test_size, 
        random_state=random_state,
        stratify=df['Cat3']  # 基于一级分类进行分层
    )
    
    return train_df, test_df

if __name__ == "__main__":
    # 配置路径
    input_path = "D:/Project/NASA-Data-Deal/5-topics_with_multiple_terms.csv"
    output_dir = Path("dataset/nasa")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 预处理数据
    df_processed = preprocess_nasa_data(
        input_path=input_path,
        output_path=output_dir / "nasa_processed.csv"
    )
    
    # 创建训练集和测试集
    train_df, test_df = create_train_test_split(df_processed)
    
    # 保存训练集和测试集
    train_df.to_csv(output_dir / "nasa_train.csv", index=False)
    test_df.to_csv(output_dir / "nasa_val.csv", index=False)
    
    print(f"\n训练集大小: {len(train_df)} 条记录")
    print(f"测试集大小: {len(test_df)} 条记录")
    print("数据预处理完成！")
