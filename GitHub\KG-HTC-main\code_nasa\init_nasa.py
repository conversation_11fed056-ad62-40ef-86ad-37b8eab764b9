import pandas as pd
import sys
from pathlib import Path
from tqdm import tqdm
import logging

# 禁用 httpx 的详细日志
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)
# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

from src.qdrant_vector_db import QdrantVectorDB
from src.graph_db import GraphDB
from src.vector_db import VectorDB


def init_nasa_dataset():
    """
    初始化NASA数据集的知识图谱和向量数据库
    """
    
    # 配置参数
    config = {
        "data_name": "nasa",
        "data_path": "dataset/nasa/nasa_train.csv",
        "output_path": "dataset/nasa/llm_graph_gpt3.json",
        # 注意：QdrantVectorDB使用内存模式，不需要vectdb_path
        "vectdb_path": "database/nasa",
        "template": {
            "sys": "prompts/system/nasa/llm_graph.txt",
            "user": "prompts/user/nasa/llm_graph.txt"
        },
        "query_params": {
            "l2_top_k": 11,  # NASA数据集二级分类较多，增加检索数量
            "l3_top_k": 53   # NASA数据集三级分类很多，增加检索数量
        }
    }

    print("=== 初始化NASA数据集 ===")
    print(f"数据路径: {config['data_path']}")
    
    # 读取预处理后的数据
    try:
        df = pd.read_csv(config["data_path"])
        print(f"成功读取数据: {len(df)} 条记录")
    except FileNotFoundError:
        print(f"错误: 找不到数据文件 {config['data_path']}")
        print("请先运行 preprocess_nasa.py 进行数据预处理")
        return
    
    # 数据质量检查
    print("\n=== 数据质量检查 ===")
    required_columns = ['Title', 'Text', 'Cat1', 'Cat2', 'Cat3']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"错误: 缺少必要字段 {missing_columns}")
        return
    
    # 移除空值和unknown标签
    initial_count = len(df)
    df = df.dropna(subset=required_columns)
    df = df[df['Cat1'] != "unknown"]
    df = df[df['Cat2'] != "unknown"] 
    df = df[df['Cat3'] != "unknown"]
    df = df[df['Text'].notna()]
    df = df[df['Title'].notna()]
    
    print(f"数据清洗后: {len(df)} 条记录 (移除了 {initial_count - len(df)} 条)")
    
    # 统计分类信息
    print(f"一级分类数量: {df['Cat1'].nunique()}")
    print(f"二级分类数量: {df['Cat2'].nunique()}")
    print(f"三级分类数量: {df['Cat3'].nunique()}")
    
    # 初始化数据库连接
    print("\n=== 初始化数据库连接 ===")
    try:
        graph_db = GraphDB()
        print("✓ Neo4j图数据库连接成功")
    except Exception as e:
        print(f"✗ Neo4j图数据库连接失败: {e}")
        print("请确保Neo4j服务正在运行，并检查环境变量配置")
        return
    
    try:
        # QdrantVectorDB构造函数参数：host, port, collection_name, use_memory
        # 使用内存模式，无需database_path参数
        vector_db = VectorDB(
            database_path=config["vectdb_path"],
            collection_name=config["data_name"]
        )
        print("✓ 向量数据库连接成功")
    except Exception as e:
        print(f"✗ 向量数据库连接失败: {e}")
        return
    
    # 构建知识图谱
    print("\n=== 构建知识图谱 ===")
    
    # NASA数据集使用3层层次结构
    query_create_graph = """
    MERGE (level1:Category1 {name: $l1})
    MERGE (level2:Category2 {name: $l2})
    MERGE (level3:Category3 {name: $l3})
    MERGE (level1)-[:contains]->(level2)
    MERGE (level2)-[:contains]->(level3)
    """
    
    # 统计唯一的层次路径
    unique_paths = df[['Cat1', 'Cat2', 'Cat3']].drop_duplicates()
    print(f"唯一层次路径数量: {len(unique_paths)}")
    
    # 批量创建图结构
    print("正在创建图结构...")
    created_count = 0
    error_count = 0
    
    for _, row in tqdm(unique_paths.iterrows(), total=len(unique_paths), desc="创建图节点"):
        try:
            # 标准化标签名称
            l1 = str(row['Cat1']).lower()
            l2 = str(row['Cat2']).lower()
            l3 = str(row['Cat3']).lower()
            
            graph_db.create_database(
                query_create_graph, 
                l1=l1, l2=l2, l3=l3
            )
            created_count += 1
            
        except Exception as e:
            error_count += 1
            if error_count <= 5:  # 只显示前5个错误
                print(f"创建图节点时出错: {e}")
    
    print(f"图结构创建完成: 成功 {created_count} 个，失败 {error_count} 个")
    
    # 构建向量数据库
    print("\n=== 构建向量数据库 ===")
    
    # 获取所有唯一标签
    label_l1 = [cat.lower() 
                for cat in df["Cat1"].unique().tolist() if cat is not None]
    label_l2 = [cat.lower()
                for cat in df["Cat2"].unique().tolist() if cat is not None]
    label_l3 = [cat.lower()
                for cat in df["Cat3"].unique().tolist() if cat is not None]
    
    # 移除unknown标签
    label_l1 = [label for label in label_l1 if label != "unknown"]
    label_l2 = [label for label in label_l2 if label != "unknown"]
    label_l3 = [label for label in label_l3 if label != "unknown"]
    
    print(f"L1标签数量: {len(label_l1)}")
    print(f"L2标签数量: {len(label_l2)}")
    print(f"L3标签数量: {len(label_l3)}")
    
    # 批量添加到向量数据库
    try:
        print("正在添加L1标签到向量数据库...")
        if label_l1:
            # 对于分类标签，title和text使用相同的内容
            vector_db.batch_add(
                label_l1,
                metadatas=[{"level": "Category1"}] * len(label_l1)
            )
            print(f"  ✓ 已添加 {len(label_l1)} 个L1标签")
        else:
            print("  ⚠ 没有L1标签需要添加")

        print("正在添加L2标签到向量数据库...")
        if label_l2:
            vector_db.batch_add(
                label_l2,
                metadatas=[{"level": "Category2"}] * len(label_l2)
            )
            print(f"  ✓ 已添加 {len(label_l2)} 个L2标签")
        else:
            print("  ⚠ 没有L2标签需要添加")

        print("正在添加L3标签到向量数据库...")
        if label_l3:
            vector_db.batch_add(
                label_l3,
                metadatas=[{"level": "Category3"}] * len(label_l3)
            )
            print(f"  ✓ 已添加 {len(label_l3)} 个L3标签")
        else:
            print("  ⚠ 没有L3标签需要添加")

        print("✓ 向量数据库创建成功")

    except Exception as e:
        print(f"✗ 向量数据库创建失败: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        return

if __name__ == "__main__":
    init_nasa_dataset()
