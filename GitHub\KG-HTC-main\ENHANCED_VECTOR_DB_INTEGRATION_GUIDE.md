# 增强版 Qdrant 向量数据库集成指南

## 概述

本文档介绍如何将新的 `EnhancedQdrantVectorDB` 类集成到现有的 KG-HTC 项目中。新的向量数据库类在保持向后兼容性的同时，提供了更强大的功能和更好的性能。

## 主要特性

### 🚀 核心功能
- **内存模式运行**：无需外部 Qdrant 服务器，支持本地开发和测试
- **持久化存储**：自动保存数据到本地 .db 文件，支持数据恢复
- **OpenAI 嵌入模型**：使用 `text-embedding-ada-002` 等标准嵌入模型
- **CSV 数据导入**：自动检测文本列，支持批量数据导入
- **层级标签支持**：完整支持三级分类结构（Cat1, Cat2, Cat3）

### 📊 增强功能
- **完整的 CRUD 操作**：插入、查询、更新、删除向量数据
- **相似性搜索**：支持返回指定数量的最相似结果
- **复杂查询过滤**：支持元数据过滤和相似度阈值
- **数据备份恢复**：完整的备份和恢复机制
- **统计分析**：详细的数据库统计和分析功能

### 🔄 向后兼容
- **接口兼容**：完全兼容现有的 `QdrantVectorDB` 接口
- **无缝替换**：可以直接替换现有实现，无需修改调用代码
- **数据迁移**：支持从现有数据库迁移数据

## 安装和配置

### 1. 依赖安装

确保已安装必要的依赖包：

```bash
pip install qdrant-client>=1.7.0
pip install openai>=1.0.0
pip install pandas>=1.3.0
pip install numpy>=1.21.0
```

### 2. API 配置

在使用前，请配置 OpenAI API 相关参数：

```python
# 方式1：直接在代码中配置
embedding_config = {
    "api_key": "your-openai-api-key",
    "base_url": "https://api.openai.com/v1/",  # 或其他兼容的API端点
    "model_name": "text-embedding-ada-002",
    "batch_size": 100,
    "max_retries": 3
}

# 方式2：使用环境变量
import os
os.environ["OPENAI_API_KEY"] = "your-openai-api-key"
os.environ["OPENAI_BASE_URL"] = "https://api.openai.com/v1/"
```

## 基本使用方法

### 1. 初始化向量数据库

```python
from src.enhanced_qdrant_vector_db import EnhancedQdrantVectorDB

# 基本初始化
vector_db = EnhancedQdrantVectorDB(
    collection_name="my_collection",
    use_memory=True,  # 使用内存模式
    storage_path="./vector_storage",  # 本地存储路径
    text_field="text",  # 文本字段名
    auto_persist=True,  # 自动持久化
    embedding_config={
        "model_name": "text-embedding-ada-002",
        "batch_size": 50
    }
)
```

### 2. 数据导入

#### 从 CSV 文件导入

```python
# 支持层级标签的CSV导入
result = vector_db.load_csv_data(
    csv_path="data.csv",
    text_column="Text",  # 可以自动检测
    hierarchical_columns={
        "Category1": "Cat1",
        "Category2": "Cat2", 
        "Category3": "Cat3"
    }
)
```

#### 批量添加文本

```python
texts = ["文本1", "文本2", "文本3"]
metadatas = [
    {"category": "tech", "level": "Category1"},
    {"category": "science", "level": "Category2"},
    {"category": "ai", "level": "Category3"}
]

result = vector_db.batch_add_texts(texts, metadatas)
```

### 3. 查询操作

#### 基本查询

```python
# 使用新的增强接口
result = vector_db.query(
    query_text="人工智能",
    n_results=10,
    score_threshold=0.5  # 相似度阈值
)

# 访问结果
for doc, metadata, score in zip(result.documents, result.metadatas, result.scores):
    print(f"文档: {doc}")
    print(f"元数据: {metadata}")
    print(f"相似度: {score}")
```

#### 层级查询

```python
# 查询特定层级
l1_result = vector_db.query_hierarchical("查询文本", "Category1", n_results=5)
l2_result = vector_db.query_hierarchical("查询文本", "Category2", n_results=5)
l3_result = vector_db.query_hierarchical("查询文本", "Category3", n_results=5)
```

#### 复杂过滤查询

```python
# 使用元数据过滤
result = vector_db.query(
    query_text="机器学习",
    n_results=10,
    where={"category": "tech", "difficulty": "advanced"},
    score_threshold=0.3
)
```

## 与现有代码集成

### 1. Pipeline 类集成

修改 `src/pipeline.py` 中的向量数据库初始化：

```python
# 原有代码
from src.qdrant_vector_db import QdrantVectorDB

# 替换为
from src.enhanced_qdrant_vector_db import EnhancedQdrantVectorDB as QdrantVectorDB

# 其余代码保持不变，完全兼容
self._vector_db = QdrantVectorDB(
    host=self._config.get("qdrant_host"),
    port=self._config.get("qdrant_port", 6333),
    collection_name=self._config.get("data_name", "default"),
    use_memory=self._config.get("qdrant_use_memory", True)
)
```

### 2. 配置文件更新

在配置文件中添加嵌入模型配置：

```python
config = {
    "data_name": "nasa_topics",
    "qdrant_host": None,  # 使用内存模式
    "qdrant_port": 6333,
    "qdrant_use_memory": True,
    
    # 新增：嵌入模型配置
    "embedding_config": {
        "model_name": "text-embedding-ada-002",
        "batch_size": 100,
        "max_retries": 3
    },
    
    "template": {
        "sys": "prompts/system/nasa/llm_graph.txt",
        "user": "prompts/user/nasa/llm_graph.txt"
    },
    "query_params": {
        "l2_top_k": 15,
        "l3_top_k": 50
    }
}
```

## 数据迁移

### 从现有 ChromaDB 迁移

```python
# 1. 导出现有数据
old_vector_db = VectorDB("old_db_path", "collection_name")
old_data = old_vector_db.get_collection_info()

# 2. 创建新的向量数据库
new_vector_db = EnhancedQdrantVectorDB("new_collection")

# 3. 迁移数据（需要根据具体数据结构调整）
# 这里需要根据实际的数据格式进行迁移
```

### 从现有 QdrantVectorDB 迁移

```python
# 直接替换类即可，数据格式兼容
# 原有的持久化数据会自动加载
```

## 高级功能

### 1. 数据备份和恢复

```python
# 创建备份
backup_path = vector_db.backup_data()

# 恢复数据
success = vector_db.restore_data(backup_path)
```

### 2. 数据导出

```python
# 导出到CSV
csv_path = vector_db.export_to_csv(include_vectors=False)

# 导出包含向量的数据
csv_with_vectors = vector_db.export_to_csv(include_vectors=True)
```

### 3. 相似文档搜索

```python
# 查找与指定文档相似的其他文档
similar_docs = vector_db.search_similar_documents(
    document_id="doc_id",
    n_results=5,
    exclude_self=True
)
```

### 4. 统计分析

```python
# 获取详细统计信息
stats = vector_db.get_statistics()
print(f"总文档数: {stats['basic_info']['total_documents']}")
print(f"向量维度: {stats['basic_info']['vector_dimension']}")
print(f"层级分布: {stats.get('hierarchical_info', {})}")
```

## 性能优化建议

### 1. 批处理大小调整

```python
# 根据内存和网络情况调整批处理大小
embedding_config = {
    "batch_size": 50,  # 较小的批次适合网络较慢的环境
    # "batch_size": 200,  # 较大的批次适合高性能环境
}
```

### 2. 内存使用优化

```python
# 对于大量数据，考虑关闭自动持久化，手动控制
vector_db = EnhancedQdrantVectorDB(
    auto_persist=False,  # 关闭自动持久化
    # 手动保存
    # vector_db._save_data_cache()
)
```

### 3. 查询性能优化

```python
# 使用相似度阈值过滤低质量结果
result = vector_db.query(
    query_text="查询文本",
    score_threshold=0.5,  # 只返回相似度大于0.5的结果
    n_results=10
)
```

## 故障排除

### 常见问题

1. **API 连接失败**
   - 检查 API 密钥是否正确
   - 确认网络连接正常
   - 验证 API 端点 URL

2. **内存不足**
   - 减少批处理大小
   - 关闭自动持久化
   - 使用外部 Qdrant 服务器

3. **数据加载失败**
   - 检查 CSV 文件编码
   - 确认文本列名正确
   - 验证数据格式

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 这将输出详细的调试信息
```

## 总结

增强版 `EnhancedQdrantVectorDB` 类提供了更强大、更灵活的向量数据库功能，同时保持了与现有代码的完全兼容性。通过本指南，您可以轻松地将其集成到现有项目中，并利用其增强功能提升系统性能。
