#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新设计的 QdrantVectorDB 类测试和使用示例

本文件包含：
1. 基本功能测试
2. CSV数据加载测试
3. 持久化功能测试
4. 兼容性测试
5. 使用示例

作者：AI Assistant
日期：2025-01-28
"""

import os
import sys
import pandas as pd
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.qdrant_vector_db import QdrantVectorDB
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_csv(file_path: str, num_rows: int = 10):
    """
    创建测试用的CSV文件
    
    Args:
        file_path: CSV文件路径
        num_rows: 数据行数
    """
    data = {
        'Title': [f'测试标题 {i}' for i in range(num_rows)],
        'Text': [f'这是第 {i} 条测试文本内容，用于验证向量化功能。包含一些中文和英文内容 test content {i}.' for i in range(num_rows)],
        'Cat1': ['earth_science'] * (num_rows // 2) + ['computer_science'] * (num_rows - num_rows // 2),
        'Cat2': ['atmosphere', 'land_surface'] * (num_rows // 2) + ['artificial_intelligence'] * (num_rows - num_rows // 2),
        'Cat3': ['aerosols', 'land_use'] * (num_rows // 2) + ['machine_learning'] * (num_rows - num_rows // 2)
    }
    
    df = pd.DataFrame(data)
    df.to_csv(file_path, index=False, encoding='utf-8')
    logger.info(f"创建测试CSV文件: {file_path}, 包含 {num_rows} 行数据")

def test_basic_functionality():
    """测试基本功能"""
    logger.info("=== 测试基本功能 ===")
    
    # 创建临时存储目录
    with tempfile.TemporaryDirectory() as temp_dir:
        # 初始化向量数据库
        vector_db = QdrantVectorDB(
            collection_name="test_basic",
            storage_path=temp_dir,
            use_memory=True,
            auto_persist=True
        )
        
        # 测试添加单个文本
        logger.info("测试添加单个文本...")
        result = vector_db.add_text(
            text="这是一个测试文本，用于验证向量数据库的基本功能。",
            metadata={"category": "test", "level": "Category1"}
        )
        assert result['success'], f"添加文本失败: {result}"
        logger.info("✓ 单个文本添加成功")
        
        # 测试批量添加（向后兼容接口）
        logger.info("测试批量添加...")
        titles = ["标题1", "标题2", "标题3"]
        texts = ["文本内容1", "文本内容2", "文本内容3"]
        metadatas = [
            {"level": "Category1", "type": "test"},
            {"level": "Category2", "type": "test"},
            {"level": "Category3", "type": "test"}
        ]
        
        vector_db.batch_add(titles, texts, metadatas)
        logger.info("✓ 批量添加成功")
        
        # 测试查询功能
        logger.info("测试查询功能...")
        query_result = vector_db.query("测试文本", n_results=5)
        assert len(query_result['documents'][0]) > 0, "查询结果为空"
        logger.info(f"✓ 查询成功，返回 {len(query_result['documents'][0])} 个结果")
        
        # 测试分级查询（向后兼容）
        logger.info("测试分级查询...")
        l1_result = vector_db.query_l1("测试", n_results=3)
        l2_result = vector_db.query_l2("测试", n_results=3)
        l3_result = vector_db.query_l3("测试", n_results=3)
        
        logger.info(f"✓ L1查询: {len(l1_result['documents'][0])} 个结果")
        logger.info(f"✓ L2查询: {len(l2_result['documents'][0])} 个结果")
        logger.info(f"✓ L3查询: {len(l3_result['documents'][0])} 个结果")
        
        # 测试获取集合信息
        logger.info("测试获取集合信息...")
        info = vector_db.get_collection_info()
        logger.info(f"✓ 集合信息: 主集合={info['main_collection']['count']} 条数据")
        
        logger.info("✓ 基本功能测试完成")

def test_csv_loading():
    """测试CSV数据加载功能"""
    logger.info("=== 测试CSV数据加载功能 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建测试CSV文件
        csv_path = os.path.join(temp_dir, "test_data.csv")
        create_test_csv(csv_path, num_rows=20)
        
        # 初始化向量数据库
        vector_db = QdrantVectorDB(
            collection_name="test_csv",
            storage_path=temp_dir,
            use_memory=True,
            auto_persist=True
        )
        
        # 加载CSV数据
        logger.info("加载CSV数据...")
        result = vector_db.load_csv_data(csv_path, text_column="Text")
        
        assert result['success'], f"CSV加载失败: {result}"
        logger.info(f"✓ CSV加载成功: {result['processed_rows']} 行数据")
        logger.info(f"  文本列: {result['text_column']}")
        logger.info(f"  所有列: {result['columns']}")
        
        # 验证数据是否正确加载
        info = vector_db.get_collection_info()
        assert info['main_collection']['count'] == result['processed_rows'], "数据数量不匹配"
        
        # 测试查询加载的数据
        logger.info("测试查询加载的数据...")
        query_result = vector_db.query("测试文本", n_results=5)
        assert len(query_result['documents'][0]) > 0, "查询结果为空"
        
        # 检查元数据
        metadata = query_result['metadatas'][0][0]
        assert 'Title' in metadata, "元数据中缺少Title字段"
        assert 'Cat1' in metadata, "元数据中缺少Cat1字段"
        
        logger.info("✓ CSV数据加载测试完成")

def test_persistence():
    """测试持久化功能"""
    logger.info("=== 测试持久化功能 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        storage_path = os.path.join(temp_dir, "persistence_test")
        
        # 第一阶段：创建数据并保存
        logger.info("第一阶段：创建数据...")
        vector_db1 = QdrantVectorDB(
            collection_name="test_persist",
            storage_path=storage_path,
            use_memory=True,
            auto_persist=True
        )
        
        # 添加一些测试数据
        test_data = [
            {"text": "持久化测试文本1", "category": "test", "level": "Category1"},
            {"text": "持久化测试文本2", "category": "test", "level": "Category2"},
            {"text": "持久化测试文本3", "category": "test", "level": "Category3"}
        ]
        
        result = vector_db1.incremental_update(test_data)
        assert result['success'], f"增量更新失败: {result}"
        logger.info(f"✓ 添加了 {result['added_count']} 条数据")
        
        # 获取统计信息
        stats1 = vector_db1.get_statistics()
        logger.info(f"第一阶段统计: {stats1['total_documents']} 条文档")
        
        # 第二阶段：重新初始化并验证数据恢复
        logger.info("第二阶段：重新初始化...")
        vector_db2 = QdrantVectorDB(
            collection_name="test_persist",
            storage_path=storage_path,
            use_memory=True,
            auto_persist=True
        )
        
        # 验证数据是否恢复
        stats2 = vector_db2.get_statistics()
        logger.info(f"第二阶段统计: {stats2['total_documents']} 条文档")
        
        # 数据应该相等
        assert stats1['total_documents'] == stats2['total_documents'], "持久化数据恢复失败"
        
        # 测试查询恢复的数据
        query_result = vector_db2.query("持久化测试", n_results=5)
        assert len(query_result['documents'][0]) >= 3, "恢复的数据查询失败"
        
        # 测试备份功能
        logger.info("测试备份功能...")
        backup_path = vector_db2.backup_data()
        assert os.path.exists(backup_path), "备份文件创建失败"
        logger.info(f"✓ 备份文件创建: {backup_path}")
        
        logger.info("✓ 持久化功能测试完成")

def test_compatibility():
    """测试向后兼容性"""
    logger.info("=== 测试向后兼容性 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 使用旧的接口方式初始化
        vector_db = QdrantVectorDB(
            collection_name="test_compat",
            use_memory=True
        )
        
        # 测试旧的batch_add接口
        logger.info("测试旧的batch_add接口...")
        titles = ["兼容性测试标题1", "兼容性测试标题2"]
        texts = ["兼容性测试文本1", "兼容性测试文本2"]
        metadatas = [{"level": "Category1"}, {"level": "Category2"}]
        
        vector_db.batch_add(titles, texts, metadatas)
        
        # 测试旧的查询接口
        logger.info("测试旧的查询接口...")
        title_result = vector_db.query_by_title("兼容性测试")
        text_result = vector_db.query_by_text("兼容性测试")
        
        assert len(title_result['documents'][0]) > 0, "title查询失败"
        assert len(text_result['documents'][0]) > 0, "text查询失败"
        
        # 测试分级查询
        l1_result = vector_db.query_l1("测试")
        l2_result = vector_db.query_l2("测试")
        
        logger.info(f"✓ 兼容性测试完成")

def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行所有测试...")
    
    try:
        test_basic_functionality()
        test_csv_loading()
        test_persistence()
        test_compatibility()
        
        logger.info("🎉 所有测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
