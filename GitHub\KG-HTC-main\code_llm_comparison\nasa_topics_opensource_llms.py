import pandas as pd
from pathlib import Path
from tqdm import tqdm
import sys
import json
import time
import requests

# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

from src.graph_db import GraphDB
from src.pipeline import Pipeline

class OpenSourceLLM:
    """开源LLM接口类，支持Ollama本地部署"""
    
    def __init__(self, model_name: str, base_url: str = "http://localhost:11434"):
        self.model_name = model_name
        self.base_url = base_url
        
    def generate(self, prompt: str, max_tokens: int = 1024, temperature: float = 0.4):
        """调用Ollama API生成文本"""
        try:
            response = requests.post(
                f"{self.base_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": temperature,
                        "num_predict": max_tokens
                    }
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("response", "").strip()
            else:
                print(f"API调用失败: {response.status_code}")
                return "error"
                
        except Exception as e:
            print(f"LLM调用出错: {e}")
            return "error"

def run_opensource_llm_comparison():
    """
    运行开源LLM对比实验
    """
    
    # 配置不同的开源模型
    models_config = {
        "qwen2": {
            "model_name": "qwen2:7b",
            "description": "Qwen2 7B - 阿里巴巴开源模型"
        },
        "llama3": {
            "model_name": "llama3:8b", 
            "description": "Llama3 8B - Meta开源模型"
        },
        "mistral": {
            "model_name": "mistral:7b",
            "description": "Mistral 7B - Mistral AI开源模型"
        },
        "gemma": {
            "model_name": "gemma:7b",
            "description": "Gemma 7B - Google开源模型"
        }
    }
    
    print("=== NASA Topics开源LLM对比实验 ===")
    print("支持的模型:")
    for key, config in models_config.items():
        print(f"  - {key}: {config['description']}")
    
    # 检查Ollama服务
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            available_models = [model['name'] for model in response.json().get('models', [])]
            print(f"\n可用模型: {available_models}")
        else:
            print("⚠ 无法连接到Ollama服务，请确保Ollama正在运行")
            return
    except Exception as e:
        print(f"⚠ Ollama连接检查失败: {e}")
        print("请确保Ollama服务正在运行: ollama serve")
        return
    
    # 读取数据
    try:
        df = pd.read_csv("dataset/nasa_topics/nasa_topics_val.csv")
        print(f"\n成功读取数据: {len(df)} 条记录")
    except Exception as e:
        print(f"读取数据失败: {e}")
        return
    
    # 数据预处理
    df = df.dropna(subset=['Title', 'Text', 'Cat1', 'Cat2', 'Cat3'])
    df = df[df['Cat1'] != "unknown"]
    df = df[df['Cat2'] != "unknown"]
    df = df[df['Cat3'] != "unknown"]
    
    # 限制实验数据量
    max_samples = 100  # 开源模型推理较慢，减少样本数
    if len(df) > max_samples:
        df = df.sample(n=max_samples, random_state=42)
    
    ds = df.to_dict(orient="records")
    print(f"实验数据量: {len(ds)} 条记录")
    
    # 初始化图数据库
    try:
        graph_db = GraphDB()
        print("✓ 图数据库连接成功")
    except Exception as e:
        print(f"✗ 图数据库连接失败: {e}")
        return
    
    # 准备分类选项
    all_l1 = df['Cat1'].unique().tolist()
    all_l2 = df['Cat2'].unique().tolist()
    all_l3 = df['Cat3'].unique().tolist()
    
    # 对每个模型进行实验
    all_results = {}
    
    for model_key, model_config in models_config.items():
        print(f"\n=== 测试 {model_config['description']} ===")
        
        # 检查模型是否可用
        model_name = model_config['model_name']
        if model_name not in available_models:
            print(f"⚠ 模型 {model_name} 不可用，跳过")
            print(f"请运行: ollama pull {model_name}")
            continue
        
        # 初始化模型
        llm = OpenSourceLLM(model_name)
        
        # 测试模型连接
        test_response = llm.generate("Hello", max_tokens=10)
        if test_response == "error":
            print(f"✗ 模型 {model_name} 测试失败，跳过")
            continue
        else:
            print(f"✓ 模型 {model_name} 测试成功")
        
        # 开始分类实验
        results = []
        success_count = 0
        error_count = 0
        total_time = 0
        
        for idx in tqdm(range(len(ds)), desc=f"{model_key}分类"):
            data = ds[idx].copy()
            
            try:
                start_time = time.time()
                
                # 构建查询文本
                title = data.get('Title', '').strip()
                text = data.get('Text', '').strip()
                query_text = f"Title: {title}\nDescription: {text[:1500]}"  # 限制长度
                
                # L1级别预测
                l1_prompt = f"""Classify this NASA Earth science dataset into one of these categories: {', '.join(all_l1)}.

Dataset:
{query_text}

Category:"""
                
                pred_l1 = llm.generate(l1_prompt, max_tokens=50, temperature=0.3)
                pred_l1 = pred_l1.strip().lower()
                
                # L2级别预测
                l2_prompt = f"""Classify this NASA Earth science dataset into one of these topic categories: {', '.join(all_l2[:10])}.

Dataset:
{query_text}

Topic Category:"""
                
                pred_l2 = llm.generate(l2_prompt, max_tokens=50, temperature=0.3)
                pred_l2 = pred_l2.strip().lower()
                
                # L3级别预测
                l3_prompt = f"""Classify this NASA Earth science dataset into one of these specific terms: {', '.join(all_l3[:15])}.

Dataset:
{query_text}

Specific Term:"""
                
                pred_l3 = llm.generate(l3_prompt, max_tokens=50, temperature=0.3)
                pred_l3 = pred_l3.strip().lower()
                
                inference_time = time.time() - start_time
                total_time += inference_time
                
                # 保存结果
                data[f"{model_key}_pred_l1"] = pred_l1
                data[f"{model_key}_pred_l2"] = pred_l2
                data[f"{model_key}_pred_l3"] = pred_l3
                data["method"] = model_key
                data["model_name"] = model_name
                data["inference_time"] = inference_time
                
                results.append(data)
                success_count += 1
                
            except Exception as e:
                error_count += 1
                print(f"\n处理第 {idx+1} 条记录时出错: {e}")
                
                data[f"{model_key}_pred_l1"] = "error"
                data[f"{model_key}_pred_l2"] = "error"
                data[f"{model_key}_pred_l3"] = "error"
                data["error_message"] = str(e)
                data["method"] = model_key
                data["model_name"] = model_name
                results.append(data)
        
        # 保存单个模型结果
        output_path = f"dataset/nasa_topics/{model_key}_results.json"
        try:
            with open(output_path, "w", encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"✓ {model_key}结果已保存: {output_path}")
        except Exception as e:
            print(f"✗ 保存{model_key}结果失败: {e}")
        
        # 计算性能指标
        if success_count > 0:
            valid_results = [r for r in results if r[f"{model_key}_pred_l1"] != 'error']
            
            if valid_results:
                correct_l1 = sum(1 for r in valid_results 
                               if r[f"{model_key}_pred_l1"].lower() == r['Cat1'].lower())
                correct_l2 = sum(1 for r in valid_results 
                               if r[f"{model_key}_pred_l2"].lower() == r['Cat2'].lower())
                correct_l3 = sum(1 for r in valid_results 
                               if r[f"{model_key}_pred_l3"].lower() == r['Cat3'].lower())
                
                acc_l1 = correct_l1 / len(valid_results)
                acc_l2 = correct_l2 / len(valid_results)
                acc_l3 = correct_l3 / len(valid_results)
                
                avg_time = total_time / len(valid_results)
                
                print(f"\n{model_key} 性能:")
                print(f"  成功率: {success_count/len(results)*100:.2f}%")
                print(f"  L1准确率: {acc_l1:.4f}")
                print(f"  L2准确率: {acc_l2:.4f}")
                print(f"  L3准确率: {acc_l3:.4f}")
                print(f"  平均推理时间: {avg_time:.2f}秒")
                
                all_results[model_key] = {
                    'model_name': model_name,
                    'description': model_config['description'],
                    'success_rate': success_count / len(results),
                    'accuracy': {
                        'L1': acc_l1,
                        'L2': acc_l2,
                        'L3': acc_l3
                    },
                    'average_inference_time': avg_time,
                    'total_samples': len(results),
                    'valid_samples': len(valid_results)
                }
    
    # 保存对比结果
    if all_results:
        print(f"\n=== 开源LLM性能对比 ===")
        for model_key, result in all_results.items():
            print(f"\n{result['description']}:")
            print(f"  L1: {result['accuracy']['L1']:.4f}")
            print(f"  L2: {result['accuracy']['L2']:.4f}")
            print(f"  L3: {result['accuracy']['L3']:.4f}")
            print(f"  推理时间: {result['average_inference_time']:.2f}秒")
        
        with open("dataset/nasa_topics/opensource_llm_comparison.json", 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n✓ 开源LLM对比结果已保存: opensource_llm_comparison.json")

if __name__ == "__main__":
    run_opensource_llm_comparison()
