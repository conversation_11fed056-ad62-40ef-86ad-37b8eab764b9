#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试修复后的代码

此脚本用于快速验证修复后的Pipeline和GraphDB是否能正确处理空结果
"""

import sys
from pathlib import Path

# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

def test_graph_db_fix():
    """测试GraphDB修复效果"""
    print("=== 测试GraphDB修复效果 ===")
    
    try:
        from src.graph_db import GraphDB
        graph_db = GraphDB()
        print("✓ GraphDB连接成功")
        
        # 测试不存在的标签
        print("\n测试不存在的L3标签查询:")
        result = graph_db.query_l2_from_l3("nonexistent_label")
        print(f"结果: {result}")
        
        print("\n测试不存在的L2标签查询:")
        result = graph_db.query_l1_from_l2("nonexistent_label")
        print(f"结果: {result}")
        
        print("✓ GraphDB测试通过")
        return True
        
    except Exception as e:
        print(f"✗ GraphDB测试失败: {e}")
        return False

def test_pipeline_fix():
    """测试Pipeline修复效果"""
    print("\n=== 测试Pipeline修复效果 ===")
    
    try:
        from src.llm import LLM
        from src.pipeline import Pipeline
        
        # 简化配置
        config = {
            "data_name": "nasa",
            "template": {
                "sys": "prompts/system/nasa/llm_graph.txt",
                "user": "prompts/user/nasa/llm_graph.txt"
            },
            "query_params": {
                "l2_top_k": 5,
                "l3_top_k": 5
            }
        }
        
        llm = LLM()
        pipeline = Pipeline(llm, config)
        print("✓ Pipeline初始化成功")
        
        # 测试向量查询
        print("\n测试向量查询:")
        result = pipeline.query_related_nodes("test query")
        print(f"L2结果数量: {len(result.get('l2', []))}")
        print(f"L3结果数量: {len(result.get('l3', []))}")
        
        # 测试子图构建
        print("\n测试子图构建:")
        l3_nodes = result.get('l3', [])[:2]  # 取前2个进行测试
        l2_nodes = result.get('l2', [])[:2]
        
        sub_graph = pipeline.build_linked_labels(l3_nodes, l2_nodes)
        print(f"子图大小: {len(sub_graph)}")
        print(f"子图内容: {sub_graph}")
        
        print("✓ Pipeline测试通过")
        return True
        
    except Exception as e:
        print(f"✗ Pipeline测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_build_linked_labels_edge_cases():
    """测试build_linked_labels的边界情况"""
    print("\n=== 测试build_linked_labels边界情况 ===")
    
    try:
        from src.llm import LLM
        from src.pipeline import Pipeline
        
        config = {
            "data_name": "nasa",
            "template": {
                "sys": "prompts/system/nasa/llm_graph.txt",
                "user": "prompts/user/nasa/llm_graph.txt"
            },
            "query_params": {
                "l2_top_k": 5,
                "l3_top_k": 5
            }
        }
        
        llm = LLM()
        pipeline = Pipeline(llm, config)
        
        # 测试空输入
        print("测试空L3节点列表:")
        result = pipeline.build_linked_labels([], ["test_l2"])
        print(f"结果: {result}")
        
        print("测试None输入:")
        result = pipeline.build_linked_labels(None, ["test_l2"])
        print(f"结果: {result}")
        
        print("测试不存在的L3节点:")
        result = pipeline.build_linked_labels(["nonexistent_l3"], ["test_l2"])
        print(f"结果: {result}")
        
        print("✓ 边界情况测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 边界情况测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 开始快速修复验证测试...\n")
    
    # 测试GraphDB修复
    graph_ok = test_graph_db_fix()
    
    # 测试Pipeline修复
    pipeline_ok = test_pipeline_fix()
    
    # 测试边界情况
    edge_case_ok = test_build_linked_labels_edge_cases()
    
    # 总结
    print(f"\n=== 测试总结 ===")
    print(f"GraphDB修复: {'✓ 通过' if graph_ok else '✗ 失败'}")
    print(f"Pipeline修复: {'✓ 通过' if pipeline_ok else '✗ 失败'}")
    print(f"边界情况处理: {'✓ 通过' if edge_case_ok else '✗ 失败'}")
    
    if graph_ok and pipeline_ok and edge_case_ok:
        print("\n🎉 所有修复验证通过！现在可以安全运行 gpt_nasa.py")
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        
        if not graph_ok:
            print("  - 检查Neo4j数据库连接和配置")
        if not pipeline_ok:
            print("  - 检查向量数据库初始化")
        if not edge_case_ok:
            print("  - 检查代码逻辑错误")
