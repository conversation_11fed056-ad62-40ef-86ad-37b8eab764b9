# 测试模型列表
Invoke-RestMethod -Uri "http://10.53.2.36:11434/api/tags" -Method Get

# 测试原生API
$body = '{"model": "deepseek-r1:1.5b", "prompt": "你好", "stream": false, "options": {"num_predict": 10}}'
Invoke-RestMethod -Uri "http://10.53.2.36:11434/api/generate" -Method Post -Body $body -ContentType "application/json"

# 测试OpenAI兼容接口
$openaiBody = '{"model": "deepseek-r1:1.5b", "messages": [{"role": "user", "content": "你好"}], "max_tokens": 10}'
Invoke-RestMethod -Uri "http://10.53.2.36:11434/v1/chat/completions" -Method Post -Body $openaiBody -ContentType "application/json"