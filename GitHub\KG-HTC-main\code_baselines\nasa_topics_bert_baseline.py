import pandas as pd
import numpy as np
from pathlib import Path
import sys
import json
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification, Trainer, TrainingArguments
from transformers import pipeline
from sklearn.metrics import accuracy_score, f1_score
from sklearn.preprocessing import LabelEncoder
import time

# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

def run_bert_baseline():
    """
    运行BERT基线方法 (使用预训练模型进行零样本分类)
    """
    
    print("=== NASA Topics BERT基线实验 ===")
    print("使用预训练BERT模型进行零样本分类")
    
    # 读取数据
    test_path = "dataset/nasa_topics/nasa_topics_val.csv"
    
    try:
        test_df = pd.read_csv(test_path)
        print(f"测试集: {len(test_df)} 条记录")
    except Exception as e:
        print(f"读取数据失败: {e}")
        return
    
    # 数据预处理
    test_df = test_df.dropna(subset=['Text', 'Cat1', 'Cat2', 'Cat3'])
    test_df = test_df[test_df['Cat1'] != "unknown"]
    test_df = test_df[test_df['Cat2'] != "unknown"]
    test_df = test_df[test_df['Cat3'] != "unknown"]
    
    # 限制数据量进行快速实验
    if len(test_df) > 200:
        test_df = test_df.sample(n=200, random_state=42)
    
    print(f"实验数据: {len(test_df)} 条记录")
    
    # 准备分类标签
    all_l1 = test_df['Cat1'].unique().tolist()
    all_l2 = test_df['Cat2'].unique().tolist()
    all_l3 = test_df['Cat3'].unique().tolist()
    
    print(f"分类数量: L1={len(all_l1)}, L2={len(all_l2)}, L3={len(all_l3)}")
    
    # 初始化零样本分类器
    try:
        print("\n=== 初始化BERT零样本分类器 ===")
        classifier = pipeline(
            "zero-shot-classification",
            model="facebook/bart-large-mnli",
            device=0 if torch.cuda.is_available() else -1
        )
        print("✓ BERT分类器初始化成功")
    except Exception as e:
        print(f"✗ BERT分类器初始化失败: {e}")
        print("尝试使用CPU版本...")
        try:
            classifier = pipeline(
                "zero-shot-classification",
                model="facebook/bart-large-mnli",
                device=-1
            )
            print("✓ BERT分类器(CPU)初始化成功")
        except Exception as e2:
            print(f"✗ BERT分类器初始化完全失败: {e2}")
            return
    
    # 开始分类实验
    print("\n=== 开始BERT零样本分类 ===")
    results = []
    success_count = 0
    error_count = 0
    
    for idx, row in test_df.iterrows():
        try:
            # 准备文本
            text = row['Text'][:512]  # 限制长度
            
            start_time = time.time()
            
            # L1分类
            try:
                l1_result = classifier(text, all_l1)
                pred_l1 = l1_result['labels'][0].lower()
            except:
                pred_l1 = "error"
            
            # L2分类
            try:
                l2_result = classifier(text, all_l2)
                pred_l2 = l2_result['labels'][0].lower()
            except:
                pred_l2 = "error"
            
            # L3分类 (限制候选数量)
            try:
                l3_candidates = all_l3[:20]  # 限制候选数量避免超时
                l3_result = classifier(text, l3_candidates)
                pred_l3 = l3_result['labels'][0].lower()
            except:
                pred_l3 = "error"
            
            inference_time = time.time() - start_time
            
            # 保存结果
            result = {
                'Title': row['Title'],
                'Text': row['Text'],
                'Cat1': row['Cat1'],
                'Cat2': row['Cat2'],
                'Cat3': row['Cat3'],
                'bert_pred_l1': pred_l1,
                'bert_pred_l2': pred_l2,
                'bert_pred_l3': pred_l3,
                'method': 'bert_baseline',
                'inference_time': inference_time
            }
            
            results.append(result)
            success_count += 1
            
            if (idx + 1) % 10 == 0:
                print(f"已处理 {idx + 1}/{len(test_df)} 条记录")
                print(f"最新结果: L1={pred_l1}, L2={pred_l2}, L3={pred_l3}")
                print(f"推理时间: {inference_time:.2f}秒")
            
        except Exception as e:
            error_count += 1
            print(f"\n处理第 {idx+1} 条记录时出错: {e}")
            
            result = {
                'Title': row['Title'],
                'Text': row['Text'],
                'Cat1': row['Cat1'],
                'Cat2': row['Cat2'],
                'Cat3': row['Cat3'],
                'bert_pred_l1': "error",
                'bert_pred_l2': "error",
                'bert_pred_l3': "error",
                'method': 'bert_baseline',
                'error_message': str(e)
            }
            results.append(result)
    
    # 保存结果
    try:
        output_path = "dataset/nasa_topics/bert_baseline_results.json"
        with open(output_path, "w", encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"✓ BERT基线结果已保存: {output_path}")
    except Exception as e:
        print(f"✗ 保存结果失败: {e}")
    
    # 计算性能指标
    print(f"\n=== BERT基线性能评估 ===")
    print(f"成功: {success_count}, 失败: {error_count}")
    print(f"成功率: {success_count/len(results)*100:.2f}%")
    
    if success_count > 0:
        valid_results = [r for r in results if r['bert_pred_l1'] != 'error']
        
        if valid_results:
            # 计算准确率
            correct_l1 = sum(1 for r in valid_results 
                           if r['bert_pred_l1'].lower() == r['Cat1'].lower())
            correct_l2 = sum(1 for r in valid_results 
                           if r['bert_pred_l2'].lower() == r['Cat2'].lower())
            correct_l3 = sum(1 for r in valid_results 
                           if r['bert_pred_l3'].lower() == r['Cat3'].lower())
            
            acc_l1 = correct_l1 / len(valid_results)
            acc_l2 = correct_l2 / len(valid_results)
            acc_l3 = correct_l3 / len(valid_results)
            
            # 层次化准确率
            hierarchical_correct = sum(1 for r in valid_results 
                                     if (r['bert_pred_l1'].lower() == r['Cat1'].lower() and
                                         r['bert_pred_l2'].lower() == r['Cat2'].lower() and
                                         r['bert_pred_l3'].lower() == r['Cat3'].lower()))
            hierarchical_acc = hierarchical_correct / len(valid_results)
            
            print(f"\n准确率:")
            print(f"  L1: {acc_l1:.4f} ({correct_l1}/{len(valid_results)})")
            print(f"  L2: {acc_l2:.4f} ({correct_l2}/{len(valid_results)})")
            print(f"  L3: {acc_l3:.4f} ({correct_l3}/{len(valid_results)})")
            print(f"  层次化: {hierarchical_acc:.4f} ({hierarchical_correct}/{len(valid_results)})")
            
            # 平均推理时间
            avg_time = sum(r.get('inference_time', 0) for r in valid_results) / len(valid_results)
            print(f"  平均推理时间: {avg_time:.2f}秒")
            
            # 保存性能摘要
            performance_summary = {
                'method': 'BERT Baseline',
                'total_samples': len(results),
                'valid_samples': len(valid_results),
                'success_rate': success_count / len(results),
                'accuracy': {
                    'L1': acc_l1,
                    'L2': acc_l2,
                    'L3': acc_l3,
                    'Hierarchical': hierarchical_acc
                },
                'average_inference_time': avg_time
            }
            
            with open("dataset/nasa_topics/bert_baseline_summary.json", 'w', encoding='utf-8') as f:
                json.dump(performance_summary, f, indent=2, ensure_ascii=False)
            
            print("✓ 性能摘要已保存: bert_baseline_summary.json")

if __name__ == "__main__":
    run_bert_baseline()
