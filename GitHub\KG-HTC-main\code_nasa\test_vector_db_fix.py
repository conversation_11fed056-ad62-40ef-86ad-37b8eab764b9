#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修正后的vector_db.py中的l1、l2、l3查询功能
验证从元数据中正确获取分类标签的功能
"""

import pandas as pd
import sys
from pathlib import Path
import logging

# 禁用详细日志
logging.getLogger("httpx").setLevel(logging.ERROR)
logging.getLogger('urllib3').setLevel(logging.ERROR)
logging.getLogger('backoff').setLevel(logging.ERROR)

# 添加根目录到Python路径
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

from src.vector_db import VectorDB
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_vector_db_queries():
    """
    测试修正后的向量数据库查询功能
    """
    print("="*80)
    print("🧪 测试修正后的VectorDB查询功能")
    print("="*80)
    
    # 配置参数
    config = {
        "data_name": "nasa-test-fix",
        "data_path": "dataset/nasa/nasa_train.csv",
        "vectdb_path": "database/nasa-test-fix"
    }
    
    # 读取测试数据
    print("\n📂 读取测试数据...")
    try:
        df = pd.read_csv(config["data_path"])
        print(f"✓ 成功读取数据: {len(df)} 条记录")
        
        # 限制测试数据量
        if len(df) > 50:
            df = df.sample(n=50, random_state=42)
            print(f"📊 限制测试数据量: {len(df)} 条记录")
            
    except FileNotFoundError:
        print(f"✗ 找不到数据文件: {config['data_path']}")
        return
    
    # 数据清洗
    required_columns = ['Title', 'Text', 'Cat1', 'Cat2', 'Cat3']
    df = df.dropna(subset=required_columns)
    df = df[df['Cat1'] != "unknown"]
    df = df[df['Cat2'] != "unknown"] 
    df = df[df['Cat3'] != "unknown"]
    
    print(f"📋 清洗后数据量: {len(df)} 条记录")
    
    # 初始化向量数据库
    print("\n🔧 初始化向量数据库...")
    try:
        vector_db = VectorDB(
            database_path=config["vectdb_path"],
            collection_name=config["data_name"],
            embedding_type="openai"
        )
        print("✓ 向量数据库初始化成功")
    except Exception as e:
        print(f"✗ 向量数据库初始化失败: {e}")
        return
    
    # 准备测试数据
    print("\n📝 准备向量化数据...")
    texts = []
    metadatas = []
    
    for idx, row in df.iterrows():
        text_content = f"{row['Title']} {row['Text']}"
        texts.append(text_content)
        
        metadata = {
            'Cat1': str(row['Cat1']).lower(),
            'Cat2': str(row['Cat2']).lower(),
            'Cat3': str(row['Cat3']).lower(),
            'Title': str(row['Title']),
            'level': 'document',
            'doc_id': str(idx)
        }
        metadatas.append(metadata)
    
    # 添加到向量数据库
    print("🚀 向量化并添加数据...")
    try:
        vector_db.batch_add(texts=texts, metadatas=metadatas)
        print(f"✓ 成功添加 {len(texts)} 条向量化数据")
    except Exception as e:
        print(f"✗ 数据添加失败: {e}")
        return
    
    # 测试查询功能
    print("\n" + "="*60)
    print("🔍 测试修正后的查询功能")
    print("="*60)
    
    test_query = "spacecraft navigation system"
    print(f"\n🎯 测试查询: '{test_query}'")
    
    # 测试一级分类查询
    print("\n📊 测试一级分类查询 (query_l1):")
    try:
        l1_results = vector_db.query_l1(test_query, n_results=5)
        print(f"✓ 查询成功，返回 {len(l1_results.get('documents', [[]])[0])} 个一级分类")
        
        if l1_results.get('documents') and l1_results['documents'][0]:
            for i, (doc, meta, dist) in enumerate(zip(
                l1_results['documents'][0],
                l1_results['metadatas'][0],
                l1_results.get('distances', [[]])[0]
            )):
                print(f"  {i+1}. 分类: {doc} | 距离: {dist:.4f} | 文档数: {meta.get('count', 'N/A')}")
        
    except Exception as e:
        print(f"✗ 一级分类查询失败: {e}")
    
    # 测试二级分类查询
    print("\n📊 测试二级分类查询 (query_l2):")
    try:
        l2_results = vector_db.query_l2(test_query, n_results=5)
        print(f"✓ 查询成功，返回 {len(l2_results.get('documents', [[]])[0])} 个二级分类")
        
        if l2_results.get('documents') and l2_results['documents'][0]:
            for i, (doc, meta, dist) in enumerate(zip(
                l2_results['documents'][0],
                l2_results['metadatas'][0],
                l2_results.get('distances', [[]])[0]
            )):
                print(f"  {i+1}. 分类: {doc} | 距离: {dist:.4f} | 文档数: {meta.get('count', 'N/A')}")
        
    except Exception as e:
        print(f"✗ 二级分类查询失败: {e}")
    
    # 测试三级分类查询
    print("\n📊 测试三级分类查询 (query_l3):")
    try:
        l3_results = vector_db.query_l3(test_query, n_results=5)
        print(f"✓ 查询成功，返回 {len(l3_results.get('documents', [[]])[0])} 个三级分类")
        
        if l3_results.get('documents') and l3_results['documents'][0]:
            for i, (doc, meta, dist) in enumerate(zip(
                l3_results['documents'][0],
                l3_results['metadatas'][0],
                l3_results.get('distances', [[]])[0]
            )):
                print(f"  {i+1}. 分类: {doc} | 距离: {dist:.4f} | 文档数: {meta.get('count', 'N/A')}")
        
    except Exception as e:
        print(f"✗ 三级分类查询失败: {e}")
    
    # 测试分类预测功能
    print("\n🎯 测试分类预测功能 (get_category_predictions):")
    try:
        predictions = vector_db.get_category_predictions(test_query, n_results=10)
        print("✓ 分类预测成功")
        
        for level, pred_info in predictions.items():
            print(f"  {level}: {pred_info['prediction']} (置信度: {pred_info['confidence']:.4f})")
            
            # 显示候选分类
            if pred_info.get('all_candidates'):
                top_candidates = sorted(
                    pred_info['all_candidates'].items(), 
                    key=lambda x: x[1], 
                    reverse=True
                )[:3]
                candidates_str = ", ".join([f"{cat}({score:.3f})" for cat, score in top_candidates])
                print(f"    候选: {candidates_str}")
        
    except Exception as e:
        print(f"✗ 分类预测失败: {e}")
    
    # 测试所有级别查询
    print("\n📊 测试所有级别查询 (query_all_levels):")
    try:
        all_results = vector_db.query_all_levels(test_query, n_results=3)
        print(f"✓ 查询成功，返回 {len(all_results.get('documents', [[]])[0])} 个文档")
        
        if all_results.get('documents') and all_results['documents'][0]:
            for i, (doc, meta, dist) in enumerate(zip(
                all_results['documents'][0],
                all_results['metadatas'][0],
                all_results.get('distances', [[]])[0]
            )):
                print(f"  {i+1}. 距离: {dist:.4f}")
                print(f"      分类: {meta.get('Cat1', 'N/A')}-{meta.get('Cat2', 'N/A')}-{meta.get('Cat3', 'N/A')}")
                print(f"      标题: {meta.get('Title', 'N/A')[:50]}...")
        
    except Exception as e:
        print(f"✗ 所有级别查询失败: {e}")
    
    print("\n" + "="*80)
    print("🎉 测试完成！")
    print("="*80)

if __name__ == "__main__":
    test_vector_db_queries()
