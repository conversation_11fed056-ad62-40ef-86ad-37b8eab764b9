#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版 Qdrant 向量数据库类

基于现有的 QdrantVectorDB 类进行改进和扩展，提供更完善的功能：
1. 使用 Qdrant 作为向量数据库后端，配置为内存模式运行
2. 向量化数据支持持久化存储到本地 .db 文件
3. 嵌入模型通过 OpenAI API 接口调用（text-embedding-ada-002）
4. 数据源为 CSV 文件，text 列作为主要检索索引进行向量化
5. CSV 文件中除 text 外的其他列作为 payload 存储在 Qdrant 中
6. 层级标签信息存储在 payload 中，支持三级分类结构

特性：
- 内存模式运行，支持本地持久化存储
- 动态 payload 结构，根据 CSV 文件列名自动构建
- 支持批量数据导入和增量更新
- 完整的 CRUD 操作：插入、查询、更新、删除
- 相似性搜索功能，支持返回指定数量的最相似结果
- 向后兼容现有接口
- 详细的中文注释和错误处理

作者：AI Assistant
日期：2025-01-28
版本：2.0
"""

import os
import json
import pickle
import pandas as pd
from pathlib import Path
from typing import Optional, List, Dict, Any, Union, Tuple
from openai import OpenAI
from qdrant_client import QdrantClient
from qdrant_client.models import (
    Distance, VectorParams, PointStruct, Filter, 
    FieldCondition, MatchValue, UpdateResult, SearchResult
)
import httpx
import uuid
import logging
from datetime import datetime
import numpy as np
from dataclasses import dataclass

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 配置自定义 httpx Client，禁用 SSL 验证（仅测试用）
custom_httpx_client = httpx.Client(
    verify=False,
    timeout=httpx.Timeout(30.0)  # 增加超时时间
)

# API 配置 - 支持多种嵌入模型
DEFAULT_OPENAI_API_KEY = "sk-kxqclwblxrbprnibbwxfachyqxnivhocpisdjiciniqckvll"
DEFAULT_OPENAI_BASE_URL = "https://api.siliconflow.cn/v1/"
DEFAULT_EMBEDDING_MODEL = "text-embedding-ada-002"  # 使用 OpenAI 标准嵌入模型

@dataclass
class VectorSearchResult:
    """
    向量搜索结果数据类
    
    封装搜索结果的各种信息，便于后续处理和分析
    """
    documents: List[str]
    metadatas: List[Dict[str, Any]]
    distances: List[float]
    scores: List[float]  # 相似度分数 (1 - distance)
    ids: List[str]
    
    def __len__(self) -> int:
        """返回结果数量"""
        return len(self.documents)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，兼容现有接口"""
        return {
            "documents": [self.documents],
            "metadatas": [self.metadatas],
            "distances": [self.distances],
            "scores": [self.scores],
            "ids": [self.ids]
        }

class EnhancedQdrantEmbeddingFunction:
    """
    增强版基于 OpenAI 接口的嵌入函数
    
    提供文本向量化功能，支持批量处理、错误重试机制和多种嵌入模型
    """
    
    def __init__(self, 
                 api_key: str = None, 
                 base_url: str = None, 
                 model_name: str = None,
                 max_retries: int = 3,
                 batch_size: int = 100):
        """
        初始化嵌入函数
        
        Args:
            api_key: OpenAI API密钥，如果为None则使用默认值
            base_url: API基础URL，如果为None则使用默认值
            model_name: 嵌入模型名称，如果为None则使用默认值
            max_retries: 最大重试次数
            batch_size: 批处理大小，用于大量文本的分批处理
        """
        self.api_key = api_key or DEFAULT_OPENAI_API_KEY
        self.base_url = base_url or DEFAULT_OPENAI_BASE_URL
        self.model_name = model_name or DEFAULT_EMBEDDING_MODEL
        self.max_retries = max_retries
        self.batch_size = batch_size
        
        # 初始化 OpenAI 客户端
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url,
            http_client=custom_httpx_client
        )
        
        logger.info(f"初始化增强版嵌入函数: {self.model_name}")
        logger.info(f"API基础URL: {self.base_url}")
        logger.info(f"批处理大小: {self.batch_size}")
        
        # 测试连接
        self._test_connection()
    
    def _test_connection(self) -> bool:
        """
        测试 API 连接是否正常
        
        Returns:
            连接状态，True表示正常，False表示异常
        """
        try:
            test_result = self.get_embeddings(["测试连接"])
            if test_result and len(test_result[0]) > 0:
                logger.info(f"✓ API连接测试成功，向量维度: {len(test_result[0])}")
                return True
            else:
                logger.warning("API连接测试失败：返回空向量")
                return False
        except Exception as e:
            logger.error(f"API连接测试失败: {e}")
            return False
    
    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        获取文本嵌入向量，支持批量处理
        
        Args:
            texts: 文本列表
            
        Returns:
            嵌入向量列表
            
        Raises:
            Exception: 当所有重试都失败时抛出异常
        """
        if not texts:
            return []
        
        # 如果文本数量超过批处理大小，进行分批处理
        if len(texts) > self.batch_size:
            logger.info(f"文本数量 {len(texts)} 超过批处理大小 {self.batch_size}，进行分批处理")
            all_embeddings = []
            
            for i in range(0, len(texts), self.batch_size):
                batch_texts = texts[i:i + self.batch_size]
                batch_embeddings = self._get_embeddings_batch(batch_texts)
                all_embeddings.extend(batch_embeddings)
                logger.debug(f"完成批次 {i//self.batch_size + 1}/{(len(texts)-1)//self.batch_size + 1}")
            
            return all_embeddings
        else:
            return self._get_embeddings_batch(texts)
    
    def _get_embeddings_batch(self, texts: List[str]) -> List[List[float]]:
        """
        获取单个批次的嵌入向量
        
        Args:
            texts: 文本列表
            
        Returns:
            嵌入向量列表
        """
        for attempt in range(self.max_retries):
            try:
                response = self.client.embeddings.create(
                    model=self.model_name,
                    input=texts
                )
                embeddings = [data.embedding for data in response.data]
                logger.debug(f"成功获取 {len(texts)} 个文本的嵌入向量")
                return embeddings
                
            except Exception as e:
                logger.warning(f"嵌入模型调用失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt == self.max_retries - 1:
                    logger.error(f"所有重试都失败，无法获取嵌入向量: {e}")
                    raise e
        
        return []
    
    def get_single_embedding(self, text: str) -> List[float]:
        """
        获取单个文本的嵌入向量
        
        Args:
            text: 单个文本
            
        Returns:
            单个嵌入向量
        """
        result = self.get_embeddings([text])
        return result[0] if result else []

class EnhancedQdrantVectorDB:
    """
    增强版基于 Qdrant 的向量数据库管理类
    
    在原有功能基础上增加了以下特性：
    - 更完善的错误处理和日志记录
    - 支持多种数据格式的导入（CSV、JSON、Excel）
    - 增强的查询功能，支持复杂过滤条件
    - 数据统计和分析功能
    - 更好的持久化和备份机制
    - 支持数据版本管理
    """
    
    def __init__(self,
                 host: str = None,
                 port: int = 6333,
                 collection_name: str = "enhanced_collection",
                 use_memory: bool = True,
                 storage_path: str = None,
                 text_field: str = "text",
                 auto_persist: bool = True,
                 embedding_config: Dict[str, Any] = None):
        """
        初始化增强版 Qdrant 向量数据库
        
        Args:
            host: Qdrant服务器地址，如果为None且use_memory=True则使用内存模式
            port: Qdrant服务器端口
            collection_name: 集合名称
            use_memory: 是否使用内存模式（默认True，适合本地开发和测试）
            storage_path: 本地存储路径，用于数据持久化
            text_field: 用于向量化的文本字段名称
            auto_persist: 是否自动持久化数据
            embedding_config: 嵌入模型配置字典
        """
        self.collection_name = collection_name
        self.text_field = text_field
        self.auto_persist = auto_persist
        self.use_memory = use_memory
        
        # 设置存储路径
        if storage_path is None:
            storage_path = f"./enhanced_vector_storage/{collection_name}"
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化 Qdrant 客户端
        if use_memory or host is None:
            self.client = QdrantClient(":memory:")
            logger.info("🚀 使用 Qdrant 内存模式，无需外部服务器")
        else:
            self.client = QdrantClient(host=host, port=port)
            logger.info(f"🔗 连接到 Qdrant 服务器: {host}:{port}")
        
        # 初始化嵌入函数
        embedding_config = embedding_config or {}
        self.embedding_func = EnhancedQdrantEmbeddingFunction(
            api_key=embedding_config.get("api_key"),
            base_url=embedding_config.get("base_url"),
            model_name=embedding_config.get("model_name"),
            max_retries=embedding_config.get("max_retries", 3),
            batch_size=embedding_config.get("batch_size", 100)
        )
        
        # 数据缓存和元数据
        self._data_cache = []
        self._metadata_schema = {}
        self._version = "2.0"
        self._created_at = datetime.now().isoformat()
        
        # 创建集合
        self._create_collections()
        
        # 尝试加载已存在的数据
        if self.auto_persist:
            self._load_persisted_data()
        
        logger.info(f"✓ 增强版向量数据库初始化完成: {self.collection_name}")

    def _create_collections(self):
        """
        创建向量集合

        使用测试文本检测向量维度，然后创建集合
        """
        try:
            # 获取嵌入维度（通过测试文本）
            test_embedding = self.embedding_func.get_single_embedding("测试文本")
            vector_size = len(test_embedding)
            logger.info(f"检测到向量维度: {vector_size}")

            # 创建主集合
            try:
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(size=vector_size, distance=Distance.COSINE)
                )
                logger.info(f"✓ 创建集合: {self.collection_name}")
            except Exception as e:
                if "already exists" in str(e).lower():
                    logger.debug(f"集合已存在: {self.collection_name}")
                else:
                    logger.warning(f"创建集合时发生错误: {e}")

        except Exception as e:
            logger.error(f"创建集合时发生错误: {e}")
            raise e

    def _save_metadata_schema(self):
        """保存元数据结构到本地文件"""
        if not self.auto_persist:
            return

        schema_file = self.storage_path / "enhanced_metadata_schema.json"
        try:
            schema_data = {
                **self._metadata_schema,
                "version": self._version,
                "created_at": self._created_at,
                "last_updated": datetime.now().isoformat(),
                "collection_name": self.collection_name,
                "text_field": self.text_field
            }

            with open(schema_file, 'w', encoding='utf-8') as f:
                json.dump(schema_data, f, ensure_ascii=False, indent=2)
            logger.debug(f"保存增强版元数据结构到: {schema_file}")
        except Exception as e:
            logger.warning(f"保存元数据结构失败: {e}")

    def _load_metadata_schema(self):
        """从本地文件加载元数据结构"""
        schema_file = self.storage_path / "enhanced_metadata_schema.json"
        if schema_file.exists():
            try:
                with open(schema_file, 'r', encoding='utf-8') as f:
                    schema_data = json.load(f)

                self._metadata_schema = {k: v for k, v in schema_data.items()
                                       if k not in ["version", "created_at", "last_updated", "collection_name", "text_field"]}

                # 加载版本信息
                self._version = schema_data.get("version", "1.0")
                self._created_at = schema_data.get("created_at", datetime.now().isoformat())

                logger.debug(f"加载增强版元数据结构: {len(self._metadata_schema)} 个字段")
            except Exception as e:
                logger.warning(f"加载元数据结构失败: {e}")
                self._metadata_schema = {}

    def _save_data_cache(self):
        """保存数据缓存到本地文件"""
        if not self.auto_persist or not self._data_cache:
            return

        cache_file = self.storage_path / "enhanced_data_cache.pkl"
        try:
            cache_data = {
                "data": self._data_cache,
                "version": self._version,
                "saved_at": datetime.now().isoformat(),
                "count": len(self._data_cache)
            }

            with open(cache_file, 'wb') as f:
                pickle.dump(cache_data, f)
            logger.debug(f"保存增强版数据缓存: {len(self._data_cache)} 条记录")
        except Exception as e:
            logger.warning(f"保存数据缓存失败: {e}")

    def _load_data_cache(self):
        """从本地文件加载数据缓存"""
        cache_file = self.storage_path / "enhanced_data_cache.pkl"
        if cache_file.exists():
            try:
                with open(cache_file, 'rb') as f:
                    cache_data = pickle.load(f)

                if isinstance(cache_data, dict):
                    self._data_cache = cache_data.get("data", [])
                    logger.debug(f"加载增强版数据缓存: {len(self._data_cache)} 条记录")
                else:
                    # 兼容旧格式
                    self._data_cache = cache_data
                    logger.debug(f"加载兼容格式数据缓存: {len(self._data_cache)} 条记录")

            except Exception as e:
                logger.warning(f"加载数据缓存失败: {e}")
                self._data_cache = []

    def _load_persisted_data(self):
        """加载持久化的数据"""
        try:
            self._load_metadata_schema()
            self._load_data_cache()

            # 如果有缓存数据，重新加载到向量数据库
            if self._data_cache:
                logger.info(f"发现 {len(self._data_cache)} 条缓存数据，正在重新加载...")
                self._reload_cached_data()

        except Exception as e:
            logger.warning(f"加载持久化数据失败: {e}")

    def _reload_cached_data(self):
        """重新加载缓存的数据到向量数据库"""
        try:
            texts = []
            metadatas = []

            for item in self._data_cache:
                if self.text_field in item:
                    texts.append(item[self.text_field])
                    # 构建metadata，排除text字段
                    metadata = {k: v for k, v in item.items() if k != self.text_field}
                    metadatas.append(metadata)

            if texts:
                self._batch_add_to_collection(texts, metadatas)
                logger.info(f"✓ 重新加载了 {len(texts)} 条数据到向量数据库")

        except Exception as e:
            logger.error(f"重新加载缓存数据失败: {e}")

    def _detect_text_column(self, df: pd.DataFrame) -> str:
        """
        自动检测文本列

        Args:
            df: DataFrame对象

        Returns:
            检测到的文本列名
        """
        # 优先级顺序的候选列名
        candidates = [
            'text', 'Text', 'TEXT', 'content', 'Content', 'CONTENT',
            'description', 'Description', 'DESCRIPTION',
            'abstract', 'Abstract', 'ABSTRACT',
            'summary', 'Summary', 'SUMMARY'
        ]

        # 检查是否有预定义的候选列
        for candidate in candidates:
            if candidate in df.columns:
                logger.info(f"自动检测到文本列: {candidate}")
                return candidate

        # 如果没有找到，选择第一个字符串类型且内容较长的列
        for col in df.columns:
            if df[col].dtype == 'object':  # 通常字符串列的dtype是object
                # 检查该列是否包含较长的文本
                sample_texts = df[col].dropna().head(10)
                if len(sample_texts) > 0:
                    avg_length = sample_texts.astype(str).str.len().mean()
                    if avg_length > 20:  # 平均长度大于20个字符
                        logger.info(f"自动检测到文本列: {col} (平均长度: {avg_length:.1f})")
                        return col

        # 如果还是没找到，使用第一列
        first_col = df.columns[0]
        logger.warning(f"无法自动检测文本列，使用第一列: {first_col}")
        return first_col

    def _clean_dataframe(self, df: pd.DataFrame, text_column: str) -> pd.DataFrame:
        """
        清洗DataFrame数据

        Args:
            df: 原始DataFrame
            text_column: 文本列名

        Returns:
            清洗后的DataFrame
        """
        # 复制数据避免修改原始数据
        df_clean = df.copy()

        # 移除文本列为空的行
        df_clean = df_clean.dropna(subset=[text_column])

        # 移除文本列为空字符串的行
        df_clean = df_clean[df_clean[text_column].astype(str).str.strip() != '']

        # 处理其他列的缺失值
        for col in df_clean.columns:
            if col != text_column:
                # 将NaN值替换为字符串"unknown"
                df_clean[col] = df_clean[col].fillna('unknown')

        # 确保文本列是字符串类型
        df_clean[text_column] = df_clean[text_column].astype(str)

        return df_clean

    def _update_metadata_schema(self, columns: List[str], text_column: str):
        """
        更新元数据结构

        Args:
            columns: 所有列名
            text_column: 文本列名
        """
        self._metadata_schema = {
            'text_column': text_column,
            'all_columns': columns,
            'payload_columns': [col for col in columns if col != text_column],
            'updated_at': datetime.now().isoformat(),
            'total_fields': len(columns),
            'payload_fields': len([col for col in columns if col != text_column])
        }
        logger.debug(f"更新元数据结构: {self._metadata_schema['payload_fields']} 个payload字段")

    def load_csv_data(self,
                     csv_path: str,
                     text_column: str = None,
                     encoding: str = 'utf-8',
                     hierarchical_columns: Dict[str, str] = None) -> Dict[str, Any]:
        """
        从CSV文件加载数据并自动处理

        Args:
            csv_path: CSV文件路径
            text_column: 用于向量化的文本列名，如果为None则自动检测
            encoding: 文件编码
            hierarchical_columns: 层级标签列映射，例如 {"level1": "Cat1", "level2": "Cat2", "level3": "Cat3"}

        Returns:
            包含加载统计信息的字典
        """
        try:
            # 读取CSV文件
            df = pd.read_csv(csv_path, encoding=encoding)
            logger.info(f"成功读取CSV文件: {csv_path}, 共 {len(df)} 行数据")

            # 自动检测文本列
            if text_column is None:
                text_column = self._detect_text_column(df)

            if text_column not in df.columns:
                raise ValueError(f"指定的文本列 '{text_column}' 不存在于CSV文件中")

            # 数据清洗
            df_clean = self._clean_dataframe(df, text_column)
            logger.info(f"数据清洗后: {len(df_clean)} 行数据")

            # 处理层级标签
            if hierarchical_columns:
                df_clean = self._process_hierarchical_labels(df_clean, hierarchical_columns)

            # 更新元数据结构
            self._update_metadata_schema(df_clean.columns.tolist(), text_column)

            # 批量添加数据
            result = self._add_dataframe_data(df_clean, text_column)

            # 保存持久化数据
            if self.auto_persist:
                self._save_metadata_schema()
                self._save_data_cache()

            return {
                'total_rows': len(df),
                'processed_rows': len(df_clean),
                'text_column': text_column,
                'columns': df_clean.columns.tolist(),
                'hierarchical_columns': hierarchical_columns,
                'success': True,
                **result
            }

        except Exception as e:
            logger.error(f"加载CSV数据失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_rows': 0,
                'processed_rows': 0
            }

    def _process_hierarchical_labels(self,
                                   df: pd.DataFrame,
                                   hierarchical_columns: Dict[str, str]) -> pd.DataFrame:
        """
        处理层级标签，标准化层级分类信息

        Args:
            df: DataFrame对象
            hierarchical_columns: 层级标签列映射

        Returns:
            处理后的DataFrame
        """
        df_processed = df.copy()

        # 标准化层级标签格式
        for level, column in hierarchical_columns.items():
            if column in df_processed.columns:
                # 清理和标准化标签
                df_processed[column] = df_processed[column].astype(str).str.lower()
                df_processed[column] = df_processed[column].str.replace(' ', '_')
                df_processed[column] = df_processed[column].str.replace('/', '_')
                df_processed[column] = df_processed[column].str.replace('-', '_')

                # 添加level信息到元数据
                df_processed[f'{column}_level'] = level

                logger.debug(f"处理层级标签 {level}: {column}, 唯一值数量: {df_processed[column].nunique()}")

        return df_processed

    def _add_dataframe_data(self, df: pd.DataFrame, text_column: str) -> Dict[str, Any]:
        """
        添加DataFrame数据到向量数据库

        Args:
            df: 清洗后的DataFrame
            text_column: 文本列名

        Returns:
            添加结果统计
        """
        try:
            # 提取文本和元数据
            texts = df[text_column].tolist()

            # 构建元数据列表
            metadatas = []
            for _, row in df.iterrows():
                metadata = {}
                for col in df.columns:
                    if col != text_column:
                        metadata[col] = row[col]
                metadatas.append(metadata)

            # 更新数据缓存
            for i, text in enumerate(texts):
                cache_item = {text_column: text}
                cache_item.update(metadatas[i])
                self._data_cache.append(cache_item)

            # 批量添加到向量数据库
            result = self._batch_add_to_collection(texts, metadatas)

            logger.info(f"✓ 成功添加 {len(texts)} 条数据到向量数据库")
            return result

        except Exception as e:
            logger.error(f"添加DataFrame数据失败: {e}")
            raise e

    def _batch_add_to_collection(self, texts: List[str], metadatas: Optional[List[dict]] = None) -> Dict[str, Any]:
        """
        批量添加数据到集合（内部方法）

        Args:
            texts: 文本列表
            metadatas: 元数据列表

        Returns:
            添加结果统计
        """
        if not texts:
            return {'added_count': 0, 'error': 'No texts provided'}

        try:
            # 生成嵌入向量
            logger.info(f"正在生成 {len(texts)} 个文本的嵌入向量...")
            embeddings = self.embedding_func.get_embeddings(texts)

            # 准备数据点
            points = []

            for i, (text, embedding) in enumerate(zip(texts, embeddings)):
                point_id = str(uuid.uuid4())

                # 构建payload
                payload = {
                    self.text_field: text,
                    "index": i,
                    "created_at": datetime.now().isoformat(),
                    "point_id": point_id
                }

                if metadatas and i < len(metadatas) and metadatas[i]:
                    payload.update(metadatas[i])

                # 创建数据点
                points.append(PointStruct(
                    id=point_id,
                    vector=embedding,
                    payload=payload
                ))

            # 插入到集合
            self.client.upsert(collection_name=self.collection_name, points=points)

            logger.info(f"✓ 成功添加 {len(texts)} 条数据到向量数据库")
            return {
                'added_count': len(texts),
                'success': True,
                'point_ids': [point.id for point in points]
            }

        except Exception as e:
            logger.error(f"批量添加数据失败: {e}")
            return {
                'added_count': 0,
                'success': False,
                'error': str(e)
            }

    def add_text(self, text: str, metadata: Optional[dict] = None) -> Dict[str, Any]:
        """
        添加单个文本（新接口）

        Args:
            text: 文本内容
            metadata: 元数据字典

        Returns:
            添加结果
        """
        metadatas = [metadata] if metadata else None
        result = self._batch_add_to_collection([text], metadatas)

        # 更新数据缓存
        if self.auto_persist and result.get('success'):
            cache_item = {self.text_field: text}
            if metadata:
                cache_item.update(metadata)
            self._data_cache.append(cache_item)
            self._save_data_cache()

        return result

    def batch_add_texts(self, texts: List[str], metadatas: Optional[List[dict]] = None) -> Dict[str, Any]:
        """
        批量添加文本（新接口）

        Args:
            texts: 文本列表
            metadatas: 元数据列表，与texts一一对应

        Returns:
            添加结果
        """
        if metadatas and len(metadatas) != len(texts):
            raise ValueError("metadatas长度必须与texts长度相等")

        result = self._batch_add_to_collection(texts, metadatas)

        # 更新数据缓存
        if self.auto_persist and result.get('success'):
            for i, text in enumerate(texts):
                cache_item = {self.text_field: text}
                if metadatas and i < len(metadatas) and metadatas[i]:
                    cache_item.update(metadatas[i])
                self._data_cache.append(cache_item)
            self._save_data_cache()

        return result

    def query(self,
             query_text: str,
             n_results: int = 10,
             where: Optional[dict] = None,
             score_threshold: float = None) -> VectorSearchResult:
        """
        查询相似文档（增强版接口）

        Args:
            query_text: 查询文本
            n_results: 返回结果数量
            where: 元数据过滤条件
            score_threshold: 相似度分数阈值，低于此值的结果将被过滤

        Returns:
            VectorSearchResult对象
        """
        try:
            # 生成查询向量
            query_embedding = self.embedding_func.get_single_embedding(query_text)

            # 构建过滤条件
            query_filter = None
            if where:
                conditions = []
                for key, value in where.items():
                    conditions.append(FieldCondition(key=key, match=MatchValue(value=value)))
                if conditions:
                    query_filter = Filter(must=conditions)

            # 执行查询
            search_result = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                limit=n_results,
                query_filter=query_filter,
                with_payload=True,
                with_vectors=False,
                score_threshold=score_threshold
            )

            # 格式化结果
            documents = []
            metadatas = []
            distances = []
            scores = []
            ids = []

            for point in search_result:
                # 获取文本内容
                text_content = point.payload.get(self.text_field, "")
                documents.append(text_content)

                # 构建元数据，排除内部字段
                metadata = {}
                internal_fields = {self.text_field, "index", "created_at", "point_id"}
                for k, v in point.payload.items():
                    if k not in internal_fields:
                        metadata[k] = v
                metadatas.append(metadata)

                # 相似度分数和距离
                score = point.score
                distance = 1 - score  # 转换为距离
                scores.append(score)
                distances.append(distance)
                ids.append(str(point.id))

            logger.debug(f"查询 '{query_text}' 返回 {len(documents)} 个结果")

            return VectorSearchResult(
                documents=documents,
                metadatas=metadatas,
                distances=distances,
                scores=scores,
                ids=ids
            )

        except Exception as e:
            logger.error(f"查询失败: {e}")
            return VectorSearchResult(
                documents=[],
                metadatas=[],
                distances=[],
                scores=[],
                ids=[]
            )

    def query_hierarchical(self,
                          query_text: str,
                          level: str,
                          n_results: int = 10,
                          score_threshold: float = None) -> VectorSearchResult:
        """
        查询指定层级的分类

        Args:
            query_text: 查询文本
            level: 层级标识，如 "Category1", "Category2", "Category3"
            n_results: 返回结果数量
            score_threshold: 相似度分数阈值

        Returns:
            VectorSearchResult对象
        """
        where_condition = {"level": level}
        return self.query(query_text, n_results, where_condition, score_threshold)

    def query_l1(self, query_text: str, n_results: int = 10) -> dict:
        """
        查询一级分类（向后兼容接口）

        Args:
            query_text: 查询文本
            n_results: 返回结果数量

        Returns:
            查询结果字典
        """
        result = self.query_hierarchical(query_text, "Category1", n_results)
        return result.to_dict()

    def query_l2(self, query_text: str, n_results: int = 10) -> dict:
        """
        查询二级分类（向后兼容接口）

        Args:
            query_text: 查询文本
            n_results: 返回结果数量

        Returns:
            查询结果字典
        """
        result = self.query_hierarchical(query_text, "Category2", n_results)
        return result.to_dict()

    def query_l3(self, query_text: str, n_results: int = 10) -> dict:
        """
        查询三级分类（向后兼容接口）

        Args:
            query_text: 查询文本
            n_results: 返回结果数量

        Returns:
            查询结果字典
        """
        result = self.query_hierarchical(query_text, "Category3", n_results)
        return result.to_dict()

    def query_all_levels(self, query_text: str, n_results: int = 10) -> dict:
        """
        查询所有级别的分类（向后兼容接口）

        Args:
            query_text: 查询文本
            n_results: 返回结果数量

        Returns:
            查询结果字典
        """
        result = self.query(query_text, n_results)
        return result.to_dict()

    def batch_add(self, titles: List[str], texts: List[str], metadatas: Optional[List[dict]] = None):
        """
        批量添加文档到向量数据库（向后兼容接口）

        Args:
            titles: 标题列表
            texts: 文本列表
            metadatas: 元数据列表，与titles和texts一一对应
        """
        if not titles or not texts or len(titles) != len(texts):
            raise ValueError("titles和texts长度必须相等且不为空")

        if metadatas and len(metadatas) != len(titles):
            raise ValueError("metadatas长度必须与titles长度相等")

        # 构建新格式的元数据，包含title信息
        new_metadatas = []
        for i in range(len(texts)):
            metadata = {"title": titles[i]}
            if metadatas and i < len(metadatas) and metadatas[i]:
                metadata.update(metadatas[i])
            new_metadatas.append(metadata)

        # 使用新的批量添加方法
        return self.batch_add_texts(texts, new_metadatas)

    def add_single(self, title: str, text: str, metadata: Optional[dict] = None):
        """添加单个文档（向后兼容接口）"""
        combined_metadata = {"title": title}
        if metadata:
            combined_metadata.update(metadata)
        return self.add_text(text, combined_metadata)

    def update_document(self, point_id: str, text: str = None, metadata: dict = None) -> bool:
        """
        更新文档

        Args:
            point_id: 文档ID
            text: 新的文本内容（如果提供）
            metadata: 新的元数据（如果提供）

        Returns:
            更新是否成功
        """
        try:
            # 如果需要更新文本，重新生成向量
            if text is not None:
                embedding = self.embedding_func.get_single_embedding(text)

                # 构建新的payload
                payload = {
                    self.text_field: text,
                    "updated_at": datetime.now().isoformat()
                }
                if metadata:
                    payload.update(metadata)

                # 更新点
                self.client.upsert(
                    collection_name=self.collection_name,
                    points=[PointStruct(
                        id=point_id,
                        vector=embedding,
                        payload=payload
                    )]
                )
            elif metadata is not None:
                # 只更新元数据
                self.client.set_payload(
                    collection_name=self.collection_name,
                    payload=metadata,
                    points=[point_id]
                )

            logger.info(f"✓ 成功更新文档: {point_id}")
            return True

        except Exception as e:
            logger.error(f"更新文档失败: {e}")
            return False

    def delete_by_ids(self, ids: List[str]) -> bool:
        """
        根据ID删除文档

        Args:
            ids: 要删除的文档ID列表

        Returns:
            删除是否成功
        """
        try:
            self.client.delete(collection_name=self.collection_name, points_selector=ids)

            # 从数据缓存中删除（简化处理）
            if self.auto_persist:
                logger.debug("注意：从缓存中删除数据功能需要进一步实现")

            logger.info(f"✓ 删除了 {len(ids)} 个文档")
            return True

        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            return False

    def delete_by_metadata(self, where: dict) -> bool:
        """
        根据元数据删除文档

        Args:
            where: 元数据过滤条件字典

        Returns:
            删除是否成功
        """
        try:
            conditions = []
            for key, value in where.items():
                conditions.append(FieldCondition(key=key, match=MatchValue(value=value)))

            if conditions:
                filter_condition = Filter(must=conditions)
                self.client.delete(collection_name=self.collection_name, points_selector=filter_condition)
                logger.info(f"✓ 根据条件删除文档: {where}")
                return True
            else:
                logger.warning("没有提供有效的删除条件")
                return False

        except Exception as e:
            logger.error(f"根据元数据删除文档失败: {e}")
            return False

    def get_collection_info(self) -> dict:
        """
        获取集合信息

        Returns:
            包含集合信息的字典
        """
        try:
            # 获取集合信息
            collection_info = self.client.get_collection(self.collection_name)

            return {
                "collection_name": self.collection_name,
                "points_count": collection_info.points_count,
                "vector_size": collection_info.config.params.vectors.size,
                "distance_metric": collection_info.config.params.vectors.distance.name,
                "storage_path": str(self.storage_path),
                "text_field": self.text_field,
                "auto_persist": self.auto_persist,
                "cache_size": len(self._data_cache),
                "metadata_schema": self._metadata_schema,
                "version": self._version,
                "created_at": self._created_at,
                "use_memory": self.use_memory
            }

        except Exception as e:
            logger.error(f"获取集合信息失败: {e}")
            return {
                "error": str(e),
                "collection_name": self.collection_name,
                "points_count": 0,
                "vector_size": 0
            }

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取详细的数据库统计信息

        Returns:
            统计信息字典
        """
        try:
            collection_info = self.get_collection_info()

            stats = {
                'basic_info': {
                    'collection_name': self.collection_name,
                    'total_documents': collection_info.get('points_count', 0),
                    'vector_dimension': collection_info.get('vector_size', 0),
                    'distance_metric': collection_info.get('distance_metric', 'unknown'),
                    'version': self._version
                },
                'storage_info': {
                    'storage_path': str(self.storage_path),
                    'use_memory': self.use_memory,
                    'auto_persist': self.auto_persist,
                    'cache_size': len(self._data_cache)
                },
                'schema_info': {
                    'text_field': self.text_field,
                    'total_fields': self._metadata_schema.get('total_fields', 0),
                    'payload_fields': self._metadata_schema.get('payload_fields', 0),
                    'all_columns': self._metadata_schema.get('all_columns', []),
                    'payload_columns': self._metadata_schema.get('payload_columns', [])
                },
                'timestamps': {
                    'created_at': self._created_at,
                    'last_updated': self._metadata_schema.get('updated_at', 'Unknown')
                }
            }

            # 添加层级统计信息（如果存在）
            if self._data_cache:
                level_stats = {}
                for item in self._data_cache:
                    for key, value in item.items():
                        if 'level' in key.lower() or key.lower().startswith('cat'):
                            if key not in level_stats:
                                level_stats[key] = {}
                            level_stats[key][value] = level_stats[key].get(value, 0) + 1

                if level_stats:
                    stats['hierarchical_info'] = level_stats

            return stats

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {'error': str(e)}

    def clear_collection(self) -> bool:
        """
        清空集合

        Returns:
            清空是否成功
        """
        try:
            # 删除集合
            self.client.delete_collection(self.collection_name)

            # 清空数据缓存
            self._data_cache = []
            self._metadata_schema = {}

            # 重新创建集合
            self._create_collections()

            # 清空持久化文件
            if self.auto_persist:
                try:
                    schema_file = self.storage_path / "enhanced_metadata_schema.json"
                    cache_file = self.storage_path / "enhanced_data_cache.pkl"

                    if schema_file.exists():
                        schema_file.unlink()
                    if cache_file.exists():
                        cache_file.unlink()

                except Exception as e:
                    logger.warning(f"清空持久化文件失败: {e}")

            logger.info("✓ 成功清空集合和缓存数据")
            return True

        except Exception as e:
            logger.error(f"清空集合失败: {e}")
            return False

    def backup_data(self, backup_path: str = None) -> str:
        """
        备份数据到指定路径

        Args:
            backup_path: 备份路径，如果为None则使用默认路径

        Returns:
            备份文件路径
        """
        if backup_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = self.storage_path / f"enhanced_backup_{timestamp}.json"

        try:
            # 创建备份目录
            backup_dir = Path(backup_path).parent
            backup_dir.mkdir(parents=True, exist_ok=True)

            # 备份数据
            backup_data = {
                'metadata_schema': self._metadata_schema,
                'data_cache': self._data_cache,
                'collection_info': self.get_collection_info(),
                'backup_time': datetime.now().isoformat(),
                'version': self._version,
                'text_field': self.text_field,
                'collection_name': self.collection_name,
                'backup_type': 'enhanced_qdrant_vector_db'
            }

            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)

            logger.info(f"✓ 数据备份到: {backup_path}")
            return str(backup_path)

        except Exception as e:
            logger.error(f"备份数据失败: {e}")
            raise e

    def restore_data(self, backup_path: str) -> bool:
        """
        从备份文件恢复数据

        Args:
            backup_path: 备份文件路径

        Returns:
            恢复是否成功
        """
        try:
            if not Path(backup_path).exists():
                raise FileNotFoundError(f"备份文件不存在: {backup_path}")

            with open(backup_path, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)

            # 验证备份文件格式
            if backup_data.get('backup_type') != 'enhanced_qdrant_vector_db':
                logger.warning("备份文件格式可能不兼容，尝试继续恢复...")

            # 清空现有数据
            self.clear_collection()

            # 恢复元数据结构
            self._metadata_schema = backup_data.get('metadata_schema', {})

            # 恢复数据缓存
            self._data_cache = backup_data.get('data_cache', [])

            # 恢复其他信息
            self._version = backup_data.get('version', self._version)
            self.text_field = backup_data.get('text_field', self.text_field)

            # 重新加载数据到向量数据库
            if self._data_cache:
                self._reload_cached_data()

            # 保存恢复的数据
            if self.auto_persist:
                self._save_metadata_schema()
                self._save_data_cache()

            logger.info(f"✓ 从备份恢复数据: {len(self._data_cache)} 条记录")
            return True

        except Exception as e:
            logger.error(f"恢复数据失败: {e}")
            return False

    def export_to_csv(self, output_path: str = None, include_vectors: bool = False) -> str:
        """
        导出数据到CSV文件

        Args:
            output_path: 输出文件路径，如果为None则使用默认路径
            include_vectors: 是否包含向量数据

        Returns:
            导出文件路径
        """
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = self.storage_path / f"export_{timestamp}.csv"

        try:
            if not self._data_cache:
                logger.warning("没有数据可导出")
                return ""

            # 创建DataFrame
            df = pd.DataFrame(self._data_cache)

            # 如果需要包含向量，获取向量数据
            if include_vectors:
                logger.info("正在获取向量数据...")
                texts = df[self.text_field].tolist()
                vectors = self.embedding_func.get_embeddings(texts)
                df['vector'] = [str(vector) for vector in vectors]

            # 保存到CSV
            df.to_csv(output_path, index=False, encoding='utf-8')

            logger.info(f"✓ 数据导出到: {output_path}")
            return str(output_path)

        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            raise e

    def search_similar_documents(self,
                                document_id: str,
                                n_results: int = 10,
                                exclude_self: bool = True) -> VectorSearchResult:
        """
        查找与指定文档相似的其他文档

        Args:
            document_id: 文档ID
            n_results: 返回结果数量
            exclude_self: 是否排除自身

        Returns:
            VectorSearchResult对象
        """
        try:
            # 获取指定文档
            points = self.client.retrieve(
                collection_name=self.collection_name,
                ids=[document_id],
                with_payload=True,
                with_vectors=True
            )

            if not points:
                logger.warning(f"未找到文档: {document_id}")
                return VectorSearchResult([], [], [], [], [])

            # 使用文档的向量进行搜索
            document_vector = points[0].vector

            search_result = self.client.search(
                collection_name=self.collection_name,
                query_vector=document_vector,
                limit=n_results + (1 if exclude_self else 0),
                with_payload=True,
                with_vectors=False
            )

            # 处理结果
            documents = []
            metadatas = []
            distances = []
            scores = []
            ids = []

            for point in search_result:
                # 如果需要排除自身且当前点是查询文档，跳过
                if exclude_self and str(point.id) == document_id:
                    continue

                # 如果已经收集够了结果，停止
                if len(documents) >= n_results:
                    break

                text_content = point.payload.get(self.text_field, "")
                documents.append(text_content)

                metadata = {}
                internal_fields = {self.text_field, "index", "created_at", "point_id"}
                for k, v in point.payload.items():
                    if k not in internal_fields:
                        metadata[k] = v
                metadatas.append(metadata)

                score = point.score
                distance = 1 - score
                scores.append(score)
                distances.append(distance)
                ids.append(str(point.id))

            return VectorSearchResult(
                documents=documents,
                metadatas=metadatas,
                distances=distances,
                scores=scores,
                ids=ids
            )

        except Exception as e:
            logger.error(f"查找相似文档失败: {e}")
            return VectorSearchResult([], [], [], [], [])

    def __del__(self):
        """
        析构函数，确保数据持久化
        """
        try:
            if hasattr(self, 'auto_persist') and self.auto_persist:
                if hasattr(self, '_data_cache') and self._data_cache:
                    self._save_data_cache()
                if hasattr(self, '_metadata_schema') and self._metadata_schema:
                    self._save_metadata_schema()
        except Exception:
            # 在析构函数中不抛出异常
            pass
