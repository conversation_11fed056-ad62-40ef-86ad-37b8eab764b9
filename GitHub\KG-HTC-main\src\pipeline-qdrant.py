from typing import List, Dict, Any
import random
from src.llm import LLM
from src.graph_db import GraphDB
from src.qdrant_vector_db import QdrantVectorDB


class Pipeline:
    """
    Pipeline类：集成了LLM、图数据库和向量数据库的处理流水线
    用于实现基于知识图谱的层次化文本分类
    """
    def __init__(self, llm: LLM, config: dict):
        """
        初始化Pipeline对象
        
        参数:
            llm: 大语言模型实例
            config: 配置字典，包含各种参数设置
        """
        self._config = config
        self._llm = llm
        self._graph_db = GraphDB()  # 初始化图数据库
        self._vector_db = QdrantVectorDB(  # 初始化Qdrant向量数据库
            host=self._config.get("qdrant_host"),  # Qdrant服务器地址，None表示使用内存模式
            port=self._config.get("qdrant_port", 6333),  # Qdrant服务器端口，默认6333
            collection_name=self._config.get("data_name", "default"),  # 集合名称前缀
            use_memory=self._config.get("qdrant_use_memory", True)  # 是否使用内存模式，默认True
        )
        self._load_prompts()  # 加载提示模板

    def _load_prompts(self):
        """
        从文件中加载系统提示和用户提示模板
        这些模板将用于构建发送给LLM的消息
        """
        with open(self._config["template"]["sys"], "r") as f:
            self.system_template = f.read()
        with open(self._config["template"]["user"], "r") as f:
            self.user_template = f.read()

    def _format_category_text(self, categories: List[str]) -> str:
        """
        格式化类别文本，将类别列表转换为加粗的字符串
        
        参数:
            categories: 类别字符串列表
        
        返回:
            格式化后的类别文本字符串，每个类别用**包围并用逗号分隔
        """
        return "**" + "**, **".join(categories) + "**"
    
    def _ensure_vector_db_initialized(self):
        """
        确保向量数据库已初始化，如果为空则自动重新初始化
        """
        try:
            # 检查向量数据库是否为空
            collection_info = self._vector_db.get_collection_info()
            title_count = collection_info['title_collection']['count']
            text_count = collection_info['text_collection']['count']

            if title_count == 0 and text_count == 0:
                print("⚠ 检测到向量数据库为空，正在自动重新初始化...")
                self._reinitialize_vector_db()

        except Exception as e:
            print(f"⚠ 向量数据库检查失败: {e}")

    def _reinitialize_vector_db(self):
        """
        重新初始化向量数据库
        """
        try:
            import pandas as pd

            # 构建训练数据路径
            data_name = self._config.get("data_name", "nasa")
            if data_name == "nasa":
                data_path = "dataset/nasa/nasa_train.csv"
            else:
                print(f"  ⚠ 未知数据集: {data_name}，跳过自动初始化")
                return

            # 读取训练数据
            try:
                df = pd.read_csv(data_path)
                print(f"  读取训练数据: {len(df)} 条记录")
            except FileNotFoundError:
                print(f"  ✗ 找不到训练数据文件: {data_path}")
                return

            # 数据清洗
            df = df.dropna(subset=['Title', 'Text', 'Cat1', 'Cat2', 'Cat3'])
            df = df[df['Cat1'] != "unknown"]
            df = df[df['Cat2'] != "unknown"]
            df = df[df['Cat3'] != "unknown"]

            # 获取唯一标签
            label_l1 = [cat.lower().replace(' ', '_').replace('/', '_')
                        for cat in df["Cat1"].unique().tolist() if cat is not None]
            label_l2 = [cat.lower().replace(' ', '_').replace('/', '_')
                        for cat in df["Cat2"].unique().tolist() if cat is not None]
            label_l3 = [cat.lower().replace(' ', '_').replace('/', '_')
                        for cat in df["Cat3"].unique().tolist() if cat is not None]

            # 移除unknown标签
            label_l1 = [label for label in label_l1 if label != "unknown"]
            label_l2 = [label for label in label_l2 if label != "unknown"]
            label_l3 = [label for label in label_l3 if label != "unknown"]

            # 添加到向量数据库
            if label_l1:
                self._vector_db.batch_add(
                    titles=label_l1,
                    texts=label_l1,
                    metadatas=[{"level": "Category1"}] * len(label_l1)
                )

            if label_l2:
                self._vector_db.batch_add(
                    titles=label_l2,
                    texts=label_l2,
                    metadatas=[{"level": "Category2"}] * len(label_l2)
                )

            if label_l3:
                self._vector_db.batch_add(
                    titles=label_l3,
                    texts=label_l3,
                    metadatas=[{"level": "Category3"}] * len(label_l3)
                )

            print(f"  ✓ 自动重新初始化完成: L1={len(label_l1)}, L2={len(label_l2)}, L3={len(label_l3)}")

        except Exception as e:
            print(f"  ✗ 自动重新初始化失败: {e}")

    def query_related_nodes(self, text: str) -> Dict[str, Any]:
        """
        查询与输入文本相关的节点

        参数:
            text: 输入文本

        返回:
            包含相关L2和L3节点的字典
            - l2: 二级类别节点列表
            - l3: 三级类别节点列表（如果配置中包含l3_top_k参数）
        """
        # 确保向量数据库已初始化
        self._ensure_vector_db_initialized()

        return {
            "l2": self._vector_db.query_l2(text, self._config["query_params"]["l2_top_k"])["documents"][0],
            "l3": self._vector_db.query_l3(text, self._config["query_params"]["l3_top_k"])["documents"][0] if "l3_top_k" in self._config["query_params"] else None
        }
    
    def build_linked_labels(self, l3_nodes: List[str], related_l2_nodes: List[str]) -> List[str]:
        """
        构建链接标签，将L3节点与其对应的L2和L1节点链接起来
        
        参数:
            l3_nodes: L3节点列表
            related_l2_nodes: 相关的L2节点列表
            
        返回:
            格式化的链接标签列表，形如"L1 -> L2 -> L3"
            仅包含L2节点在related_l2_nodes中的链接
        """
        labels = []
        for l3_node in l3_nodes:
            # print(l3_node)
            l2_node = self._graph_db.query_l2_from_l3(l3_node)  # 从L3节点查询对应的L2节点
            l1_node = self._graph_db.query_l1_from_l2(l2_node)  # 从L2节点查询对应的L1节点
            if l2_node in related_l2_nodes:
                labels.append(f"{l1_node} -> {l2_node} -> {l3_node}")
        return labels
    
    def predict_level(
            self, query_txt_vecdb: str, 
            context_nodes: List[str], 
            sub_graph: List[str]
        ) -> str:
        """
        预测输入文本的层级类别
        
        参数:
            query_txt_vecdb: 查询文本
            context_nodes: 上下文节点列表，作为候选类别
            sub_graph: 子图信息列表，提供给LLM的知识背景
            
        返回:
            预测的类别字符串
        """
        # 构建系统消息，包含类别信息和知识图谱
        sys_msg = self.system_template.format(
            category_text=self._format_category_text(context_nodes),
            knowledge="\n".join(sub_graph)      
        )
        # 构建用户消息，包含查询文本
        user_msg = self.user_template.format(text=query_txt_vecdb)
        # 构建完整消息列表
        messages = self._llm.construct_messages(sys_msg, user_msg)
        # 调用LLM进行预测
        response = self._llm.chat(messages)

        # 如果LLM没有返回有效结果，随机选择一个类别
        if response is None:
            # 从context_nodes中随机选择一个作为结果
            response = random.choice(context_nodes)
        
        return response
    
