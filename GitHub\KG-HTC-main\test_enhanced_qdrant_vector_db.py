#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版 QdrantVectorDB 类测试文件

本文件包含对 EnhancedQdrantVectorDB 类的全面测试：
1. 基本功能测试
2. CSV数据加载测试
3. 层级查询测试
4. CRUD操作测试
5. 备份和恢复测试
6. 向后兼容性测试

作者：AI Assistant
日期：2025-01-28
版本：2.0
"""

import os
import sys
import unittest
import pandas as pd
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.enhanced_qdrant_vector_db import EnhancedQdrantVectorDB, VectorSearchResult

class TestEnhancedQdrantVectorDB(unittest.TestCase):
    """增强版 QdrantVectorDB 测试类"""
    
    def setUp(self):
        """测试前的设置"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        self.test_collection = "test_enhanced_collection"
        
        # 初始化测试数据库
        self.vector_db = EnhancedQdrantVectorDB(
            collection_name=self.test_collection,
            use_memory=True,
            storage_path=self.temp_dir,
            auto_persist=True,
            embedding_config={
                "batch_size": 10,
                "max_retries": 2
            }
        )
        
        # 测试数据
        self.test_texts = [
            "人工智能是计算机科学的一个分支",
            "机器学习是人工智能的子领域",
            "深度学习使用神经网络进行学习",
            "自然语言处理处理人类语言",
            "计算机视觉分析图像和视频"
        ]
        
        self.test_metadatas = [
            {"category": "ai", "level": "Category1", "topic": "general"},
            {"category": "ml", "level": "Category2", "topic": "learning"},
            {"category": "dl", "level": "Category3", "topic": "neural"},
            {"category": "nlp", "level": "Category2", "topic": "language"},
            {"category": "cv", "level": "Category3", "topic": "vision"}
        ]
    
    def tearDown(self):
        """测试后的清理"""
        # 清理临时目录
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsNotNone(self.vector_db)
        self.assertEqual(self.vector_db.collection_name, self.test_collection)
        self.assertTrue(self.vector_db.use_memory)
        self.assertTrue(self.vector_db.auto_persist)
        
        # 测试集合信息
        info = self.vector_db.get_collection_info()
        self.assertIn("collection_name", info)
        self.assertEqual(info["collection_name"], self.test_collection)
    
    def test_add_single_text(self):
        """测试添加单个文本"""
        result = self.vector_db.add_text(
            text=self.test_texts[0],
            metadata=self.test_metadatas[0]
        )
        
        self.assertTrue(result.get("success"))
        self.assertEqual(result.get("added_count"), 1)
        
        # 验证数据是否添加成功
        info = self.vector_db.get_collection_info()
        self.assertEqual(info["points_count"], 1)
    
    def test_batch_add_texts(self):
        """测试批量添加文本"""
        result = self.vector_db.batch_add_texts(
            texts=self.test_texts,
            metadatas=self.test_metadatas
        )
        
        self.assertTrue(result.get("success"))
        self.assertEqual(result.get("added_count"), len(self.test_texts))
        
        # 验证数据是否添加成功
        info = self.vector_db.get_collection_info()
        self.assertEqual(info["points_count"], len(self.test_texts))
    
    def test_query_functionality(self):
        """测试查询功能"""
        # 先添加测试数据
        self.vector_db.batch_add_texts(self.test_texts, self.test_metadatas)
        
        # 测试基本查询
        result = self.vector_db.query("人工智能", n_results=3)
        
        self.assertIsInstance(result, VectorSearchResult)
        self.assertGreater(len(result), 0)
        self.assertLessEqual(len(result), 3)
        
        # 验证结果结构
        self.assertEqual(len(result.documents), len(result.metadatas))
        self.assertEqual(len(result.documents), len(result.scores))
        self.assertEqual(len(result.documents), len(result.distances))
        self.assertEqual(len(result.documents), len(result.ids))
    
    def test_hierarchical_query(self):
        """测试层级查询"""
        # 先添加测试数据
        self.vector_db.batch_add_texts(self.test_texts, self.test_metadatas)
        
        # 测试不同层级的查询
        l1_result = self.vector_db.query_hierarchical("人工智能", "Category1", n_results=5)
        l2_result = self.vector_db.query_hierarchical("机器学习", "Category2", n_results=5)
        l3_result = self.vector_db.query_hierarchical("深度学习", "Category3", n_results=5)
        
        # 验证层级过滤是否生效
        for metadata in l1_result.metadatas:
            self.assertEqual(metadata.get("level"), "Category1")
        
        for metadata in l2_result.metadatas:
            self.assertEqual(metadata.get("level"), "Category2")
        
        for metadata in l3_result.metadatas:
            self.assertEqual(metadata.get("level"), "Category3")
    
    def test_backward_compatibility(self):
        """测试向后兼容性"""
        # 测试旧的batch_add接口
        titles = ["标题1", "标题2", "标题3"]
        texts = self.test_texts[:3]
        metadatas = self.test_metadatas[:3]
        
        result = self.vector_db.batch_add(titles, texts, metadatas)
        self.assertTrue(result.get("success"))
        
        # 测试旧的查询接口
        l1_result = self.vector_db.query_l1("人工智能")
        l2_result = self.vector_db.query_l2("机器学习")
        l3_result = self.vector_db.query_l3("深度学习")
        all_result = self.vector_db.query_all_levels("技术")
        
        # 验证返回格式兼容
        self.assertIn("documents", l1_result)
        self.assertIn("metadatas", l1_result)
        self.assertIn("distances", l1_result)
        
        self.assertIsInstance(l1_result["documents"], list)
        self.assertIsInstance(l1_result["documents"][0], list)
    
    def test_csv_loading(self):
        """测试CSV数据加载"""
        # 创建测试CSV文件
        test_data = {
            'Title': ['标题1', '标题2', '标题3'],
            'Text': self.test_texts[:3],
            'Cat1': ['ai', 'ml', 'dl'],
            'Cat2': ['general', 'learning', 'neural'],
            'Cat3': ['basic', 'algorithm', 'network']
        }
        
        df = pd.DataFrame(test_data)
        csv_path = os.path.join(self.temp_dir, "test_data.csv")
        df.to_csv(csv_path, index=False, encoding='utf-8')
        
        # 测试加载CSV数据
        result = self.vector_db.load_csv_data(
            csv_path=csv_path,
            text_column="Text",
            hierarchical_columns={
                "Category1": "Cat1",
                "Category2": "Cat2",
                "Category3": "Cat3"
            }
        )
        
        self.assertTrue(result.get("success"))
        self.assertEqual(result.get("processed_rows"), 3)
        self.assertEqual(result.get("text_column"), "Text")
        
        # 验证数据是否正确加载
        info = self.vector_db.get_collection_info()
        self.assertEqual(info["points_count"], 3)
    
    def test_update_and_delete(self):
        """测试更新和删除功能"""
        # 先添加测试数据
        result = self.vector_db.batch_add_texts(self.test_texts[:2], self.test_metadatas[:2])
        self.assertTrue(result.get("success"))
        
        point_ids = result.get("point_ids", [])
        self.assertGreater(len(point_ids), 0)
        
        # 测试更新文档
        update_success = self.vector_db.update_document(
            point_id=point_ids[0],
            metadata={"updated": True, "new_field": "test_value"}
        )
        self.assertTrue(update_success)
        
        # 测试根据ID删除
        delete_success = self.vector_db.delete_by_ids([point_ids[0]])
        self.assertTrue(delete_success)
        
        # 验证删除是否成功
        info = self.vector_db.get_collection_info()
        self.assertEqual(info["points_count"], 1)
    
    def test_backup_and_restore(self):
        """测试备份和恢复功能"""
        # 先添加测试数据
        self.vector_db.batch_add_texts(self.test_texts, self.test_metadatas)
        
        # 创建备份
        backup_path = self.vector_db.backup_data()
        self.assertTrue(os.path.exists(backup_path))
        
        # 清空数据库
        self.vector_db.clear_collection()
        info = self.vector_db.get_collection_info()
        self.assertEqual(info["points_count"], 0)
        
        # 恢复数据
        restore_success = self.vector_db.restore_data(backup_path)
        self.assertTrue(restore_success)
        
        # 验证恢复是否成功
        info = self.vector_db.get_collection_info()
        self.assertEqual(info["points_count"], len(self.test_texts))
    
    def test_statistics(self):
        """测试统计功能"""
        # 先添加测试数据
        self.vector_db.batch_add_texts(self.test_texts, self.test_metadatas)
        
        # 获取统计信息
        stats = self.vector_db.get_statistics()
        
        self.assertIn("basic_info", stats)
        self.assertIn("storage_info", stats)
        self.assertIn("schema_info", stats)
        self.assertIn("timestamps", stats)
        
        # 验证基本信息
        basic_info = stats["basic_info"]
        self.assertEqual(basic_info["collection_name"], self.test_collection)
        self.assertEqual(basic_info["total_documents"], len(self.test_texts))
        self.assertGreater(basic_info["vector_dimension"], 0)
    
    def test_export_functionality(self):
        """测试导出功能"""
        # 先添加测试数据
        self.vector_db.batch_add_texts(self.test_texts, self.test_metadatas)
        
        # 测试导出到CSV
        export_path = self.vector_db.export_to_csv()
        self.assertTrue(os.path.exists(export_path))
        
        # 验证导出的CSV文件
        df = pd.read_csv(export_path)
        self.assertEqual(len(df), len(self.test_texts))
        self.assertIn(self.vector_db.text_field, df.columns)
    
    def test_similar_documents_search(self):
        """测试相似文档搜索"""
        # 先添加测试数据
        result = self.vector_db.batch_add_texts(self.test_texts, self.test_metadatas)
        point_ids = result.get("point_ids", [])
        
        if point_ids:
            # 查找相似文档
            similar_docs = self.vector_db.search_similar_documents(
                document_id=point_ids[0],
                n_results=3,
                exclude_self=True
            )
            
            self.assertIsInstance(similar_docs, VectorSearchResult)
            # 应该排除自身，所以结果数量应该小于总数
            self.assertLessEqual(len(similar_docs), len(self.test_texts) - 1)

def run_tests():
    """运行所有测试"""
    print("🧪 开始运行增强版 QdrantVectorDB 测试")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestEnhancedQdrantVectorDB)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("🎉 所有测试通过！")
        print(f"✓ 运行了 {result.testsRun} 个测试")
    else:
        print("❌ 测试失败！")
        print(f"✗ 失败: {len(result.failures)} 个")
        print(f"✗ 错误: {len(result.errors)} 个")
        
        # 输出失败详情
        for test, traceback in result.failures + result.errors:
            print(f"\n失败测试: {test}")
            print(f"错误信息: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
