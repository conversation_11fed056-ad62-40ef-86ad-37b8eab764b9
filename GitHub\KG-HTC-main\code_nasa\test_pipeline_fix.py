import pandas as pd
import sys
from pathlib import Path
from tqdm import tqdm
import logging

# 禁用 httpx 的详细日志
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)
# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

from src.qdrant_vector_db import QdrantVectorDB
from src.graph_db import GraphDB
from src.vector_db import VectorDB
import urllib3

# 禁用 SSL 警告信息
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def init_nasa_dataset():
    """
    初始化NASA数据集的知识图谱和向量数据库
    """
    
    # 配置参数
    config = {
        "data_name": "nasa-test",
        "data_path": "dataset/nasa/nasa_train.csv",
        "output_path": "dataset/nasa/llm_graph_gpt3.json",
        # 注意：QdrantVectorDB使用内存模式，不需要vectdb_path
        "vectdb_path": "database/nasa-test",
        "template": {
            "sys": "prompts/system/nasa/llm_graph.txt",
            "user": "prompts/user/nasa/llm_graph.txt"
        },
        "query_params": {
            "l2_top_k": 11,  # NASA数据集二级分类较多，增加检索数量
            "l3_top_k": 53   # NASA数据集三级分类很多，增加检索数量
        }
    }

    print("=== 初始化NASA数据集 ===")
    print(f"数据路径: {config['data_path']}")
    
    # 读取预处理后的数据
    try:
        df = pd.read_csv(config["data_path"])
        print(f"成功读取数据: {len(df)} 条记录")
    except FileNotFoundError:
        print(f"错误: 找不到数据文件 {config['data_path']}")
        print("请先运行 preprocess_nasa.py 进行数据预处理")
        return
    
    # 数据质量检查
    print("\n=== 数据质量检查 ===")
    required_columns = ['Title', 'Text', 'Cat1', 'Cat2', 'Cat3']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"错误: 缺少必要字段 {missing_columns}")
        return
    
    if len(df) > 1000:
        print(f"数据量较大({len(df)}条)，随机采样1000条进行实验")
        df = df.sample(n=100, random_state=42)

    # 移除空值和unknown标签
    initial_count = len(df)
    df = df.dropna(subset=required_columns)
    df = df[df['Cat1'] != "unknown"]
    df = df[df['Cat2'] != "unknown"] 
    df = df[df['Cat3'] != "unknown"]
    df = df[df['Text'].notna()]
    df = df[df['Title'].notna()]
    
    print(f"数据清洗后: {len(df)} 条记录 (移除了 {initial_count - len(df)} 条)")
    
    
    # 初始化数据库连接
    print("\n=== 初始化数据库连接 ===")
    try:
        # QdrantVectorDB构造函数参数：host, port, collection_name, use_memory
        # 使用内存模式，无需database_path参数
        vector_db = VectorDB(
            database_path=config["vectdb_path"],
            collection_name=config["data_name"]
        )
        print("✓ 向量数据库连接成功")
    except Exception as e:
        print(f"✗ 向量数据库连接失败: {e}")
        return
    
    
    # 构建向量数据库
    print("\n=== 构建向量数据库 ===")
    
    # 准备向量化数据
    print("正在准备向量化数据...")
    
    # 提取文本数据（用于向量化）
    texts = []
    metadatas = []
    
    print(f"开始处理 {len(df)} 条记录...")
    
    for idx, row in tqdm(df.iterrows(), total=len(df), desc="准备向量化数据"):
        # 组合标题和文本作为向量化内容
        text_content = f"{row['Title']} {row['Text']}"
        texts.append(text_content)
        
        # 创建元数据（标签作为payload）
        metadata = {
            'Cat1': str(row['Cat1']).lower(),  # 一级分类
            'Cat2': str(row['Cat2']).lower(),  # 二级分类
            'Cat3': str(row['Cat3']).lower(),  # 三级分类
            'Title': str(row['Title']),        # 原始标题
            'level': 'document',               # 标记为文档级别
            'doc_id': str(idx)                 # 文档ID
        }
        metadatas.append(metadata)
    
    print(f"准备完成，共 {len(texts)} 条文本数据")
    
    # 批量添加到向量数据库
    print("正在向量化并添加到数据库...")
    try:
        vector_db.batch_add(
            texts=texts,
            metadatas=metadatas
        )
        print(f"✓ 成功添加 {len(texts)} 条向量化数据")
    except Exception as e:
        print(f"✗ 向量化数据添加失败: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        return
    
    # 验证向量数据库
    print("\n=== 验证向量数据库 ===")
    try:
        db_info = vector_db.get_collection_info()
        print(f"向量数据库信息:")
        print(f"  - 集合名称: {db_info['name']}")
        print(f"  - 文档数量: {db_info['count']}")
        print(f"  - 嵌入类型: {db_info['embedding_type']}")
        
        # 测试查询功能
        print("\n测试查询功能...")
        test_query = "space mission"
        results = vector_db.query_all_levels(test_query, n_results=3)
        
        if results and results.get('documents') and len(results['documents'][0]) > 0:
            print(f"✓ 查询测试成功，找到 {len(results['documents'][0])} 个相关文档")
            print("前3个相关文档的分类信息:")
            for i, metadata in enumerate(results['metadatas'][0][:3]):
                print(f"  {i+1}. Cat1: {metadata.get('Cat1', 'N/A')}, Cat2: {metadata.get('Cat2', 'N/A')}, Cat3: {metadata.get('Cat3', 'N/A')}")
        else:
            print("⚠ 查询测试返回空结果")
            
    except Exception as e:
        print(f"✗ 向量数据库验证失败: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
    
    print("\n=== NASA数据集向量化完成 ===")
    print(f"成功向量化 {len(texts)} 条文档")
    print("可以开始使用向量检索功能进行文本分类实验")

if __name__ == "__main__":
    init_nasa_dataset()
