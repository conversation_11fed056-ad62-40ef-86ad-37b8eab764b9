#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的Pipeline类和向量数据库查询功能

此脚本用于验证修复后的代码是否能正确处理空查询结果的情况
"""

import pandas as pd
import sys
from pathlib import Path

# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

from src.llm import LLM
from src.graph_db import GraphDB
from src.pipeline import Pipeline

def test_graph_db_robustness():
    """
    测试GraphDB类的鲁棒性，特别是处理不存在的标签查询
    """
    print("=== 测试GraphDB类修复效果 ===")

    try:
        graph_db = GraphDB()
        print("✓ 图数据库连接成功")

        # 测试不存在的标签查询
        test_labels = [
            "nonexistent_l3_label",
            "fake_category",
            "",
            None
        ]

        for label in test_labels:
            if label is None:
                continue

            print(f"\n测试查询不存在的L3标签: '{label}'")
            try:
                l2_result = graph_db.query_l2_from_l3(label)
                print(f"  L2结果: {l2_result}")

                if l2_result:
                    l1_result = graph_db.query_l1_from_l2(l2_result)
                    print(f"  L1结果: {l1_result}")

                print("  ✓ 查询完成，无异常")

            except Exception as e:
                print(f"  ✗ 查询失败: {e}")
                return False

        print("✓ GraphDB鲁棒性测试通过")
        return True

    except Exception as e:
        print(f"✗ GraphDB连接失败: {e}")
        return False

def test_pipeline_robustness():
    """
    测试Pipeline类的鲁棒性，特别是处理空查询结果的能力
    """
    print("\n=== 测试Pipeline类修复效果 ===")

    # 配置参数
    config = {
        "data_name": "nasa",
        "data_path": "dataset/nasa/nasa_val.csv",
        "output_path": "dataset/nasa/test_results.json",
        "vectdb_path": "database/nasa",
        "template": {
            "sys": "prompts/system/nasa/llm_graph.txt",
            "user": "prompts/user/nasa/llm_graph.txt"
        },
        "query_params": {
            "l2_top_k": 5,
            "l3_top_k": 5
        }
    }
    
    # 读取测试数据
    try:
        df = pd.read_csv(config["data_path"])
        print(f"✓ 成功读取数据: {len(df)} 条记录")
        
        # 取前3条记录进行测试
        test_data = df.head(3).to_dict(orient="records")
        print(f"✓ 准备测试 {len(test_data)} 条记录")
        
    except FileNotFoundError:
        print(f"✗ 找不到数据文件: {config['data_path']}")
        print("请先运行 init_nasa.py 进行数据初始化")
        return False
    
    # 初始化组件
    print("\n=== 初始化组件 ===")
    try:
        llm = LLM()
        print("✓ LLM初始化成功")
    except Exception as e:
        print(f"✗ LLM初始化失败: {e}")
        return False
    
    try:
        graph_db = GraphDB()
        print("✓ 图数据库连接成功")
    except Exception as e:
        print(f"✗ 图数据库连接失败: {e}")
        return False
    
    try:
        pipeline = Pipeline(llm, config)
        print("✓ Pipeline初始化成功")
    except Exception as e:
        print(f"✗ Pipeline初始化失败: {e}")
        return False
    
    # 测试向量查询功能
    print("\n=== 测试向量查询功能 ===")
    test_queries = [
        "artificial intelligence space technology",
        "earth science climate change",
        "satellite remote sensing",
        "非常罕见的查询文本12345",  # 测试可能返回空结果的查询
        ""  # 测试空查询
    ]
    
    for i, query in enumerate(test_queries):
        print(f"\n测试查询 {i+1}: '{query}'")
        try:
            # 测试修复后的query_related_nodes方法
            retrieved_nodes = pipeline.query_related_nodes(query)
            
            l2_count = len(retrieved_nodes.get("l2", [])) if retrieved_nodes.get("l2") else 0
            l3_count = len(retrieved_nodes.get("l3", [])) if retrieved_nodes.get("l3") else 0
            
            print(f"  L2结果数量: {l2_count}")
            print(f"  L3结果数量: {l3_count}")
            
            if l2_count > 0:
                print(f"  L2示例: {retrieved_nodes['l2'][:2]}")
            if l3_count > 0:
                print(f"  L3示例: {retrieved_nodes['l3'][:2]}")
                
            print("  ✓ 查询成功，无异常")
            
        except Exception as e:
            print(f"  ✗ 查询失败: {e}")
            return False
    
    # 测试完整的分类流程
    print("\n=== 测试完整分类流程 ===")
    success_count = 0
    
    for idx, data in enumerate(test_data):
        print(f"\n测试记录 {idx+1}: {data.get('Title', 'No Title')[:50]}...")
        
        try:
            # 构建查询文本
            query_txt = f"Title: {data.get('Title', '')}\n"
            
            # 1. 向量检索
            retrieved_nodes = pipeline.query_related_nodes(query_txt)
            print(f"  向量检索: L2={len(retrieved_nodes.get('l2', []))}, L3={len(retrieved_nodes.get('l3', []))}")
            
            # 2. 构建子图
            sub_graph = pipeline.build_linked_labels(
                retrieved_nodes.get("l3", []), 
                retrieved_nodes.get("l2", [])
            )
            print(f"  子图大小: {len(sub_graph)}")
            
            # 3. 检查是否有足够的候选
            if retrieved_nodes.get("l2") and len(retrieved_nodes["l2"]) > 0:
                print("  ✓ 有L2候选，可以进行预测")
                success_count += 1
            else:
                print("  ⚠ 没有L2候选")
            
            print("  ✓ 流程完成，无异常")
            
        except Exception as e:
            print(f"  ✗ 处理失败: {e}")
            import traceback
            print(f"  详细错误: {traceback.format_exc()}")
    
    # 总结测试结果
    print(f"\n=== 测试总结 ===")
    print(f"测试记录数: {len(test_data)}")
    print(f"成功处理数: {success_count}")
    print(f"成功率: {success_count/len(test_data)*100:.1f}%")
    
    if success_count == len(test_data):
        print("✓ 所有测试通过，修复成功！")
        return True
    else:
        print("⚠ 部分测试失败，需要进一步检查")
        return False

def test_vector_db_status():
    """
    测试向量数据库的状态
    """
    print("\n=== 检查向量数据库状态 ===")
    
    try:
        from src.qdrant_vector_db import QdrantVectorDB
        
        vector_db = QdrantVectorDB(
            collection_name="nasa",
            use_memory=True
        )
        
        # 获取集合信息
        info = vector_db.get_collection_info()
        print(f"Title集合文档数: {info['title_collection']['count']}")
        print(f"Text集合文档数: {info['text_collection']['count']}")
        
        # 测试查询
        test_query = "artificial intelligence"
        l2_result = vector_db.query_l2(test_query, n_results=3)
        l3_result = vector_db.query_l3(test_query, n_results=3)
        
        l2_count = len(l2_result.get("documents", [[]])[0]) if l2_result else 0
        l3_count = len(l3_result.get("documents", [[]])[0]) if l3_result else 0
        
        print(f"L2查询结果数: {l2_count}")
        print(f"L3查询结果数: {l3_count}")
        
        if l2_count > 0 or l3_count > 0:
            print("✓ 向量数据库包含数据")
            return True
        else:
            print("⚠ 向量数据库可能为空，需要运行 init_nasa.py")
            return False
            
    except Exception as e:
        print(f"✗ 向量数据库检查失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试修复后的Pipeline和GraphDB功能...")

    # 首先测试GraphDB的鲁棒性
    graph_ok = test_graph_db_robustness()

    # 然后检查向量数据库状态
    db_ok = test_vector_db_status()

    if not db_ok:
        print("\n建议先运行以下命令初始化数据库:")
        print("python init_nasa.py")
        print("\n然后再运行此测试脚本")
    elif not graph_ok:
        print("\n❌ GraphDB测试失败，需要检查图数据库连接和数据")
    else:
        # 测试Pipeline功能
        pipeline_ok = test_pipeline_robustness()

        if pipeline_ok:
            print("\n🎉 所有测试通过！可以安全运行 gpt_nasa.py")
        else:
            print("\n❌ 测试失败，需要进一步调试")
