import pandas as pd
import sys
from pathlib import Path
from tqdm import tqdm
import logging

# 禁用 httpx 的详细日志
logging.getLogger("httpx").setLevel(logging.ERROR)

# 配置 urllib3 日志级别为 ERROR，忽略 WARNING
logging.getLogger('urllib3').setLevel(logging.ERROR)

# 配置 backoff 日志级别为 ERROR，忽略 INFO
logging.getLogger('backoff').setLevel(logging.ERROR)
# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

from src.qdrant_vector_db import QdrantVectorDB
from src.graph_db import GraphDB
from src.vector_db import VectorDB
import urllib3

# 禁用 SSL 警告信息
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def init_nasa_dataset():
    """
    初始化NASA数据集的知识图谱和向量数据库
    """
    
    # 配置参数
    config = {
        "data_name": "nasa-test",
        "data_path": "dataset/nasa/nasa_train.csv",
        "output_path": "dataset/nasa/llm_graph_gpt3.json",
        # 注意：QdrantVectorDB使用内存模式，不需要vectdb_path
        "vectdb_path": "database/nasa-test",
        "template": {
            "sys": "prompts/system/nasa/llm_graph.txt",
            "user": "prompts/user/nasa/llm_graph.txt"
        },
        "query_params": {
            "l2_top_k": 11,  # NASA数据集二级分类较多，增加检索数量
            "l3_top_k": 53   # NASA数据集三级分类很多，增加检索数量
        }
    }

    print("=== 初始化NASA数据集 ===")
    print(f"数据路径: {config['data_path']}")
    
    # 读取预处理后的数据
    try:
        df = pd.read_csv(config["data_path"])
        print(f"成功读取数据: {len(df)} 条记录")
    except FileNotFoundError:
        print(f"错误: 找不到数据文件 {config['data_path']}")
        print("请先运行 preprocess_nasa.py 进行数据预处理")
        return
    
    # 数据质量检查
    print("\n=== 数据质量检查 ===")
    required_columns = ['Title', 'Text', 'Cat1', 'Cat2', 'Cat3']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"错误: 缺少必要字段 {missing_columns}")
        return
    
    if len(df) > 1000:
        print(f"数据量较大({len(df)}条)，随机采样1000条进行实验")
        df = df.sample(n=100, random_state=42)

    # 移除空值和unknown标签
    initial_count = len(df)
    df = df.dropna(subset=required_columns)
    df = df[df['Cat1'] != "unknown"]
    df = df[df['Cat2'] != "unknown"] 
    df = df[df['Cat3'] != "unknown"]
    df = df[df['Text'].notna()]
    df = df[df['Title'].notna()]
    
    print(f"数据清洗后: {len(df)} 条记录 (移除了 {initial_count - len(df)} 条)")
    
    
    # 初始化数据库连接
    print("\n=== 初始化数据库连接 ===")
    try:
        # QdrantVectorDB构造函数参数：host, port, collection_name, use_memory
        # 使用内存模式，无需database_path参数
        vector_db = VectorDB(
            database_path=config["vectdb_path"],
            collection_name=config["data_name"]
        )
        print("✓ 向量数据库连接成功")
    except Exception as e:
        print(f"✗ 向量数据库连接失败: {e}")
        return
    
    
    # 构建向量数据库
    print("\n=== 构建向量数据库 ===")
    
    # 准备向量化数据
    print("正在准备向量化数据...")
    
    # 提取文本数据（用于向量化）
    texts = []
    metadatas = []
    
    print(f"开始处理 {len(df)} 条记录...")
    
    for idx, row in tqdm(df.iterrows(), total=len(df), desc="准备向量化数据"):
        # 组合标题和文本作为向量化内容
        text_content = f"{row['Title']} {row['Text']}"
        texts.append(text_content)
        
        # 创建元数据（标签作为payload）
        metadata = {
            'Cat1': str(row['Cat1']).lower(),  # 一级分类
            'Cat2': str(row['Cat2']).lower(),  # 二级分类
            'Cat3': str(row['Cat3']).lower(),  # 三级分类
            'Title': str(row['Title']),        # 原始标题
            'level': 'document',               # 标记为文档级别
            'doc_id': str(idx)                 # 文档ID
        }
        metadatas.append(metadata)
    
    print(f"准备完成，共 {len(texts)} 条文本数据")
    
    # 批量添加到向量数据库
    print("正在向量化并添加到数据库...")
    try:
        vector_db.batch_add(
            texts=texts,
            metadatas=metadatas
        )
        print(f"✓ 成功添加 {len(texts)} 条向量化数据")
    except Exception as e:
        print(f"✗ 向量化数据添加失败: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        return
    
    # 验证向量数据库
    print("\n=== 验证向量数据库 ===")
    try:
        db_info = vector_db.get_collection_info()
        print(f"向量数据库信息:")
        print(f"  - 集合名称: {db_info['name']}")
        print(f"  - 文档数量: {db_info['count']}")
        print(f"  - 嵌入类型: {db_info['embedding_type']}")
        
        # 测试查询功能
        print("\n=== 测试向量检索功能 ===")
        test_queries = [
            "space mission exploration",
            "satellite communication system",
            "rocket engine propulsion"
        ]

        for query_idx, test_query in enumerate(test_queries, 1):
            print(f"\n🔍 查询 {query_idx}: '{test_query}'")
            print("-" * 60)

            results = vector_db.query_all_levels(test_query, n_results=5)
            print(f"results: {results}")

            if results and results.get('documents') and len(results['documents'][0]) > 0:
                documents = results['documents'][0]
                metadatas = results['metadatas'][0]
                distances = results.get('distances', [None])[0] if results.get('distances') else [None] * len(documents)

                print(f"✓ 找到 {len(documents)} 个相关文档")
                print("\n📋 检索结果详情:")

                for i, (doc, metadata, distance) in enumerate(zip(documents, metadatas, distances)):
                    # 计算相似性分数 (1 - distance，距离越小相似性越高)
                    similarity_score = 1 - distance if distance is not None else "N/A"

                    print(f"\n  📄 结果 {i+1}:")
                    print(f"     🎯 相似性分数: {similarity_score:.4f}" if isinstance(similarity_score, float) else f"     🎯 相似性分数: {similarity_score}")
                    print(f"     📊 距离值: {distance:.4f}" if distance is not None else f"     📊 距离值: N/A")
                    print(f"     🏷️  分类标签: Cat1={metadata.get('Cat1', 'N/A')}, Cat2={metadata.get('Cat2', 'N/A')}, Cat3={metadata.get('Cat3', 'N/A')}")
                    print(f"     📝 标题: {metadata.get('Title', 'N/A')}")

                    # 截取文档内容的前100个字符进行显示
                    doc_preview = doc[:100] + "..." if len(doc) > 100 else doc
                    print(f"     📖 内容预览: {doc_preview}")

                    # 如果有文档ID，也显示出来
                    if metadata.get('doc_id'):
                        print(f"     🆔 文档ID: {metadata.get('doc_id')}")

            else:
                print("⚠ 查询返回空结果")

        # 添加分类级别的专门测试
        print(f"\n=== 测试分类级别检索 ===")
        test_query_levels = "spacecraft navigation system"
        print(f"🔍 测试查询: '{test_query_levels}'")

        # 测试不同级别的查询
        level_tests = [
            ("一级分类", "query_l1"),
            ("二级分类", "query_l2"),
            ("三级分类", "query_l3")
        ]

        for level_name, method_name in level_tests:
            print(f"\n📊 {level_name}检索结果:")
            try:
                method = getattr(vector_db, method_name)
                level_results = method(test_query_levels, n_results=3)

                if level_results and level_results.get('documents') and len(level_results['documents'][0]) > 0:
                    documents = level_results['documents'][0]
                    metadatas = level_results['metadatas'][0]
                    distances = level_results.get('distances', [None])[0] if level_results.get('distances') else [None] * len(documents)

                    for i, (doc, metadata, distance) in enumerate(zip(documents, metadatas, distances)):
                        similarity_score = 1 - distance if distance is not None else "N/A"
                        print(f"  {i+1}. 相似性: {similarity_score:.4f if isinstance(similarity_score, float) else similarity_score} | "
                              f"分类: {metadata.get('Cat1', 'N/A')}-{metadata.get('Cat2', 'N/A')}-{metadata.get('Cat3', 'N/A')}")
                else:
                    print(f"  ⚠ {level_name}无匹配结果")

            except Exception as e:
                print(f"  ✗ {level_name}查询失败: {e}")
            
    except Exception as e:
        print(f"✗ 向量数据库验证失败: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
    
    print("\n=== NASA数据集向量化完成 ===")
    print(f"成功向量化 {len(texts)} 条文档")
    print("可以开始使用向量检索功能进行文本分类实验")

    return vector_db, df

def detailed_retrieval_test(vector_db, df):
    """
    详细的检索测试函数，展示相似样本和相似性分数

    Args:
        vector_db: 向量数据库实例
        df: 原始数据DataFrame
    """
    print("\n" + "="*80)
    print("🔬 详细检索测试 - 相似样本和相似性分数分析")
    print("="*80)

    # 从数据集中随机选择几个样本作为查询
    test_samples = df.sample(n=3, random_state=42)

    for idx, (_, sample) in enumerate(test_samples.iterrows(), 1):
        print(f"\n🧪 测试样本 {idx}")
        print("-" * 50)
        print(f"📝 原始标题: {sample['Title']}")
        print(f"🏷️  真实标签: Cat1={sample['Cat1']}, Cat2={sample['Cat2']}, Cat3={sample['Cat3']}")

        # 使用标题作为查询
        query_text = sample['Title']
        print(f"🔍 查询文本: '{query_text}'")

        # 执行检索
        results = vector_db.query_all_levels(query_text, n_results=8)

        if results and results.get('documents') and len(results['documents'][0]) > 0:
            documents = results['documents'][0]
            metadatas = results['metadatas'][0]
            distances = results.get('distances', [None])[0] if results.get('distances') else [None] * len(documents)

            print(f"\n📊 检索到 {len(documents)} 个相似文档:")
            print("\n" + "="*60)

            for i, (doc, metadata, distance) in enumerate(zip(documents, metadatas, distances)):
                # 计算相似性分数
                similarity_score = 1 - distance if distance is not None else "N/A"

                # 判断是否为正确分类
                is_correct_l1 = metadata.get('Cat1', '').lower() == str(sample['Cat1']).lower()
                is_correct_l2 = metadata.get('Cat2', '').lower() == str(sample['Cat2']).lower()
                is_correct_l3 = metadata.get('Cat3', '').lower() == str(sample['Cat3']).lower()

                # 设置颜色标记
                l1_mark = "✅" if is_correct_l1 else "❌"
                l2_mark = "✅" if is_correct_l2 else "❌"
                l3_mark = "✅" if is_correct_l3 else "❌"

                print(f"\n📄 相似文档 {i+1}:")
                print(f"   🎯 相似性分数: {similarity_score:.6f}" if isinstance(similarity_score, float) else f"   🎯 相似性分数: {similarity_score}")
                print(f"   📏 余弦距离: {distance:.6f}" if distance is not None else f"   📏 余弦距离: N/A")
                print(f"   🏷️  预测标签: {l1_mark}Cat1={metadata.get('Cat1', 'N/A')} | {l2_mark}Cat2={metadata.get('Cat2', 'N/A')} | {l3_mark}Cat3={metadata.get('Cat3', 'N/A')}")
                print(f"   📝 文档标题: {metadata.get('Title', 'N/A')}")

                # 显示文档内容的关键部分
                doc_preview = doc[:150] + "..." if len(doc) > 150 else doc
                print(f"   📖 内容摘要: {doc_preview}")

                # 计算匹配度
                match_score = sum([is_correct_l1, is_correct_l2, is_correct_l3])
                match_percentage = (match_score / 3) * 100
                print(f"   🎯 分类匹配度: {match_score}/3 ({match_percentage:.1f}%)")

                print("-" * 60)

            # 分析检索质量
            print(f"\n📈 检索质量分析:")

            # 计算各级别的准确率
            l1_correct = sum(1 for meta in metadatas if meta.get('Cat1', '').lower() == str(sample['Cat1']).lower())
            l2_correct = sum(1 for meta in metadatas if meta.get('Cat2', '').lower() == str(sample['Cat2']).lower())
            l3_correct = sum(1 for meta in metadatas if meta.get('Cat3', '').lower() == str(sample['Cat3']).lower())

            total_results = len(metadatas)
            print(f"   📊 一级分类准确率: {l1_correct}/{total_results} ({l1_correct/total_results*100:.1f}%)")
            print(f"   📊 二级分类准确率: {l2_correct}/{total_results} ({l2_correct/total_results*100:.1f}%)")
            print(f"   📊 三级分类准确率: {l3_correct}/{total_results} ({l3_correct/total_results*100:.1f}%)")

            # 显示相似性分数分布
            if all(d is not None for d in distances):
                similarity_scores = [1 - d for d in distances]
                print(f"   📈 相似性分数范围: {min(similarity_scores):.4f} - {max(similarity_scores):.4f}")
                print(f"   📈 平均相似性分数: {sum(similarity_scores)/len(similarity_scores):.4f}")
        else:
            print("⚠ 未找到相似文档")

        print("\n" + "="*80)


if __name__ == "__main__":
    # 初始化数据集和向量数据库
    result = init_nasa_dataset()

    if result:
        vector_db, df = result

        # 运行详细检索测试
        detailed_retrieval_test(vector_db, df)



