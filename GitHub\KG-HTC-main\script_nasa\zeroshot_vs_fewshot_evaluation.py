import pandas as pd
import numpy as np
import json
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from sklearn.metrics import accuracy_score, f1_score
import warnings
warnings.filterwarnings('ignore')

def load_all_fewshot_results():
    """加载所有少样本实验结果"""
    results = {}
    
    # 零样本结果
    zero_shot_files = {
        'Zero-Shot KG-HTC': 'dataset/nasa_topics/llm_graph_gpt3.json',
        'Zero-Shot LLM-Only': 'dataset/nasa_topics/llm_only_results.json',
        'Zero-Shot BERT': 'dataset/nasa_topics/bert_baseline_results.json'
    }
    
    # 少样本结果
    few_shot_files = {
        'Few-Shot BERT': 'dataset/nasa_topics/fewshot_bert_results.json',
        'Few-Shot Prompt 1-shot': 'dataset/nasa_topics/fewshot_prompt_1_results.json',
        'Few-Shot Prompt 3-shot': 'dataset/nasa_topics/fewshot_prompt_3_results.json',
        'Few-Shot Prompt 5-shot': 'dataset/nasa_topics/fewshot_prompt_5_results.json',
        'Few-Shot KG-HTC 3-shot': 'dataset/nasa_topics/fewshot_kg_htc_3_results.json',
        'Few-Shot KG-HTC 5-shot': 'dataset/nasa_topics/fewshot_kg_htc_5_results.json'
    }
    
    all_files = {**zero_shot_files, **few_shot_files}
    
    for method, file_path in all_files.items():
        if Path(file_path).exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 处理不同格式的结果文件
                if isinstance(data, dict) and any(key.endswith('-shot') for key in data.keys()):
                    # Few-Shot BERT格式
                    results[method] = data
                elif isinstance(data, list):
                    # 标准格式
                    results[method] = pd.DataFrame(data)
                else:
                    # 其他格式
                    results[method] = data
                
                print(f"✓ 加载 {method}")
            except Exception as e:
                print(f"✗ 加载 {method} 失败: {e}")
        else:
            print(f"⚠ 文件不存在: {file_path}")
    
    return results

def extract_performance_metrics(results_dict):
    """提取性能指标"""
    metrics = {}
    
    for method, data in results_dict.items():
        try:
            if 'Few-Shot BERT' in method:
                # 处理BERT少样本结果
                bert_metrics = {}
                for shot_config, level_results in data.items():
                    if isinstance(level_results, dict) and 'Cat1' in level_results:
                        avg_acc = np.mean([level_results[level]['accuracy'] for level in ['Cat1', 'Cat2', 'Cat3']])
                        avg_f1 = np.mean([level_results[level]['f1_macro'] for level in ['Cat1', 'Cat2', 'Cat3']])
                        bert_metrics[shot_config] = {
                            'accuracy': {
                                'L1': level_results['Cat1']['accuracy'],
                                'L2': level_results['Cat2']['accuracy'],
                                'L3': level_results['Cat3']['accuracy'],
                                'Average': avg_acc
                            },
                            'f1_macro': {
                                'L1': level_results['Cat1']['f1_macro'],
                                'L2': level_results['Cat2']['f1_macro'],
                                'L3': level_results['Cat3']['f1_macro'],
                                'Average': avg_f1
                            }
                        }
                metrics[method] = bert_metrics
                
            elif isinstance(data, pd.DataFrame):
                # 处理标准DataFrame格式
                df = data.copy()
                
                # 标准化预测字段
                pred_cols = [col for col in df.columns if 'pred' in col.lower() and ('l1' in col.lower() or 'l2' in col.lower() or 'l3' in col.lower())]
                
                if len(pred_cols) >= 3:
                    # 找到L1, L2, L3预测字段
                    l1_col = next((col for col in pred_cols if 'l1' in col.lower()), None)
                    l2_col = next((col for col in pred_cols if 'l2' in col.lower()), None)
                    l3_col = next((col for col in pred_cols if 'l3' in col.lower()), None)
                    
                    if all([l1_col, l2_col, l3_col]):
                        # 过滤有效结果
                        valid_mask = (df[l1_col] != 'error') & (df[l1_col].notna())
                        df_valid = df[valid_mask]
                        
                        if len(df_valid) > 0:
                            # 计算准确率
                            acc_l1 = (df_valid[l1_col].str.lower() == df_valid['Cat1'].str.lower()).mean()
                            acc_l2 = (df_valid[l2_col].str.lower() == df_valid['Cat2'].str.lower()).mean()
                            acc_l3 = (df_valid[l3_col].str.lower() == df_valid['Cat3'].str.lower()).mean()
                            
                            # 层次化准确率
                            hierarchical_acc = ((df_valid[l1_col].str.lower() == df_valid['Cat1'].str.lower()) & 
                                               (df_valid[l2_col].str.lower() == df_valid['Cat2'].str.lower()) & 
                                               (df_valid[l3_col].str.lower() == df_valid['Cat3'].str.lower())).mean()
                            
                            # 推理时间
                            avg_time = df_valid.get('inference_time', pd.Series([0])).mean()
                            
                            metrics[method] = {
                                'accuracy': {
                                    'L1': acc_l1,
                                    'L2': acc_l2,
                                    'L3': acc_l3,
                                    'Hierarchical': hierarchical_acc,
                                    'Average': (acc_l1 + acc_l2 + acc_l3) / 3
                                },
                                'statistics': {
                                    'total_samples': len(df),
                                    'valid_samples': len(df_valid),
                                    'success_rate': len(df_valid) / len(df),
                                    'average_inference_time': avg_time
                                }
                            }
            
        except Exception as e:
            print(f"处理 {method} 时出错: {e}")
    
    return metrics

def create_comparison_visualizations(metrics):
    """创建零样本vs少样本对比可视化"""
    if not metrics:
        print("无可用指标数据")
        return
    
    # 分离零样本和少样本方法
    zero_shot_methods = {k: v for k, v in metrics.items() if 'Zero-Shot' in k}
    few_shot_methods = {k: v for k, v in metrics.items() if 'Few-Shot' in k}
    
    # 设置图表样式
    plt.style.use('default')
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. L1准确率对比
    ax1 = axes[0, 0]
    zero_l1 = [v['accuracy']['L1'] for v in zero_shot_methods.values() if 'accuracy' in v]
    few_l1 = [v['accuracy']['L1'] for v in few_shot_methods.values() if 'accuracy' in v]
    
    zero_labels = [k.replace('Zero-Shot ', '') for k in zero_shot_methods.keys()]
    few_labels = [k.replace('Few-Shot ', '') for k in few_shot_methods.keys()]
    
    x_zero = np.arange(len(zero_labels))
    x_few = np.arange(len(few_labels)) + len(zero_labels) + 0.5
    
    bars1 = ax1.bar(x_zero, zero_l1, alpha=0.8, label='Zero-Shot', color='skyblue')
    bars2 = ax1.bar(x_few, few_l1, alpha=0.8, label='Few-Shot', color='lightcoral')
    
    ax1.set_xlabel('Methods')
    ax1.set_ylabel('L1 Accuracy')
    ax1.set_title('L1 Accuracy: Zero-Shot vs Few-Shot')
    ax1.set_xticks(list(x_zero) + list(x_few))
    ax1.set_xticklabels(zero_labels + few_labels, rotation=45, ha='right')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 层次化准确率对比
    ax2 = axes[0, 1]
    zero_hier = [v['accuracy'].get('Hierarchical', 0) for v in zero_shot_methods.values() if 'accuracy' in v]
    few_hier = [v['accuracy'].get('Hierarchical', 0) for v in few_shot_methods.values() if 'accuracy' in v]
    
    bars1 = ax2.bar(x_zero, zero_hier, alpha=0.8, label='Zero-Shot', color='skyblue')
    bars2 = ax2.bar(x_few, few_hier, alpha=0.8, label='Few-Shot', color='lightcoral')
    
    ax2.set_xlabel('Methods')
    ax2.set_ylabel('Hierarchical Accuracy')
    ax2.set_title('Hierarchical Accuracy: Zero-Shot vs Few-Shot')
    ax2.set_xticks(list(x_zero) + list(x_few))
    ax2.set_xticklabels(zero_labels + few_labels, rotation=45, ha='right')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 各级别准确率热力图
    ax3 = axes[1, 0]
    
    # 准备热力图数据
    heatmap_data = []
    method_names = []
    
    for method, metric in metrics.items():
        if 'accuracy' in metric:
            method_names.append(method.replace('Zero-Shot ', 'ZS-').replace('Few-Shot ', 'FS-'))
            heatmap_data.append([
                metric['accuracy']['L1'],
                metric['accuracy']['L2'],
                metric['accuracy']['L3']
            ])
    
    if heatmap_data:
        heatmap_array = np.array(heatmap_data)
        im = ax3.imshow(heatmap_array, cmap='YlOrRd', aspect='auto')
        
        ax3.set_xticks(range(3))
        ax3.set_xticklabels(['L1', 'L2', 'L3'])
        ax3.set_yticks(range(len(method_names)))
        ax3.set_yticklabels(method_names)
        ax3.set_title('Accuracy Heatmap by Level')
        
        # 添加数值标签
        for i in range(len(method_names)):
            for j in range(3):
                text = ax3.text(j, i, f'{heatmap_array[i, j]:.3f}',
                               ha="center", va="center", color="black", fontsize=8)
        
        plt.colorbar(im, ax=ax3)
    
    # 4. 推理时间对比
    ax4 = axes[1, 1]
    zero_time = [v['statistics'].get('average_inference_time', 0) for v in zero_shot_methods.values() if 'statistics' in v]
    few_time = [v['statistics'].get('average_inference_time', 0) for v in few_shot_methods.values() if 'statistics' in v]
    
    bars1 = ax4.bar(x_zero, zero_time, alpha=0.8, label='Zero-Shot', color='skyblue')
    bars2 = ax4.bar(x_few, few_time, alpha=0.8, label='Few-Shot', color='lightcoral')
    
    ax4.set_xlabel('Methods')
    ax4.set_ylabel('Average Inference Time (seconds)')
    ax4.set_title('Inference Time: Zero-Shot vs Few-Shot')
    ax4.set_xticks(list(x_zero) + list(x_few))
    ax4.set_xticklabels(zero_labels + few_labels, rotation=45, ha='right')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('nasa_topics_zeroshot_vs_fewshot_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✓ 零样本vs少样本对比图表已保存: nasa_topics_zeroshot_vs_fewshot_comparison.png")

def generate_comparison_report(metrics):
    """生成零样本vs少样本对比报告"""
    if not metrics:
        print("无可用指标数据")
        return
    
    report = []
    report.append("# NASA Topics零样本vs少样本学习对比报告")
    report.append("=" * 60)
    report.append("")
    
    # 分离方法
    zero_shot_methods = {k: v for k, v in metrics.items() if 'Zero-Shot' in k}
    few_shot_methods = {k: v for k, v in metrics.items() if 'Few-Shot' in k}
    
    # 性能对比表格
    report.append("## 详细性能对比")
    report.append("")
    report.append("| 方法类型 | 方法名称 | L1准确率 | L2准确率 | L3准确率 | 层次化准确率 | 平均准确率 | 推理时间(s) |")
    report.append("|----------|----------|----------|----------|----------|--------------|------------|------------|")
    
    # 零样本方法
    for method, metric in zero_shot_methods.items():
        if 'accuracy' in metric:
            acc = metric['accuracy']
            time_val = metric.get('statistics', {}).get('average_inference_time', 0)
            method_name = method.replace('Zero-Shot ', '')
            
            report.append(f"| 零样本 | {method_name} | {acc['L1']:.4f} | {acc['L2']:.4f} | "
                         f"{acc['L3']:.4f} | {acc.get('Hierarchical', 0):.4f} | "
                         f"{acc.get('Average', 0):.4f} | {time_val:.2f} |")
    
    # 少样本方法
    for method, metric in few_shot_methods.items():
        if 'accuracy' in metric:
            acc = metric['accuracy']
            time_val = metric.get('statistics', {}).get('average_inference_time', 0)
            method_name = method.replace('Few-Shot ', '')
            
            report.append(f"| 少样本 | {method_name} | {acc['L1']:.4f} | {acc['L2']:.4f} | "
                         f"{acc['L3']:.4f} | {acc.get('Hierarchical', 0):.4f} | "
                         f"{acc.get('Average', 0):.4f} | {time_val:.2f} |")
    
    report.append("")
    
    # 关键发现
    report.append("## 关键发现")
    report.append("")
    
    # 找到最佳方法
    all_hierarchical = [(k, v['accuracy'].get('Hierarchical', 0)) for k, v in metrics.items() if 'accuracy' in v]
    if all_hierarchical:
        best_method, best_acc = max(all_hierarchical, key=lambda x: x[1])
        report.append(f"1. **最佳整体性能**: {best_method} (层次化准确率: {best_acc:.4f})")
    
    # 零样本vs少样本对比
    if zero_shot_methods and few_shot_methods:
        zero_avg = np.mean([v['accuracy'].get('Hierarchical', 0) for v in zero_shot_methods.values() if 'accuracy' in v])
        few_avg = np.mean([v['accuracy'].get('Hierarchical', 0) for v in few_shot_methods.values() if 'accuracy' in v])
        
        improvement = ((few_avg - zero_avg) / zero_avg * 100) if zero_avg > 0 else 0
        
        report.append(f"2. **少样本学习提升**: 平均层次化准确率从 {zero_avg:.4f} 提升到 {few_avg:.4f} ({improvement:+.1f}%)")
    
    # KG-HTC方法对比
    kg_htc_zero = next((v for k, v in zero_shot_methods.items() if 'KG-HTC' in k), None)
    kg_htc_few = [v for k, v in few_shot_methods.items() if 'KG-HTC' in k]
    
    if kg_htc_zero and kg_htc_few:
        zero_hier = kg_htc_zero['accuracy'].get('Hierarchical', 0)
        few_hier_max = max([v['accuracy'].get('Hierarchical', 0) for v in kg_htc_few])
        kg_improvement = ((few_hier_max - zero_hier) / zero_hier * 100) if zero_hier > 0 else 0
        
        report.append(f"3. **KG-HTC方法提升**: 从零样本 {zero_hier:.4f} 到少样本 {few_hier_max:.4f} ({kg_improvement:+.1f}%)")
    
    # 效率分析
    zero_times = [v.get('statistics', {}).get('average_inference_time', 0) for v in zero_shot_methods.values()]
    few_times = [v.get('statistics', {}).get('average_inference_time', 0) for v in few_shot_methods.values()]
    
    if zero_times and few_times:
        zero_avg_time = np.mean([t for t in zero_times if t > 0])
        few_avg_time = np.mean([t for t in few_times if t > 0])
        
        if zero_avg_time > 0 and few_avg_time > 0:
            time_ratio = few_avg_time / zero_avg_time
            report.append(f"4. **推理效率**: 少样本方法平均推理时间是零样本的 {time_ratio:.1f} 倍")
    
    # 保存报告
    report_text = "\n".join(report)
    with open("nasa_topics_zeroshot_vs_fewshot_report.md", 'w', encoding='utf-8') as f:
        f.write(report_text)
    
    print("✓ 零样本vs少样本对比报告已保存: nasa_topics_zeroshot_vs_fewshot_report.md")
    
    return report_text

def main():
    """主函数"""
    print("=== NASA Topics零样本vs少样本学习对比评估 ===")
    
    # 加载所有结果
    results_dict = load_all_fewshot_results()
    
    if not results_dict:
        print("未找到任何实验结果文件")
        return
    
    # 提取性能指标
    metrics = extract_performance_metrics(results_dict)
    
    if not metrics:
        print("无法提取任何性能指标")
        return
    
    print(f"\n成功提取 {len(metrics)} 个方法的性能指标")
    
    # 创建可视化
    create_comparison_visualizations(metrics)
    
    # 生成报告
    report = generate_comparison_report(metrics)
    
    # 保存完整指标
    with open("nasa_topics_zeroshot_vs_fewshot_metrics.json", 'w', encoding='utf-8') as f:
        json.dump(metrics, f, indent=2, ensure_ascii=False)
    
    print("\n✓ 零样本vs少样本对比评估完成")
    print("生成文件:")
    print("  - nasa_topics_zeroshot_vs_fewshot_comparison.png")
    print("  - nasa_topics_zeroshot_vs_fewshot_report.md")
    print("  - nasa_topics_zeroshot_vs_fewshot_metrics.json")

if __name__ == "__main__":
    main()
