import pandas as pd
import numpy as np
from pathlib import Path
import sys
import json
import torch
from transformers import (
    AutoTokenizer, AutoModelForSequenceClassification, 
    Trainer, TrainingArguments, DataCollatorWithPadding
)
from sklearn.metrics import accuracy_score, f1_score, classification_report
from sklearn.preprocessing import LabelEncoder
from sklearn.model_selection import train_test_split
from torch.utils.data import Dataset
import time
import warnings
warnings.filterwarnings('ignore')

# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

class NASADataset(Dataset):
    """NASA数据集类，用于BERT训练"""
    
    def __init__(self, texts, labels, tokenizer, max_length=512):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        
        # 文本编码
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }

def create_few_shot_dataset(df, shots_per_class=5, random_state=42):
    """
    创建少样本数据集
    
    Args:
        df: 完整数据集
        shots_per_class: 每个类别的样本数
        random_state: 随机种子
    """
    few_shot_samples = []
    
    # 对每个级别分别采样
    for level in ['Cat1', 'Cat2', 'Cat3']:
        level_samples = []
        unique_classes = df[level].unique()
        
        print(f"{level} 类别数: {len(unique_classes)}")
        
        for class_name in unique_classes:
            class_samples = df[df[level] == class_name]
            
            # 如果该类别样本数少于shots_per_class，使用所有样本
            n_samples = min(shots_per_class, len(class_samples))
            
            if n_samples > 0:
                sampled = class_samples.sample(n=n_samples, random_state=random_state)
                level_samples.append(sampled)
        
        if level_samples:
            level_df = pd.concat(level_samples, ignore_index=True)
            few_shot_samples.append(level_df)
    
    return few_shot_samples

def train_bert_classifier(train_texts, train_labels, val_texts, val_labels, 
                         model_name="distilbert-base-uncased", num_epochs=3):
    """
    训练BERT分类器
    
    Args:
        train_texts: 训练文本
        train_labels: 训练标签
        val_texts: 验证文本
        val_labels: 验证标签
        model_name: 预训练模型名称
        num_epochs: 训练轮数
    """
    
    # 初始化tokenizer和模型
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    num_labels = len(np.unique(train_labels))
    model = AutoModelForSequenceClassification.from_pretrained(
        model_name, 
        num_labels=num_labels
    )
    
    # 创建数据集
    train_dataset = NASADataset(train_texts, train_labels, tokenizer)
    val_dataset = NASADataset(val_texts, val_labels, tokenizer)
    
    # 数据整理器
    data_collator = DataCollatorWithPadding(tokenizer=tokenizer)
    
    # 训练参数
    training_args = TrainingArguments(
        output_dir='./results',
        num_train_epochs=num_epochs,
        per_device_train_batch_size=8,
        per_device_eval_batch_size=16,
        warmup_steps=100,
        weight_decay=0.01,
        logging_dir='./logs',
        logging_steps=10,
        evaluation_strategy="epoch",
        save_strategy="epoch",
        load_best_model_at_end=True,
        metric_for_best_model="eval_loss",
        greater_is_better=False,
        remove_unused_columns=False,
    )
    
    # 定义评估函数
    def compute_metrics(eval_pred):
        predictions, labels = eval_pred
        predictions = np.argmax(predictions, axis=1)
        return {
            'accuracy': accuracy_score(labels, predictions),
            'f1': f1_score(labels, predictions, average='macro', zero_division=0)
        }
    
    # 初始化训练器
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=val_dataset,
        tokenizer=tokenizer,
        data_collator=data_collator,
        compute_metrics=compute_metrics,
    )
    
    # 训练模型
    print("开始训练...")
    trainer.train()
    
    return trainer, tokenizer

def run_fewshot_bert_experiment():
    """
    运行少样本BERT实验
    """
    
    print("=== NASA Topics少样本BERT实验 ===")
    
    # 读取数据
    train_path = "dataset/nasa_topics/nasa_topics_train.csv"
    test_path = "dataset/nasa_topics/nasa_topics_val.csv"
    
    try:
        train_df = pd.read_csv(train_path)
        test_df = pd.read_csv(test_path)
        print(f"训练集: {len(train_df)} 条记录")
        print(f"测试集: {len(test_df)} 条记录")
    except Exception as e:
        print(f"读取数据失败: {e}")
        return
    
    # 数据预处理
    def preprocess_data(df):
        df = df.dropna(subset=['Text', 'Cat1', 'Cat2', 'Cat3'])
        df = df[df['Cat1'] != "unknown"]
        df = df[df['Cat2'] != "unknown"]
        df = df[df['Cat3'] != "unknown"]
        return df
    
    train_df = preprocess_data(train_df)
    test_df = preprocess_data(test_df)
    
    # 限制数据量进行快速实验
    if len(test_df) > 200:
        test_df = test_df.sample(n=200, random_state=42)
    
    print(f"预处理后 - 训练集: {len(train_df)}, 测试集: {len(test_df)}")
    
    # 不同的少样本设置
    few_shot_configs = [
        {"shots_per_class": 1, "name": "1-shot"},
        {"shots_per_class": 3, "name": "3-shot"},
        {"shots_per_class": 5, "name": "5-shot"},
        {"shots_per_class": 10, "name": "10-shot"}
    ]
    
    all_results = {}
    
    for config in few_shot_configs:
        print(f"\n=== {config['name']} 实验 ===")
        
        try:
            # 创建少样本数据集
            few_shot_datasets = create_few_shot_dataset(
                train_df, 
                shots_per_class=config["shots_per_class"]
            )
            
            # 对每个级别训练分类器
            level_results = {}
            
            for level_idx, (level, few_shot_df) in enumerate(zip(['Cat1', 'Cat2', 'Cat3'], few_shot_datasets)):
                print(f"\n训练 {level} 分类器...")
                print(f"训练样本数: {len(few_shot_df)}")
                
                # 准备训练数据
                train_texts = few_shot_df['Text'].tolist()
                train_labels_raw = few_shot_df[level].tolist()
                
                # 标签编码
                le = LabelEncoder()
                train_labels = le.fit_transform(train_labels_raw)
                
                # 准备测试数据
                test_texts = test_df['Text'].tolist()
                test_labels_raw = test_df[level].tolist()
                
                # 过滤测试集中训练时未见过的标签
                valid_test_mask = test_df[level].isin(le.classes_)
                valid_test_df = test_df[valid_test_mask]
                
                if len(valid_test_df) == 0:
                    print(f"⚠ {level} 无有效测试样本")
                    continue
                
                valid_test_texts = valid_test_df['Text'].tolist()
                valid_test_labels = le.transform(valid_test_df[level].tolist())
                
                print(f"有效测试样本数: {len(valid_test_df)}")
                print(f"类别数: {len(le.classes_)}")
                
                # 创建验证集
                if len(train_texts) > 20:
                    train_texts_split, val_texts, train_labels_split, val_labels = train_test_split(
                        train_texts, train_labels, test_size=0.2, random_state=42, stratify=train_labels
                    )
                else:
                    # 样本太少时不分割验证集
                    train_texts_split, val_texts = train_texts, train_texts[:5]
                    train_labels_split, val_labels = train_labels, train_labels[:5]
                
                # 训练模型
                start_time = time.time()
                trainer, tokenizer = train_bert_classifier(
                    train_texts_split, train_labels_split,
                    val_texts, val_labels,
                    num_epochs=3
                )
                training_time = time.time() - start_time
                
                # 预测
                print("进行预测...")
                predictions = trainer.predict(NASADataset(valid_test_texts, valid_test_labels, tokenizer))
                pred_labels = np.argmax(predictions.predictions, axis=1)
                
                # 计算指标
                accuracy = accuracy_score(valid_test_labels, pred_labels)
                f1 = f1_score(valid_test_labels, pred_labels, average='macro', zero_division=0)
                
                print(f"{level} 结果: 准确率={accuracy:.4f}, F1={f1:.4f}")
                
                # 保存结果
                level_results[level] = {
                    'accuracy': accuracy,
                    'f1_macro': f1,
                    'training_time': training_time,
                    'train_samples': len(train_texts),
                    'test_samples': len(valid_test_df),
                    'num_classes': len(le.classes_),
                    'predictions': le.inverse_transform(pred_labels).tolist(),
                    'true_labels': valid_test_df[level].tolist()
                }
                
                # 清理GPU内存
                del trainer
                torch.cuda.empty_cache() if torch.cuda.is_available() else None
                
        except Exception as e:
            print(f"✗ {config['name']} 实验失败: {e}")
            continue
        
        # 保存配置结果
        all_results[config['name']] = level_results
        
        # 计算整体性能
        if level_results:
            avg_accuracy = np.mean([r['accuracy'] for r in level_results.values()])
            avg_f1 = np.mean([r['f1_macro'] for r in level_results.values()])
            total_training_time = sum([r['training_time'] for r in level_results.values()])
            
            print(f"\n{config['name']} 整体结果:")
            print(f"  平均准确率: {avg_accuracy:.4f}")
            print(f"  平均F1: {avg_f1:.4f}")
            print(f"  总训练时间: {total_training_time:.2f}秒")
    
    # 保存所有结果
    output_path = "dataset/nasa_topics/fewshot_bert_results.json"
    try:
        with open(output_path, "w", encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)
        print(f"\n✓ 少样本BERT结果已保存: {output_path}")
    except Exception as e:
        print(f"✗ 保存结果失败: {e}")
    
    # 性能对比分析
    print(f"\n=== 少样本学习性能对比 ===")
    for config_name, results in all_results.items():
        if results:
            print(f"\n{config_name}:")
            for level, metrics in results.items():
                print(f"  {level}: 准确率={metrics['accuracy']:.4f}, F1={metrics['f1_macro']:.4f}")

if __name__ == "__main__":
    run_fewshot_bert_experiment()
