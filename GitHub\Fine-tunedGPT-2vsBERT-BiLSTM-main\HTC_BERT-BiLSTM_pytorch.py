#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
层次文本分类 (Hierarchical Text Classification) - BERT-BiLSTM 模型实现 (PyTorch版本) - 增强版 + WandB集成

该脚本实现了一个基于BERT和双向LSTM的层次文本分类模型，用于同时预测文本的类别和超类别。
模型结构：BERT编码器 + 双向LSTM + 全连接层
训练原理：多任务学习，同时优化类别分类和超类别分类任务

增强功能：
1. 层级约束损失函数（软约束+硬约束）
2. 三阶段训练策略
3. 全面的层级评估指标
4. 后处理层级一致性校正
5. WandB实验跟踪和可视化

1. 增强的层级约束损失函数
结合软约束（KL散度）和硬约束（预测一致性检查）
可调节的权重参数  alpha 和 beta
详细的损失组件记录
2. 三阶段训练策略
阶段1：超类别预训练（冻结细分类层）
阶段2：细分类微调（冻结超类别层）
阶段3：层级约束联合训练（使用增强损失函数）
3. 全面的层级评估指标
严格层级准确率
层级一致性率
加权层级F1分数
层级距离惩罚分数
4. 后处理层级一致性校正
Top-k候选搜索
组合得分计算
自动校正不一致的预测
"""

import pandas as pd
import numpy as np
import nltk
import re
import string
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, f1_score
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from transformers import BertTokenizer, BertModel
from tqdm import tqdm
import copy
import os
from datetime import datetime

# WandB导入和配置
import wandb

# ==================== WandB初始化 ====================
os.environ["WANDB_MODE"] = "online"

# 初始化WandB项目
wandb.init(
    project="hierarchical-bert-bilstm-classification",
    name=f"bert-bilstm-htc-{datetime.now().strftime('%Y%m%d-%H%M%S')}",
    config={
        # 模型超参数
        "model_name": "bert-base-uncased",
        "lstm_hidden_size": 128,
        "max_length": 128,
        "dropout_rate": 0.1,
        
        # 训练超参数
        "batch_size": 16,
        "stage1_epochs": 2,
        "stage2_epochs": 2,
        "stage3_epochs": 3,
        "stage1_lr": 3e-5,
        "stage2_lr": 2e-5,
        "stage3_lr": 1e-5,
        
        # 损失函数权重
        "hierarchical_loss_alpha": 0.5,  # 软约束权重
        "hierarchical_loss_beta": 0.3,   # 硬约束权重
        
        # 数据集配置
        "test_size": 0.3,
        "random_state": 42,
        
        # 训练策略
        "training_strategy": "three_stage",
        "use_post_processing": True,
        "use_hierarchical_constraint": True,
    },
    tags=["hierarchical-classification", "bert", "bilstm", "pytorch", "three-stage-training"],
    notes="Enhanced BERT-BiLSTM model with hierarchical constraints and three-stage training strategy"
)

config = wandb.config

# 设备配置
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {device}")

# 记录设备信息
wandb.log({"device": str(device), "cuda_available": torch.cuda.is_available()})

# 下载NLTK数据包
nltk.download('punkt')
nltk.download('stopwords')

def preprocess_text(text):
    """文本预处理函数"""
    text = text.lower()
    text = re.sub(f'[{string.punctuation}]', '', text)
    tokens = word_tokenize(text)
    stop_words = set(stopwords.words('english'))
    tokens = [word for word in tokens if word not in stop_words]
    return ' '.join(tokens)

# 自定义数据集类
class TextDataset(Dataset):
    def __init__(self, texts, categories, super_categories, tokenizer, max_length=128):
        self.texts = texts
        self.categories = categories
        self.super_categories = super_categories
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'category': torch.tensor(self.categories[idx], dtype=torch.long),
            'super_category': torch.tensor(self.super_categories[idx], dtype=torch.long)
        }

# BERT-BiLSTM模型定义
class BertBiLSTMClassifier(nn.Module):
    def __init__(self, num_categories, num_super_categories, lstm_hidden_size=128):
        super(BertBiLSTMClassifier, self).__init__()
        
        # BERT模型
        self.bert = BertModel.from_pretrained('bert-base-uncased')
        
        # 双向LSTM
        self.lstm = nn.LSTM(
            input_size=768,  # BERT hidden size
            hidden_size=lstm_hidden_size,
            num_layers=1,
            batch_first=True,
            bidirectional=True
        )
        
        # 全局最大池化
        self.global_max_pool = nn.AdaptiveMaxPool1d(1)
        
        # 分类头
        lstm_output_size = lstm_hidden_size * 2  # 双向LSTM
        self.category_classifier = nn.Linear(lstm_output_size, num_categories)
        self.super_category_classifier = nn.Linear(lstm_output_size, num_super_categories)
        
        # Dropout
        self.dropout = nn.Dropout(config.dropout_rate)
    
    def forward(self, input_ids, attention_mask):
        # BERT编码
        bert_output = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        sequence_output = bert_output.last_hidden_state  # (batch_size, seq_len, 768)
        
        # BiLSTM
        lstm_output, _ = self.lstm(sequence_output)  # (batch_size, seq_len, lstm_hidden_size*2)
        
        # 全局最大池化
        lstm_output = lstm_output.transpose(1, 2)  # (batch_size, lstm_hidden_size*2, seq_len)
        pooled_output = self.global_max_pool(lstm_output).squeeze(-1)  # (batch_size, lstm_hidden_size*2)
        
        # Dropout
        pooled_output = self.dropout(pooled_output)
        
        # 分类输出
        category_logits = self.category_classifier(pooled_output)
        super_category_logits = self.super_category_classifier(pooled_output)
        
        return category_logits, super_category_logits

def enhanced_hierarchical_constraint_loss(category_logits, super_category_logits, 
                                        category_labels, super_category_labels, 
                                        category_to_super_idx, alpha=0.5, beta=0.3):
    """
    增强的层级约束损失函数
    
    参数:
    - alpha: 软约束权重 (KL散度)
    - beta: 硬约束权重 (预测一致性)
    """
    ce_loss = nn.CrossEntropyLoss()
    cat_loss = ce_loss(category_logits, category_labels)
    super_cat_loss = ce_loss(super_category_logits, super_category_labels)
    
    batch_size = category_logits.size(0)
    soft_consistency_loss = 0
    hard_consistency_loss = 0
    
    for i in range(batch_size):
        cat_probs = torch.softmax(category_logits[i], dim=0)
        super_cat_probs = torch.softmax(super_category_logits[i], dim=0)
        
        # 软约束：KL散度
        expected_super_probs = torch.zeros_like(super_cat_probs)
        for cat_idx, super_idx in category_to_super_idx.items():
            if cat_idx < len(cat_probs):
                expected_super_probs[super_idx] += cat_probs[cat_idx]
        
        # 归一化
        if expected_super_probs.sum() > 0:
            expected_super_probs = expected_super_probs / expected_super_probs.sum()
        
        soft_consistency_loss += torch.nn.functional.kl_div(
            torch.log(super_cat_probs + 1e-8), expected_super_probs, reduction='sum'
        )
        
        # 硬约束：预测类别与超类别必须匹配
        pred_cat = torch.argmax(cat_probs).item()
        pred_super = torch.argmax(super_cat_probs).item()
        expected_super = category_to_super_idx.get(pred_cat, -1)
        
        if expected_super != -1 and pred_super != expected_super:
            hard_consistency_loss += 1.0
    
    # 归一化损失
    soft_consistency_loss = soft_consistency_loss / batch_size
    hard_consistency_loss = hard_consistency_loss / batch_size
    
    total_loss = cat_loss + super_cat_loss + alpha * soft_consistency_loss + beta * hard_consistency_loss
    
    return total_loss, {
        'category_loss': cat_loss.item(),
        'super_category_loss': super_cat_loss.item(),
        'soft_consistency_loss': soft_consistency_loss.item(),
        'hard_consistency_loss': hard_consistency_loss.item(),
        'total_loss': total_loss.item()
    }

def post_process_predictions(category_logits, super_category_logits, category_to_super_idx):
    """
    后处理：确保预测的层级一致性
    """
    batch_size = category_logits.size(0)
    corrected_predictions = []
    
    for i in range(batch_size):
        cat_probs = torch.softmax(category_logits[i], dim=0)
        super_cat_probs = torch.softmax(super_category_logits[i], dim=0)
        
        # 获取top-k候选
        top_k_cats = torch.topk(cat_probs, k=min(3, len(cat_probs)))
        top_k_supers = torch.topk(super_cat_probs, k=min(2, len(super_cat_probs)))
        
        # 寻找一致的预测组合
        best_score = -1
        best_cat, best_super = None, None
        
        for cat_idx in top_k_cats.indices:
            expected_super = category_to_super_idx.get(cat_idx.item(), -1)
            if expected_super != -1 and expected_super in top_k_supers.indices:
                # 计算组合得分
                cat_score = cat_probs[cat_idx].item()
                super_score = super_cat_probs[expected_super].item()
                combined_score = cat_score * super_score
                
                if combined_score > best_score:
                    best_score = combined_score
                    best_cat = cat_idx.item()
                    best_super = expected_super
        
        # 如果找不到一致的组合，使用原始预测
        if best_cat is None:
            best_cat = torch.argmax(cat_probs).item()
            best_super = torch.argmax(super_cat_probs).item()
        
        corrected_predictions.append((best_cat, best_super))
    
    return corrected_predictions

def comprehensive_hierarchical_metrics(y_true_cat, y_pred_cat, y_true_super_cat, y_pred_super_cat, 
                                     category_to_super_idx):
    """
    全面的层级评估指标
    """
    
    # 1. 严格层级准确率
    strict_correct = (np.array(y_true_cat) == np.array(y_pred_cat)) & (np.array(y_true_super_cat) == np.array(y_pred_super_cat))
    strict_accuracy = np.mean(strict_correct)
    
    # 2. 层级一致性率
    consistency_correct = []
    for i in range(len(y_pred_cat)):
        pred_cat = y_pred_cat[i]
        pred_super = y_pred_super_cat[i]
        expected_super = category_to_super_idx.get(pred_cat, -1)
        consistency_correct.append(pred_super == expected_super)
    consistency_rate = np.mean(consistency_correct)
    
    # 3. 加权层级F1分数
    weights = {'super': 0.3, 'category': 0.7}  # 更重视细分类
    super_f1 = f1_score(y_true_super_cat, y_pred_super_cat, average='weighted')
    cat_f1 = f1_score(y_true_cat, y_pred_cat, average='weighted')
    weighted_hierarchical_f1 = weights['super'] * super_f1 + weights['category'] * cat_f1
    
    # 4. 层级距离惩罚
    penalties = []
    for i in range(len(y_true_cat)):
        if y_true_cat[i] == y_pred_cat[i] and y_true_super_cat[i] == y_pred_super_cat[i]:
            penalties.append(0)  # 完全正确
        elif y_true_super_cat[i] == y_pred_super_cat[i]:
            penalties.append(0.5)  # 超类别正确
        else:
            penalties.append(1.0)  # 完全错误
    hierarchical_distance_score = 1 - np.mean(penalties)
    
    return {
        'strict_hierarchical_accuracy': strict_accuracy,
        'consistency_rate': consistency_rate,
        'weighted_hierarchical_f1': weighted_hierarchical_f1,
        'hierarchical_distance_score': hierarchical_distance_score,
        'category_accuracy': accuracy_score(y_true_cat, y_pred_cat),
        'super_category_accuracy': accuracy_score(y_true_super_cat, y_pred_super_cat),
        'category_f1': cat_f1,
        'super_category_f1': super_f1
    }

def train_epoch_stage1(model, dataloader, optimizer, criterion, device, epoch):
    """阶段1训练：仅训练超类别分类器"""
    model.train()
    total_loss = 0
    super_category_correct = 0
    total_samples = 0
    
    progress_bar = tqdm(dataloader, desc=f"Stage 1 - Epoch {epoch+1}")
    
    for batch_idx, batch in enumerate(progress_bar):
        input_ids = batch['input_ids'].to(device)
        attention_mask = batch['attention_mask'].to(device)
        super_categories = batch['super_category'].to(device)
        
        optimizer.zero_grad()
        
        _, super_category_logits = model(input_ids, attention_mask)
        loss = criterion(super_category_logits, super_categories)
        
        loss.backward()
        optimizer.step()
        
        total_loss += loss.item()
        
        # 计算准确率
        super_category_pred = torch.argmax(super_category_logits, dim=1)
        super_category_correct += (super_category_pred == super_categories).sum().item()
        total_samples += super_categories.size(0)
        
        # 实时更新进度条
        current_acc = super_category_correct / total_samples
        progress_bar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Acc': f'{current_acc:.4f}'
        })
        
        # 记录批次级别的指标
        if batch_idx % 50 == 0:
            wandb.log({
                "stage1/batch_loss": loss.item(),
                "stage1/batch_accuracy": current_acc,
                "stage1/step": epoch * len(dataloader) + batch_idx
            })
    
    avg_loss = total_loss / len(dataloader)
    super_category_acc = super_category_correct / total_samples
    
    return avg_loss, super_category_acc

def train_epoch_stage2(model, dataloader, optimizer, criterion, device, epoch):
    """阶段2训练：仅训练类别分类器"""
    model.train()
    total_loss = 0
    category_correct = 0
    total_samples = 0
    
    progress_bar = tqdm(dataloader, desc=f"Stage 2 - Epoch {epoch+1}")
    
    for batch_idx, batch in enumerate(progress_bar):
        input_ids = batch['input_ids'].to(device)
        attention_mask = batch['attention_mask'].to(device)
        categories = batch['category'].to(device)
        
        optimizer.zero_grad()
        
        category_logits, _ = model(input_ids, attention_mask)
        loss = criterion(category_logits, categories)
        
        loss.backward()
        optimizer.step()
        
        total_loss += loss.item()
        
        # 计算准确率
        category_pred = torch.argmax(category_logits, dim=1)
        category_correct += (category_pred == categories).sum().item()
        total_samples += categories.size(0)
        
        # 实时更新进度条
        current_acc = category_correct / total_samples
        progress_bar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Acc': f'{current_acc:.4f}'
        })
        
        # 记录批次级别的指标
        if batch_idx % 50 == 0:
            wandb.log({
                "stage2/batch_loss": loss.item(),
                "stage2/batch_accuracy": current_acc,
                "stage2/step": epoch * len(dataloader) + batch_idx
            })
    
    avg_loss = total_loss / len(dataloader)
    category_acc = category_correct / total_samples
    
    return avg_loss, category_acc

def train_epoch_stage3(model, dataloader, optimizer, category_to_super_idx, device, epoch, alpha=0.5, beta=0.3):
    """阶段3训练：层级约束联合训练"""
    model.train()
    total_loss = 0
    category_correct = 0
    super_category_correct = 0
    total_samples = 0
    
    # 累积损失组件
    total_loss_components = {
        'category_loss': 0,
        'super_category_loss': 0,
        'soft_consistency_loss': 0,
        'hard_consistency_loss': 0
    }
    
    progress_bar = tqdm(dataloader, desc=f"Stage 3 - Epoch {epoch+1}")
    
    for batch_idx, batch in enumerate(progress_bar):
        input_ids = batch['input_ids'].to(device)
        attention_mask = batch['attention_mask'].to(device)
        categories = batch['category'].to(device)
        super_categories = batch['super_category'].to(device)
        
        optimizer.zero_grad()
        
        category_logits, super_category_logits = model(input_ids, attention_mask)
        
        # 使用增强的层级约束损失
        total_loss_batch, loss_components = enhanced_hierarchical_constraint_loss(
            category_logits, super_category_logits,
            categories, super_categories,
            category_to_super_idx, alpha, beta
        )
        
        total_loss_batch.backward()
        optimizer.step()
        
        total_loss += total_loss_batch.item()
        
        # 累积损失组件
        for key in total_loss_components:
            total_loss_components[key] += loss_components[key]
        
        # 计算准确率
        category_pred = torch.argmax(category_logits, dim=1)
        super_category_pred = torch.argmax(super_category_logits, dim=1)
        
        category_correct += (category_pred == categories).sum().item()
        super_category_correct += (super_category_pred == super_categories).sum().item()
        total_samples += categories.size(0)
        
        # 实时更新进度条
        current_cat_acc = category_correct / total_samples
        current_super_acc = super_category_correct / total_samples
        progress_bar.set_postfix({
            'Loss': f'{total_loss_batch.item():.4f}',
            'Cat_Acc': f'{current_cat_acc:.4f}',
            'Super_Acc': f'{current_super_acc:.4f}'
        })
        
        # 记录批次级别的指标
        if batch_idx % 50 == 0:
            wandb.log({
                "stage3/batch_total_loss": total_loss_batch.item(),
                "stage3/batch_category_loss": loss_components['category_loss'],
                "stage3/batch_super_category_loss": loss_components['super_category_loss'],
                "stage3/batch_soft_consistency_loss": loss_components['soft_consistency_loss'],
                "stage3/batch_hard_consistency_loss": loss_components['hard_consistency_loss'],
                "stage3/batch_category_accuracy": current_cat_acc,
                "stage3/batch_super_category_accuracy": current_super_acc,
                "stage3/step": epoch * len(dataloader) + batch_idx
            })
    
    avg_loss = total_loss / len(dataloader)
    category_acc = category_correct / total_samples
    super_category_acc = super_category_correct / total_samples
    
    # 平均损失组件
    avg_loss_components = {key: value / len(dataloader) for key, value in total_loss_components.items()}
    
    return avg_loss, category_acc, super_category_acc, avg_loss_components

def evaluate_enhanced(model, dataloader, category_to_super_idx, device, use_post_processing=True):
    """增强的评估函数"""
    model.eval()
    category_preds = []
    super_category_preds = []
    category_true = []
    super_category_true = []
    
    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Evaluating"):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            categories = batch['category'].to(device)
            super_categories = batch['super_category'].to(device)
            
            category_logits, super_category_logits = model(input_ids, attention_mask)
            
            if use_post_processing:
                # 使用后处理校正
                corrected_preds = post_process_predictions(category_logits, super_category_logits, category_to_super_idx)
                batch_cat_preds = [pred[0] for pred in corrected_preds]
                batch_super_preds = [pred[1] for pred in corrected_preds]
            else:
                # 原始预测
                batch_cat_preds = torch.argmax(category_logits, dim=1).cpu().numpy()
                batch_super_preds = torch.argmax(super_category_logits, dim=1).cpu().numpy()
            
            category_preds.extend(batch_cat_preds)
            super_category_preds.extend(batch_super_preds)
            category_true.extend(categories.cpu().numpy())
            super_category_true.extend(super_categories.cpu().numpy())
    
    return category_preds, super_category_preds, category_true, super_category_true

# 主训练流程
def main():
    # 加载数据
    print("加载数据集...")
    data = pd.read_csv('train_40k_Adapted.csv')
    data['processed_text'] = data['text'].apply(preprocess_text)
    
    # 记录数据集统计信息
    dataset_stats = {
        "dataset_size": len(data),
        "num_categories": data['category'].nunique(),
        "num_super_categories": data['super_category'].nunique(),
        "avg_text_length": data['text'].str.len().mean(),
        "max_text_length": data['text'].str.len().max(),
        "min_text_length": data['text'].str.len().min()
    }
    
    wandb.log(dataset_stats)
    print(f"数据集统计: {dataset_stats}")
    
    # 创建标签映射
    category_labels = list(data['category'].unique())
    super_category_labels = list(data['super_category'].unique())
    
    category_to_idx = {label: idx for idx, label in enumerate(category_labels)}
    super_category_to_idx = {label: idx for idx, label in enumerate(super_category_labels)}
    
    # 创建类别到超类别的映射
    category_to_super_idx = {}
    for _, row in data.iterrows():
        cat_idx = category_to_idx[row['category']]
        super_idx = super_category_to_idx[row['super_category']]
        category_to_super_idx[cat_idx] = super_idx
    
    # 转换标签为索引
    data['category_idx'] = data['category'].map(category_to_idx)
    data['super_category_idx'] = data['super_category'].map(super_category_to_idx)
    
    # 数据集划分
    train_texts, test_texts, train_cats, test_cats, train_super_cats, test_super_cats = train_test_split(
        data['processed_text'].values,
        data['category_idx'].values,
        data['super_category_idx'].values,
        test_size=config.test_size,
        random_state=config.random_state
    )
    
    # 记录数据划分信息
    split_info = {
        "train_size": len(train_texts),
        "test_size": len(test_texts),
        "train_ratio": len(train_texts) / len(data),
        "test_ratio": len(test_texts) / len(data)
    }
    wandb.log(split_info)
    
    # 初始化分词器
    tokenizer = BertTokenizer.from_pretrained(config.model_name)
    
    # 创建数据集和数据加载器
    train_dataset = TextDataset(train_texts, train_cats, train_super_cats, tokenizer, config.max_length)
    test_dataset = TextDataset(test_texts, test_cats, test_super_cats, tokenizer, config.max_length)
    
    train_loader = DataLoader(train_dataset, batch_size=config.batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=config.batch_size, shuffle=False)
    
    # 初始化模型
    model = BertBiLSTMClassifier(
        num_categories=len(category_labels),
        num_super_categories=len(super_category_labels),
        lstm_hidden_size=config.lstm_hidden_size
    ).to(device)
    
    # 记录模型参数信息
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    model_info = {
        "total_parameters": total_params,
        "trainable_parameters": trainable_params,
        "model_size_mb": total_params * 4 / (1024 * 1024),  # 假设float32
        "bert_parameters": sum(p.numel() for p in model.bert.parameters()),
        "lstm_parameters": sum(p.numel() for p in model.lstm.parameters()),
        "classifier_parameters": sum(p.numel() for p in model.category_classifier.parameters()) + 
                                sum(p.numel() for p in model.super_category_classifier.parameters())
    }
    
    wandb.log(model_info)
    print(f"模型参数统计: {model_info}")
    
    # 监控模型参数和梯度
    wandb.watch(model, log="all", log_freq=100)
    
    # 损失函数
    criterion = nn.CrossEntropyLoss()
    
    print(f"开始三阶段训练...")
    print(f"训练集大小: {len(train_dataset)}")
    print(f"测试集大小: {len(test_dataset)}")
    print(f"细分类别数量: {len(category_labels)}")
    print(f"超类别数量: {len(super_category_labels)}")
    
    # ==================== 阶段1：超类别预训练 ====================
    print("\n" + "="*50)
    print("阶段1：超类别预训练")
    print("="*50)
    
    wandb.log({"training_stage": "stage1_super_category_pretraining"})
    
    # 冻结类别分类器
    for param in model.category_classifier.parameters():
        param.requires_grad = False
    
    optimizer_stage1 = optim.Adam(filter(lambda p: p.requires_grad, model.parameters()), lr=config.stage1_lr)
    
    for epoch in range(config.stage1_epochs):
        print(f"\nStage 1 - Epoch {epoch+1}/{config.stage1_epochs}")
        train_loss, train_super_acc = train_epoch_stage1(model, train_loader, optimizer_stage1, criterion, device, epoch)
        
        # 记录epoch级别的指标
        wandb.log({
            "stage1/epoch": epoch + 1,
            "stage1/train_loss": train_loss,
            "stage1/train_super_category_accuracy": train_super_acc,
            "stage1/learning_rate": config.stage1_lr
        })
        
        print(f"训练损失: {train_loss:.4f}, 超类别准确率: {train_super_acc:.4f}")
    
    # ==================== 阶段2：类别微调 ====================
    print("\n" + "="*50)
    print("阶段2：类别微调")
    print("="*50)
    
    wandb.log({"training_stage": "stage2_category_finetuning"})
    
    # 解冻类别分类器，冻结超类别分类器
    for param in model.category_classifier.parameters():
        param.requires_grad = True
    for param in model.super_category_classifier.parameters():
        param.requires_grad = False
    
    optimizer_stage2 = optim.Adam(filter(lambda p: p.requires_grad, model.parameters()), lr=config.stage2_lr)
    
    for epoch in range(config.stage2_epochs):
        print(f"\nStage 2 - Epoch {epoch+1}/{config.stage2_epochs}")
        train_loss, train_cat_acc = train_epoch_stage2(model, train_loader, optimizer_stage2, criterion, device, epoch)
        
        # 记录epoch级别的指标
        wandb.log({
            "stage2/epoch": epoch + 1,
            "stage2/train_loss": train_loss,
            "stage2/train_category_accuracy": train_cat_acc,
            "stage2/learning_rate": config.stage2_lr
        })
        
        print(f"训练损失: {train_loss:.4f}, 类别准确率: {train_cat_acc:.4f}")
    
    # ==================== 阶段3：层级约束联合训练 ====================
    print("\n" + "="*50)
    print("阶段3：层级约束联合训练")
    print("="*50)
    
    wandb.log({"training_stage": "stage3_hierarchical_joint_training"})
    
    # 解冻所有参数
    for param in model.parameters():
        param.requires_grad = True
    
    optimizer_stage3 = optim.Adam(model.parameters(), lr=config.stage3_lr)
    
    best_hierarchical_score = 0
    best_model_state = None
    
    for epoch in range(config.stage3_epochs):
        print(f"\nStage 3 - Epoch {epoch+1}/{config.stage3_epochs}")
        
        # 训练
        train_loss, train_cat_acc, train_super_acc, loss_components = train_epoch_stage3(
            model, train_loader, optimizer_stage3, category_to_super_idx, device, epoch,
            config.hierarchical_loss_alpha, config.hierarchical_loss_beta
        )
        
        print(f"训练损失: {train_loss:.4f}")
        print(f"训练类别准确率: {train_cat_acc:.4f}")
        print(f"训练超类别准确率: {train_super_acc:.4f}")
        
        # 验证（使用后处理）
        cat_preds, super_preds, cat_true, super_true = evaluate_enhanced(
            model, test_loader, category_to_super_idx, device, use_post_processing=True
        )
        
        # 计算全面的评估指标
        metrics = comprehensive_hierarchical_metrics(
            cat_true, cat_preds, super_true, super_preds, category_to_super_idx
        )
        
        # 记录epoch级别的指标
        epoch_log = {
            "stage3/epoch": epoch + 1,
            "stage3/train_loss": train_loss,
            "stage3/train_category_accuracy": train_cat_acc,
            "stage3/train_super_category_accuracy": train_super_acc,
            "stage3/learning_rate": config.stage3_lr,
            **{f"stage3/train_{k}": v for k, v in loss_components.items()},
            **{f"stage3/val_{k}": v for k, v in metrics.items()}
        }
        wandb.log(epoch_log)
        
        print(f"验证指标:")
        for metric_name, value in metrics.items():
            print(f"  {metric_name}: {value:.4f}")
        
        # 保存最佳模型
        if metrics['strict_hierarchical_accuracy'] > best_hierarchical_score:
            best_hierarchical_score = metrics['strict_hierarchical_accuracy']
            best_model_state = copy.deepcopy(model.state_dict())
            
            # 保存最佳模型到wandb
            torch.save(best_model_state, 'best_model.pth')
            wandb.save('best_model.pth')
    
    # 加载最佳模型进行最终评估
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
    
    # ==================== 最终评估 ====================
    print("\n" + "="*70)
    print("最终评估结果")
    print("="*70)
    
    wandb.log({"evaluation_stage": "final_evaluation"})
    
    # 不使用后处理的评估
    cat_preds_raw, super_preds_raw, cat_true, super_true = evaluate_enhanced(
        model, test_loader, category_to_super_idx, device, use_post_processing=False
    )
    
    # 使用后处理的评估
    cat_preds_corrected, super_preds_corrected, _, _ = evaluate_enhanced(
        model, test_loader, category_to_super_idx, device, use_post_processing=True
    )
    
    # 原始预测指标
    metrics_raw = comprehensive_hierarchical_metrics(
        cat_true, cat_preds_raw, super_true, super_preds_raw, category_to_super_idx
    )
    
    # 后处理预测指标
    metrics_corrected = comprehensive_hierarchical_metrics(
        cat_true, cat_preds_corrected, super_true, super_preds_corrected, category_to_super_idx
    )
    
    # 记录最终评估结果
    final_results = {
        **{f"final/raw_{k}": v for k, v in metrics_raw.items()},
        **{f"final/corrected_{k}": v for k, v in metrics_corrected.items()},
        **{f"final/improvement_{k}": metrics_corrected[k] - metrics_raw[k] for k in metrics_raw.keys()}
    }
    wandb.log(final_results)
    
    print("原始预测结果:")
    for metric_name, value in metrics_raw.items():
        print(f"  {metric_name}: {value:.4f}")
    
    print("\n后处理预测结果:")
    for metric_name, value in metrics_corrected.items():
        print(f"  {metric_name}: {value:.4f}")
    
    print("\n改进效果:")
    for metric_name in metrics_raw.keys():
        improvement = metrics_corrected[metric_name] - metrics_raw[metric_name]
        print(f"  {metric_name}: {improvement:+.4f}")
    
    # 创建结果对比表格
    results_table = wandb.Table(
        columns=["Metric", "Raw Prediction", "Post-processed", "Improvement"],
        data=[
            [metric_name, f"{metrics_raw[metric_name]:.4f}", 
             f"{metrics_corrected[metric_name]:.4f}", 
             f"{metrics_corrected[metric_name] - metrics_raw[metric_name]:+.4f}"]
            for metric_name in metrics_raw.keys()
        ]
    )
    wandb.log({"final_results_comparison": results_table})
    
    # 生成实验摘要
    experiment_summary = {
        "best_strict_hierarchical_accuracy": best_hierarchical_score,
        "final_consistency_rate": metrics_corrected['consistency_rate'],
        "final_weighted_hierarchical_f1": metrics_corrected['weighted_hierarchical_f1'],
        "post_processing_improvement": metrics_corrected['strict_hierarchical_accuracy'] - metrics_raw['strict_hierarchical_accuracy'],
        "total_training_epochs": config.stage1_epochs + config.stage2_epochs + config.stage3_epochs,
        "model_size_mb": model_info["model_size_mb"],
        "dataset_size": dataset_stats["dataset_size"]
    }
    
    wandb.log(experiment_summary)
    wandb.summary.update(experiment_summary)
    
    print("="*70)
    print("实验完成！详细结果已记录到 Weights & Biases")
    print(f"项目链接: {wandb.run.url}")
    
    # 结束wandb运行
    wandb.finish()

if __name__ == "__main__":
    main()

