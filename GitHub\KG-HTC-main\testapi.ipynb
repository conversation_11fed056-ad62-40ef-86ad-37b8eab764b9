{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f873285f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\anconda\\envs\\py310\\lib\\site-packages\\requests\\__init__.py:86: RequestsDependencyWarning: Unable to find acceptable character detection dependency (chardet or charset_normalizer).\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🚀 OpenAI API 兼容接口快速连接测试\n", "==================================================\n", "正在测试 推理模型 端点: https://api.siliconflow.cn/v1/chat/completions\n", "❌ 推理模型 请求失败!\n", "状态码: 401\n", "响应内容: \"Invalid token\"\n", "--------------------------------------------------\n", "正在测试 向量化模型 端点: https://api.siliconflow.cn/v1/embeddings\n", "❌ 向量化模型 请求失败!\n", "状态码: 401\n", "响应内容: \"Invalid token\"\n", "==================================================\n", "📊 测试总结\n", "总测试数: 2\n"]}], "source": ["import requests\n", "from requests.exceptions import RequestException\n", "\n", "# API 配置\n", "api_config = {\n", "    \"base_url\": \"https://api.siliconflow.cn/v1\",\n", "    \"chat_completions_endpoint\": \"/chat/completions\",\n", "    \"embeddings_endpoint\": \"/embeddings\",\n", "    \"api_key\": \"\"  # 替换为你的实际 API 密钥\n", "}\n", "\n", "# 请求头设置\n", "headers = {\n", "    \"Content-Type\": \"application/json\",\n", "    \"Authorization\": f\"Bearer {api_config['api_key']}\"\n", "}\n", "\n", "def test_api_endpoint(endpoint, payload, endpoint_name):\n", "    \"\"\"测试指定 API 端点\"\"\"\n", "    try:\n", "        url = f\"{api_config['base_url']}{endpoint}\"\n", "        print(f\"正在测试 {endpoint_name} 端点: {url}\")\n", "        \n", "        # 发送请求，设置 verify=False 忽略 SSL 验证\n", "        response = requests.post(url, headers=headers, json=payload, verify=False)\n", "        \n", "        # 检查响应状态码\n", "        if response.status_code == 200:\n", "            print(f\"✅ {endpoint_name} 连接成功!\")\n", "            print(f\"响应内容: {response.json()}\")\n", "            return response.json()\n", "        else:\n", "            print(f\"❌ {endpoint_name} 请求失败!\")\n", "            print(f\"状态码: {response.status_code}\")\n", "            print(f\"响应内容: {response.text}\")\n", "            return None\n", "            \n", "    except RequestException as e:\n", "        print(f\"❌ {endpoint_name} 连接失败!\")\n", "        print(f\"错误: {str(e)}\")\n", "        return None\n", "\n", "def main():\n", "    # 禁用不安全请求的警告\n", "    import urllib3\n", "    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)\n", "    \n", "    print(\"🚀 OpenAI API 兼容接口快速连接测试\")\n", "    print(\"=\" * 50)\n", "    \n", "    # 测试聊天完成接口\n", "    chat_payload = {\n", "        \"model\": \"Qwen/Qwen2.5-7B-Instruct\",\n", "        \"messages\": [{\"role\": \"user\", \"content\": \"你好\"}],\n", "        \"temperature\": 0.7\n", "    }\n", "    test_api_endpoint(api_config[\"chat_completions_endpoint\"], chat_payload, \"推理模型\")\n", "    \n", "    print(\"-\" * 50)\n", "    \n", "    # 测试向量化接口\n", "    embedding_payload = {\n", "        \"model\": \"BAAI/bge-m3\",\n", "        \"input\": \"这是一个测试文本\"\n", "    }\n", "    test_api_endpoint(api_config[\"embeddings_endpoint\"], embedding_payload, \"向量化模型\")\n", "    \n", "    print(\"=\" * 50)\n", "    print(\"📊 测试总结\")\n", "    print(f\"总测试数: 2\")\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": null, "id": "8c966900", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"CURL_CA_BUNDLE\"] = \"\"\n", "\n", "from openai import OpenAI\n", "\n", "client = OpenAI(\n", "    api_key=\"sk-kxqclwblxrbprnibbwxfachyqxnivhocpisdjiciniqckvll\",\n", "    base_url=\"https://api.siliconflow.cn/v1/chat/completions\"\n", ")\n", "\n", "# 推理模型测试\n", "chat_response = client.chat.completions.create(\n", "    model=\"Qwen/Qwen2.5-7B-Instruct\",\n", "    messages=[{\"role\": \"user\", \"content\": \"你好\"}],\n", "    temperature=0.7\n", ")\n", "print(\"推理模型响应:\", chat_response)\n", "\n", "# 嵌入模型测试\n", "embedding_response = client.embeddings.create(\n", "    model=\"BAAI/bge-m3\",\n", "    input=\"这是一个测试文本\"\n", ")\n", "print(\"嵌入模型响应:\", embedding_response)"]}, {"cell_type": "code", "execution_count": 4, "id": "e9bbba37", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["推理模型响应: ChatCompletion(id='0198360d94187cd51757ddc646484b7f', choices=[Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content='你好！很高兴能为你服务。有什么我可以帮助你的吗？', refusal=None, role='assistant', annotations=None, audio=None, function_call=None, tool_calls=None))], created=1753253516, model='Qwen/Qwen2.5-7B-Instruct', object='chat.completion', service_tier=None, system_fingerprint='', usage=CompletionUsage(completion_tokens=13, prompt_tokens=30, total_tokens=43, completion_tokens_details=None, prompt_tokens_details=None))\n", "嵌入模型响应: CreateEmbeddingResponse(data=[Embedding(embedding=[-0.07180705666542053, 0.029805265367031097, -0.04474601149559021, -0.013702036812901497, 0.002273747231811285, -0.04215425252914429, 0.028052015230059624, 0.04531772434711456, 0.0013197024818509817, 0.019685959443449974, -0.010157421231269836, -0.00574094383046031, -0.002379752229899168, -0.017694493755698204, 0.006107792723923922, -0.011777272447943687, 0.019895587116479874, -0.04322144761681557, -0.01556963101029396, -0.025269681587815285, -0.056828197091817856, -0.0030253108125180006, 0.028566556051373482, 0.02391663007438183, -0.006684269290417433, 0.03464576229453087, -0.02500288374722004, -0.014607247896492481, -0.0012315635103732347, 0.029042983427643776, 0.05271187052130699, -0.009266503155231476, 0.04066779837012291, -0.06509897112846375, -0.009938265196979046, -0.023687943816184998, 0.017199009656906128, -0.03775206580758095, -0.029138268902897835, 0.004273549187928438, 0.029042983427643776, -0.037504322826862335, -0.00017672459944151342, -0.04817628487944603, -0.0041711172088980675, -0.01459771953523159, 0.03163474425673485, -0.03399782255291939, -0.036875441670417786, 0.0010374195408076048, -0.01899990439414978, 0.009947793558239937, 0.04646115005016327, 0.005426502320915461, 0.006951068062335253, -0.02210620790719986, 0.023535488173365593, -0.020352955907583237, -0.07573281973600388, 0.0362846702337265, -0.03725658357143402, 0.0034779163543134928, 0.022068092599511147, 0.03496973216533661, 0.01454054843634367, 0.06925341486930847, 0.012387098744511604, 0.0021820352412760258, -0.009318909607827663, -0.03249231353402138, -0.006812904495745897, 0.016303328797221184, -0.02330680377781391, 0.009890622459352016, -0.07527545094490051, 0.02502194046974182, -0.011443774215877056, -0.0436025895178318, -0.04005797207355499, 0.0030276929028332233, 0.040286656469106674, 0.03780923783779144, 0.010500448755919933, 0.01136754546314478, -0.027080103754997253, 0.000875434372574091, -0.022677918896079063, 0.013987893238663673, -0.0006860547000542283, -0.035369932651519775, -0.04177311062812805, -0.029576580971479416, 0.03165380284190178, -0.016093699261546135, -0.050272565335035324, 0.01749439537525177, -0.06837678700685501, 0.023764172568917274, 0.027556531131267548, 0.02405002899467945, 0.002634640783071518, 0.029271667823195457, 0.011996429413557053, -0.024717027321457863, 0.044822242110967636, -0.02521251142024994, 0.027975786477327347, 0.03432179242372513, -0.004192556720227003, -0.00022213404008653015, 0.0037447153590619564, 0.02166789397597313, 0.03188248723745346, 0.009947793558239937, 0.019590673968195915, 0.018980847671628, -0.037942636758089066, -0.005021539516746998, -0.016579655930399895, -0.013940250501036644, 0.03485539183020592, 0.050196338444948196, 0.060372814536094666, -0.03603693097829819, -0.022468291223049164, -0.021267695352435112, 0.0011517619714140892, 0.039333805441856384, 0.010938760824501514, -0.012148885987699032, 0.007632358931005001, 0.0603347010910511, -0.003630372928455472, -0.016693998128175735, -0.0046737478114664555, -0.012291813269257545, -0.013578166253864765, 0.020543526858091354, 0.0032992560882121325, -0.03134888783097267, 0.05823842063546181, 0.0407821424305439, -0.00992873590439558, -0.02199186570942402, 0.033959709107875824, -0.06570879369974136, 0.03559861704707146, -0.022449234500527382, 0.008070670999586582, 0.012244170531630516, 0.004785708151757717, 0.006012507248669863, -0.0032492312602698803, 0.020257670432329178, 0.018018465489149094, 0.011119803413748741, 0.009447544813156128, -0.004897668492048979, 0.011253203265368938, 0.07584716379642487, -0.01169151533395052, -0.01277776900678873, -0.017294295132160187, 0.012320399284362793, -0.011358017101883888, 0.03445519134402275, 0.020105214789509773, 0.03519841656088829, -0.026374991983175278, -0.038037922233343124, -0.002223722403869033, -0.035141248255968094, 0.01277776900678873, -0.020143328234553337, 0.026203477755188942, 0.015836428850889206, 0.06883415579795837, 0.018323378637433052, 0.009123574942350388, -0.03251137211918831, -0.01137707382440567, 0.03883831948041916, -0.027118219062685966, -0.028585612773895264, -0.006055385805666447, -0.006555634085088968, 0.019180946052074432, 0.006617569364607334, -0.018485363572835922, 0.004366452340036631, -0.01890461891889572, -0.04466978460550308, 0.013101738877594471, 0.011901143938302994, 0.003999603912234306, 0.011472359299659729, 0.018971318379044533, -0.021420152857899666, 0.016836926341056824, -0.024126257747411728, -0.035045962780714035, -0.008794840425252914, 0.044707898050546646, -0.025593651458621025, -0.0066747404634952545, -0.0006378164980560541, -0.07920120656490326, -0.04821440204977989, 0.047070976346731186, -0.03369290754199028, -0.040744028985500336, -0.010662433691322803, -0.01853300631046295, 0.0032468491699546576, -0.018266206607222557, 0.0006193549488671124, -0.005826700944453478, -0.0019390573725104332, 0.017694493755698204, -0.0350840762257576, -0.017294295132160187, 0.01251097023487091, 0.034817278385162354, 0.05450323596596718, 0.033769138157367706, 0.004261638503521681, 0.006736676208674908, 0.042535390704870224, 0.02963375300168991, -0.052445072680711746, -0.003065807046368718, -0.001500744721852243, -0.005112060345709324, -0.03394065052270889, 0.029042983427643776, 0.03325459733605385, -0.014445262961089611, 0.009752457961440086, 0.04123951122164726, -0.004447444807738066, 0.019876530393958092, -0.0313679464161396, 0.03914323449134827, 0.0019616878125816584, -0.04691851884126663, -0.006960596889257431, 0.007046353537589312, 0.021744122728705406, -0.018990375101566315, -0.00830888468772173, 0.011043575592339039, -0.015064618550240993, -0.02542213909327984, 0.005364566575735807, 0.030415091663599014, 0.027175389230251312, 0.028585612773895264, -0.006531812716275454, 0.00914263166487217, -0.02370700240135193, 0.01833290606737137, -0.02534591034054756, 0.04783326014876366, 0.0407821424305439, 0.013759208843111992, -0.022087151184678078, -0.02265886217355728, 0.011405659839510918, 0.011453302577137947, -0.012425213120877743, -0.0025322088040411472, -0.042764078825712204, 0.013063625432550907, -0.013730622828006744, 0.010224120691418648, 0.00231186137534678, -0.015693502500653267, -0.02340208925306797, 0.03679921105504036, 0.006627098191529512, 0.005002482328563929, 0.02553648129105568, 0.012044071219861507, -0.01974312961101532, -0.007036825176328421, 0.0012071466771885753, 0.00308724632486701, -0.047909487038850784, 0.020734097808599472, -0.01661776937544346, 0.014959803782403469, -0.0027418367099016905, 0.07150214910507202, -0.023783229291439056, -0.08125936985015869, -0.00332545954734087, 0.016065115109086037, -0.16953174769878387, -0.0020581642165780067, -0.027537474408745766, -0.011920200660824776, 0.02384040132164955, -0.003956725355237722, -0.05236884206533432, -0.022677918896079063, -0.011243674904108047, 0.06140189617872238, 0.004233052954077721, -0.04508903995156288, -0.017827894538640976, -0.024983825162053108, -0.0007354840054176748, 0.02027672901749611, -0.057133112102746964, -0.010062135756015778, 0.01974312961101532, -0.025707995519042015, 0.005378859583288431, -0.026965761557221413, 0.12699635326862335, 0.0084708696231246, -0.003711365396156907, -0.020448241382837296, 0.047490231692790985, -0.02425965666770935, -0.037942636758089066, -0.012701541185379028, -0.018990375101566315, 0.01148188766092062, -0.015493402257561684, 0.027861444279551506, 0.023230575025081635, 0.06071584299206734, 0.075923390686512, -0.01833290606737137, -0.0338263101875782, -0.00821359921246767, 0.016808340325951576, 0.014473848976194859, -0.027346903458237648, 0.04070591181516647, 0.020867496728897095, 0.004692804999649525, 0.041163284331560135, 0.008704319596290588, -0.027346903458237648, -0.02746124565601349, -0.022811319679021835, -0.02212526462972164, -0.001461439416743815, -0.009109281934797764, -0.03822849318385124, -0.028395041823387146, -0.04604189470410347, 0.023535488173365593, -0.06406988948583603, 0.005674244370311499, -0.023421145975589752, -0.04177311062812805, 0.05999167263507843, 0.044059958308935165, -0.00800397153943777, -0.011967843398451805, 0.03413122147321701, -0.020791269838809967, -0.0024131021928042173, -0.022144321352243423, 0.027423132210969925, -0.017970822751522064, 0.0314251184463501, -0.03666581213474274, 0.004464120138436556, 0.003096774686127901, -0.002577469451352954, 0.0007646651356481016, -0.02145826630294323, -0.13347575068473816, 0.02748030237853527, 0.021934693679213524, -0.023097176104784012, 0.005807643756270409, -0.00860903412103653, -0.01136754546314478, -0.014664419926702976, 0.019400103017687798, 0.07321728020906448, 0.26222535967826843, -0.005917221773415804, 0.003001489443704486, -0.06010601669549942, 0.010957818478345871, -0.017989879474043846, -0.026584619656205177, -0.01288258284330368, -0.03658958524465561, -0.03390253707766533, 0.007027296349406242, 0.006188785191625357, 0.0035684374161064625, 0.02780427224934101, 0.01260625571012497, 0.016551069915294647, 0.004992953967303038, -0.0013328042114153504, 0.08781500160694122, 0.0072559816762804985, 0.01793270744383335, -0.03327365219593048, 0.012568141333758831, -0.00014121590356808156, -0.06887227296829224, -0.013311367481946945, -0.024221543222665787, 0.013292309828102589, -0.042116135358810425, 0.02166789397597313, -0.007051117718219757, 0.01267295517027378, 0.03456953540444374, 0.022163378074765205, 0.00860903412103653, 0.000683077028952539, 0.057781051844358444, -0.0521782711148262, -0.028185414150357246, 0.014931218698620796, -0.017904121428728104, -0.04173499345779419, -0.019190475344657898, -0.002370223868638277, -0.00179017405025661, -0.025574594736099243, -0.026679905131459236, -0.052330728620290756, 0.022411121055483818, -0.006846254225820303, -0.015302831307053566, -0.013844965025782585, 0.02349737472832203, 0.02648933418095112, 0.030529435724020004, 0.04516527056694031, -0.03512218967080116, -0.015493402257561684, -0.02210620790719986, 0.054770033806562424, 0.029138268902897835, 0.023249631747603416, -0.053817182779312134, 0.010976875200867653, 0.024335885420441628, 0.03131077438592911, -0.07851514965295792, -0.022677918896079063, 0.011767744086682796, 0.0056933010928332806, 0.05084427818655968, -0.00742749497294426, 0.01983841508626938, -0.0036113157402724028, -0.004056775011122227, 0.020257670432329178, 0.001500744721852243, 0.0334642231464386, 0.017160896211862564, 0.051682788878679276, -0.006236427929252386, -0.0037566260434687138, -0.022049035876989365, -0.0032921098172664642, 0.012196527794003487, -0.01116744615137577, 0.028604669496417046, 0.04436487331986427, -0.020886555314064026, -0.03603693097829819, -0.02424059994518757, 0.01449290569871664, -0.040210429579019547, -0.02361171692609787, 0.03403593599796295, 0.007737172767519951, -0.005073946435004473, -0.02187752164900303, -0.04756645858287811, -0.03390253707766533, 0.013930722139775753, -0.016398614272475243, 0.0007724071037955582, -0.01106263231486082, -0.008656676858663559, 0.012063128873705864, 0.03929568827152252, 0.010176477953791618, 0.01948585920035839, 0.03306402638554573, -0.025898564606904984, 0.020791269838809967, -0.001405459363013506, -0.05313112586736679, 0.0009790572803467512, 0.015865014865994453, 0.044822242110967636, 0.04192556440830231, 0.02820447087287903, 0.05743802711367607, -0.008852011524140835, -0.0046737478114664555, -0.0028514149598777294, -0.009485659189522266, -0.016160400584340096, -0.026432164013385773, 0.02212526462972164, 0.039448145776987076, -0.011920200660824776, 0.02349737472832203, 0.0222205501049757, -0.039333805441856384, -0.005840993486344814, 0.02972903847694397, 0.047490231692790985, 0.009075932204723358, 0.09459932148456573, 0.035350874066352844, -0.02843315713107586, 0.03035792149603367, -0.02748030237853527, -0.030319806188344955, 0.006022035609930754, 0.01911424659192562, -0.034626707434654236, 0.003411216428503394, -0.006265013478696346, 0.010309877805411816, -0.013997421599924564, 0.017084667459130287, 0.032473254948854446, -0.008404170162975788, -0.00737985223531723, -0.05656139925122261, 0.008994939737021923, -0.08446095883846283, -0.027880501002073288, -0.02393568679690361, -0.01883791945874691, -0.029652809724211693, -0.00490719685330987, -0.005926750600337982, 0.028071071952581406, 0.08575683832168579, 0.034931618720293045, 0.016122285276651382, -0.0010314641986042261, -0.012234642170369625, 0.01272059790790081, -0.017694493755698204, -0.014740647748112679, 0.02029578574001789, -0.05313112586736679, 0.0717308297753334, 0.008427991531789303, -0.05225450173020363, -0.04661360755562782, -0.015064618550240993, -0.04303087666630745, -0.029366953298449516, 0.031177375465631485, -0.04966273903846741, -0.03369290754199028, 0.04131573811173439, 0.014921690337359905, 0.027956729754805565, -0.011996429413557053, -0.010710076428949833, 0.05442700907588005, -0.04207802191376686, -0.02124863862991333, 0.09894433617591858, -0.027632759883999825, -0.006107792723923922, 0.03296874091029167, -0.04066779837012291, 0.047375887632369995, -0.002796625718474388, -0.02101995423436165, -0.032263629138469696, 0.04017231613397598, -0.011310374364256859, -0.008685261942446232, 0.006765261758118868, -0.009080696851015091, -0.014283278025686741, -0.0258795078843832, 0.018923675641417503, -0.03222551569342613, 0.03647524118423462, -0.019476331770420074, -0.007503723260015249, 0.00633647758513689, -0.010205063968896866, -0.04108705371618271, -0.05320735275745392, 0.015807844698429108, 0.01920953206717968, -0.02544119581580162, -0.03302591294050217, 0.004571315832436085, 0.012320399284362793, 0.010814890265464783, -0.04722343385219574, -0.00566471554338932, 0.006007743068039417, -0.011805858463048935, -0.04764268919825554, 0.018170921131968498, 0.011405659839510918, 0.003139653243124485, 0.005021539516746998, -0.03516030311584473, -0.03725658357143402, 0.057056885212659836, -0.017599208280444145, -0.006808140315115452, 0.05172090232372284, -0.030453206971287727, -0.012034542858600616, 0.02029578574001789, 0.016532013192772865, 0.016951268538832664, 0.02824258618056774, -0.028414098545908928, 0.061668697744607925, -0.03527464717626572, -0.02317340299487114, 0.00938560999929905, 0.029557524248957634, 0.009428488090634346, -0.02662273496389389, 0.002305906033143401, -0.023783229291439056, -0.014693005010485649, 0.06204983592033386, -0.00904734618961811, -0.014292806386947632, -0.018170921131968498, 0.015626801177859306, -0.012434741482138634, 0.020105214789509773, -0.006260249298065901, 0.0033588095102459192, -0.0067176190204918385, -0.06620427966117859, 0.03296874091029167, -0.008380348794162273, -0.006912954151630402, -0.055837232619524, -0.046194352209568024, 0.006507991347461939, -0.018685461953282356, -0.06471782922744751, 0.039753060787916183, -0.007799108047038317, -0.01561727374792099, 0.014531020075082779, 0.005936278961598873, 0.017017967998981476, 0.011653401888906956, 0.02778521552681923, -0.007194045931100845, 0.008170721121132374, 0.004664218984544277, 0.0011434245388954878, 0.025841394439339638, -0.018228093162178993, 0.016112757846713066, -0.031825315207242966, 0.00568377273157239, -0.0010636229999363422, -0.03477916121482849, 0.03434085100889206, -0.017275238409638405, 0.022392064332962036, -0.03517936170101166, 0.022773204371333122, -0.007460845168679953, 0.04215425252914429, -0.017713552340865135, 0.03216834366321564, -0.045889437198638916, -0.012463327497243881, -0.0037399509456008673, -0.003592258784919977, -0.01658918336033821, 0.002613201504573226, 0.023897573351860046, 0.008542333729565144, 0.014578662812709808, -0.06620427966117859, -0.014035535976290703, -0.023421145975589752, -0.028719013556838036, -0.00919980276376009, 0.04531772434711456, -0.008385113440454006, 0.009476130828261375, 0.008342234417796135, -0.0032706705387681723, -0.013273253105580807, -0.03960060328245163, 0.027956729754805565, -0.041468195617198944, -0.002264218870550394, 0.011824915185570717, 0.016674941405653954, -0.007875336334109306, -0.039219461381435394, -0.0003471960953902453, -0.0407821424305439, -0.013311367481946945, -0.023954743519425392, 0.016303328797221184, 0.04333578795194626, -0.004950075410306454, -0.010691019706428051, 0.024640798568725586, 0.02220149338245392, 0.024316828697919846, 0.005140645895153284, 0.04699474945664406, -0.053626611828804016, 0.015512458980083466, -0.01964784413576126, -0.0006533003761433065, 0.013997421599924564, 0.010529033839702606, -0.0038185615558177233, 0.010205063968896866, -0.009947793558239937, -0.0169417392462492, -0.0570949986577034, 0.007861043326556683, 0.037599608302116394, 0.0028514149598777294, 0.06376497447490692, 0.056180257350206375, 0.00810878537595272, -0.010205063968896866, 0.017084667459130287, -0.056599512696266174, -0.008304120972752571, -0.019724072888493538, -0.0047642686404287815, 0.018018465489149094, 0.005269281566143036, -0.026965761557221413, -0.010443277657032013, -0.00721786729991436, 0.044707898050546646, 0.010433748364448547, -0.00019265511946287006, -0.013997421599924564, 0.039981745183467865, -0.026470277458429337, 0.00872337631881237, 0.020429184660315514, 0.009938265196979046, 0.017418166622519493, -0.01865687593817711, 0.010062135756015778, -0.015426702797412872, 0.045660752803087234, -0.020562583580613136, -0.03180626034736633, 0.020581642165780067, 0.03113926202058792, 0.024659855291247368, -0.01770402304828167, -0.003975782543420792, -0.014769233763217926, 0.0031444174237549305, -0.195754274725914, 0.0040734498761594296, -0.0285474993288517, -0.011767744086682796, -0.015150374732911587, -0.00711305346339941, 0.02950035221874714, -0.041582539677619934, 0.0131493816152215, -0.03556050360202789, -0.06910095363855362, 0.015836428850889206, -0.0065842196345329285, -0.02307811751961708, -0.0019795536063611507, 0.02727067470550537, -0.004140149801969528, 0.025079110637307167, -0.003527941182255745, 0.02789955772459507, -0.015455287881195545, 0.0015245659742504358, 0.012253699824213982, 0.016646355390548706, -0.03683732822537422, -0.004466501995921135, 0.016065115109086037, 0.00735126668587327, -0.028471270576119423, -0.034950677305459976, 0.050196338444948196, -0.0379045233130455, 0.009366552345454693, 0.050196338444948196, 0.025936679914593697, 0.024297771975398064, 0.014673948287963867, -0.05366472527384758, 0.012148885987699032, -0.006765261758118868, -0.011605759151279926, 0.02727067470550537, -0.027080103754997253, -0.02425965666770935, 0.06254532188177109, 0.023992858827114105, -0.010853004641830921, -0.013806851580739021, -0.06502274423837662, -0.0064174700528383255, -0.01809469237923622, 0.00644129142165184, -0.034398019313812256, 0.02008615806698799, -0.03037697821855545, 0.007074939087033272, -0.02565082348883152, 0.006322184577584267, -0.041811224073171616, -0.005297867115586996, -0.010405163280665874, 0.009690523147583008, -0.03496973216533661, -0.038571521639823914, -0.060029786080121994, 0.013635337352752686, -0.07771475613117218, 0.04432675614953041, -0.0222205501049757, 0.038800206035375595, -0.030662834644317627, 0.01115791779011488, 0.021915636956691742, -0.06380309164524078, -0.0019795536063611507, 0.04032476991415024, 0.028699954971671104, 0.006064914166927338, 0.0049310182221233845, -0.05545609071850777, 0.0028823825996369123, -0.006860547233372927, -0.04322144761681557, 0.007803872227668762, 0.04005797207355499, 0.023668887093663216, 0.0039043184369802475, -0.0026370228733867407, -0.054350778460502625, 0.015750672668218613, -0.037637725472450256, -0.010205063968896866, 0.020638812333345413, 0.008385113440454006, -0.025174396112561226, -0.0023737968876957893, -0.018866505473852158, -0.025136282667517662, 0.004523673094809055, -0.012272756546735764, -0.025269681587815285, 0.011739158071577549, 0.015340945683419704, 0.04284030571579933, -0.02189658023416996, 0.07039683312177658, 0.036532413214445114, 0.012644369155168533, 0.018971318379044533, 0.015083675272762775, -0.05465569347143173, 0.0031658567022532225, -0.012320399284362793, -0.017465809360146523, -0.0798872634768486, 0.003954343032091856, -0.002084367675706744, 0.017513452097773552, -0.029481295496225357, 0.017199009656906128, 0.007965857163071632, -0.028490327298641205, 0.007341738324612379, -0.015855487436056137, 0.01297786831855774, 0.016741640865802765, 0.024450227618217468, 0.00881866179406643, -0.013397123664617538, -0.010376577265560627, 0.030319806188344955, -0.017418166622519493, 0.04082025587558746, 0.048557426780462265, 0.026451220735907555, -0.010252706706523895, -0.016903625801205635, 0.025364967063069344, -0.03199683129787445, -0.010986403562128544, -0.020753154531121254, -0.010776775889098644, -0.024221543222665787, -0.02145826630294323, -0.009561887942254543, 0.0116343442350626, 0.006184021010994911, 0.00566471554338932, -0.045965664088726044, -0.018590176478028297, 0.04821440204977989, -0.018323378637433052, -0.004897668492048979, 0.013730622828006744, -0.0024631270207464695, 0.028356928378343582, 0.034188393503427505, -0.017951764166355133, -0.004926254041492939, 0.05572288855910301, -0.01615087129175663, -0.00716069620102644, -0.026851419359445572, -0.014235635288059711, -0.008656676858663559, 0.028738070279359818, 0.013864022679626942, -0.014826404862105846, -0.030548492446541786, 0.03329271078109741, 0.027880501002073288, 0.0029181146528571844, -0.004511762410402298, 0.008189777843654156, 0.025136282667517662, -0.02233489230275154, 0.020105214789509773, 0.019819358363747597, 0.04615623503923416, 0.008918710984289646, 0.022677918896079063, -0.0014149878406897187, 0.024069085717201233, 0.02521251142024994, 0.003044367767870426, 0.059191275388002396, -0.034188393503427505, 0.01920953206717968, 0.02595573663711548, 0.003044367767870426, 0.02134392410516739, -0.006069678347557783, -0.04905291274189949, -0.011558116413652897, 0.0620117224752903, 0.00815642811357975, 0.009690523147583008, 0.03662769868969917, -0.004545112606137991, 0.010643376968801022, -0.02532685361802578, -0.007336974143981934, -0.01997181586921215, -0.036532413214445114, -0.036970727145671844, -0.0011553352233022451, 0.011186502873897552, -0.036341842263936996, -0.015217075124382973, 0.008785312063992023, -0.022163378074765205, -0.02972903847694397, -0.014997918158769608, 0.022506406530737877, -0.0029014397878199816, 0.04017231613397598, 0.00918551068753004, 0.03841906413435936, 0.002228486817330122, -0.019666902720928192, -0.020372014492750168, 0.027747102081775665, 0.0169417392462492, -0.02618442103266716, -0.02328774705529213, 0.003011017804965377, -0.05076804757118225, 0.02199186570942402, 0.005931514780968428, -0.03392159566283226, -0.008532805368304253, 0.019285760819911957, -0.024221543222665787, 0.05492249131202698, 0.013930722139775753, 0.021648837253451347, 0.03737092390656471, 0.018085164949297905, -0.005469380412250757, -0.0034207450225949287, 0.04638492316007614, -0.010233649052679539, -0.0037756829988211393, 0.02706104703247547], index=0, object='embedding')], model='BAAI/bge-m3', object='list', usage=Usage(prompt_tokens=6, total_tokens=6, completion_tokens=0))\n"]}], "source": ["import os\n", "os.environ[\"CURL_CA_BUNDLE\"] = \"\"\n", "\n", "from openai import OpenAI\n", "\n", "import httpx\n", "# 1. 配置自定义 httpx Client，禁用 SSL 验证（仅测试用）\n", "custom_httpx_client = httpx.Client(\n", "    verify=False,  # 忽略 SSL 证书验证（测试环境临时使用）\n", "    timeout=httpx.Timeout(10.0)  # 可选：设置超时时间\n", ")\n", "client = OpenAI(\n", "    api_key=\"sk-kxqclwblxrbprnibbwxfachyqxnivhocpisdjiciniqckvll\",\n", "    base_url=\"https://api.siliconflow.cn/v1\",\n", "     http_client=custom_httpx_client  # 使用自定义的 httpx Client\n", ")\n", "\n", "# 推理模型测试\n", "chat_response = client.chat.completions.create(\n", "    model=\"Qwen/Qwen2.5-7B-Instruct\",\n", "    messages=[{\"role\": \"user\", \"content\": \"你好\"}],\n", "    temperature=0.7\n", ")\n", "print(\"推理模型响应:\", chat_response)\n", "\n", "# 嵌入模型测试\n", "embedding_response = client.embeddings.create(\n", "    model=\"BAAI/bge-m3\",\n", "    input=\"这是一个测试文本\"\n", ")\n", "print(\"嵌入模型响应:\", embedding_response)"]}], "metadata": {"kernelspec": {"display_name": "py310", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}