import pandas as pd
import numpy as np
import json
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from sklearn.metrics import accuracy_score, f1_score, classification_report
import warnings
warnings.filterwarnings('ignore')

def load_all_results():
    """加载所有实验结果"""
    results = {}
    
    # 定义所有结果文件
    result_files = {
        'KG-HTC': 'dataset/nasa_topics/llm_graph_gpt3.json',
        'LLM-Only': 'dataset/nasa_topics/llm_only_results.json',
        'Full-KG': 'dataset/nasa_topics/full_kg_results.json',
        'Vector-Only': 'dataset/nasa_topics/vector_only_results.json',
        'Random Forest': 'dataset/nasa_topics/random_forest_results.json',
        'Logistic Regression': 'dataset/nasa_topics/logistic_regression_results.json',
        'BERT Baseline': 'dataset/nasa_topics/bert_baseline_results.json'
    }
    
    for method, file_path in result_files.items():
        if Path(file_path).exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                results[method] = pd.DataFrame(data)
                print(f"✓ 加载 {method}: {len(data)} 条记录")
            except Exception as e:
                print(f"✗ 加载 {method} 失败: {e}")
        else:
            print(f"⚠ 文件不存在: {file_path}")
    
    return results

def standardize_predictions(df, method):
    """标准化预测结果字段名"""
    # 根据不同方法的字段名进行标准化
    if method == 'KG-HTC':
        pred_cols = ['gpt3_graph_l1', 'gpt3_graph_l2', 'gpt3_graph_l3']
    elif method == 'LLM-Only':
        pred_cols = ['llm_only_l1', 'llm_only_l2', 'llm_only_l3']
    elif method == 'Full-KG':
        pred_cols = ['full_kg_l1', 'full_kg_l2', 'full_kg_l3']
    elif method == 'Vector-Only':
        pred_cols = ['vector_only_l1', 'vector_only_l2', 'vector_only_l3']
    elif method == 'BERT Baseline':
        pred_cols = ['bert_pred_l1', 'bert_pred_l2', 'bert_pred_l3']
    elif 'Random Forest' in method:
        pred_cols = ['Random Forest_pred_l1', 'Random Forest_pred_l2', 'Random Forest_pred_l3']
    elif 'Logistic Regression' in method:
        pred_cols = ['Logistic Regression_pred_l1', 'Logistic Regression_pred_l2', 'Logistic Regression_pred_l3']
    else:
        # 尝试自动检测
        possible_l1 = [col for col in df.columns if 'l1' in col.lower() and 'pred' in col.lower()]
        possible_l2 = [col for col in df.columns if 'l2' in col.lower() and 'pred' in col.lower()]
        possible_l3 = [col for col in df.columns if 'l3' in col.lower() and 'pred' in col.lower()]
        
        if possible_l1 and possible_l2 and possible_l3:
            pred_cols = [possible_l1[0], possible_l2[0], possible_l3[0]]
        else:
            print(f"⚠ 无法识别 {method} 的预测字段")
            return df
    
    # 检查字段是否存在
    missing_cols = [col for col in pred_cols if col not in df.columns]
    if missing_cols:
        print(f"⚠ {method} 缺少字段: {missing_cols}")
        return df
    
    # 标准化字段名
    df = df.copy()
    df['pred_l1'] = df[pred_cols[0]].astype(str).str.lower().str.strip()
    df['pred_l2'] = df[pred_cols[1]].astype(str).str.lower().str.strip()
    df['pred_l3'] = df[pred_cols[2]].astype(str).str.lower().str.strip()
    
    # 标准化真实标签
    df['true_l1'] = df['Cat1'].astype(str).str.lower().str.strip()
    df['true_l2'] = df['Cat2'].astype(str).str.lower().str.strip()
    df['true_l3'] = df['Cat3'].astype(str).str.lower().str.strip()
    
    return df

def calculate_comprehensive_metrics(results_dict):
    """计算所有方法的综合指标"""
    comprehensive_metrics = {}
    
    for method, df in results_dict.items():
        print(f"\n=== 评估 {method} ===")
        
        # 标准化预测结果
        df = standardize_predictions(df, method)
        
        # 过滤有效结果
        valid_mask = (df['pred_l1'] != 'error') & (df['pred_l1'] != 'nan') & (df['pred_l1'].notna())
        df_valid = df[valid_mask].copy()
        
        if len(df_valid) == 0:
            print(f"⚠ {method} 无有效结果")
            continue
        
        print(f"有效样本: {len(df_valid)}/{len(df)}")
        
        try:
            # 计算准确率
            acc_l1 = (df_valid['pred_l1'] == df_valid['true_l1']).mean()
            acc_l2 = (df_valid['pred_l2'] == df_valid['true_l2']).mean()
            acc_l3 = (df_valid['pred_l3'] == df_valid['true_l3']).mean()
            
            # 层次化准确率
            hierarchical_acc = ((df_valid['pred_l1'] == df_valid['true_l1']) & 
                               (df_valid['pred_l2'] == df_valid['true_l2']) & 
                               (df_valid['pred_l3'] == df_valid['true_l3'])).mean()
            
            # 部分层次化准确率
            l1_l2_acc = ((df_valid['pred_l1'] == df_valid['true_l1']) & 
                         (df_valid['pred_l2'] == df_valid['true_l2'])).mean()
            
            # F1分数 (如果有足够的类别)
            try:
                f1_l1 = f1_score(df_valid['true_l1'], df_valid['pred_l1'], average='macro', zero_division=0)
                f1_l2 = f1_score(df_valid['true_l2'], df_valid['pred_l2'], average='macro', zero_division=0)
                f1_l3 = f1_score(df_valid['true_l3'], df_valid['pred_l3'], average='macro', zero_division=0)
            except:
                f1_l1 = f1_l2 = f1_l3 = 0.0
            
            # 推理时间 (如果有)
            avg_inference_time = df_valid.get('inference_time', pd.Series([0])).mean()
            
            comprehensive_metrics[method] = {
                'accuracy': {
                    'L1': acc_l1,
                    'L2': acc_l2,
                    'L3': acc_l3,
                    'L1_L2': l1_l2_acc,
                    'Hierarchical': hierarchical_acc
                },
                'f1_macro': {
                    'L1': f1_l1,
                    'L2': f1_l2,
                    'L3': f1_l3
                },
                'statistics': {
                    'total_samples': len(df),
                    'valid_samples': len(df_valid),
                    'success_rate': len(df_valid) / len(df),
                    'average_inference_time': avg_inference_time
                }
            }
            
            print(f"L1准确率: {acc_l1:.4f}")
            print(f"L2准确率: {acc_l2:.4f}")
            print(f"L3准确率: {acc_l3:.4f}")
            print(f"层次化准确率: {hierarchical_acc:.4f}")
            
        except Exception as e:
            print(f"✗ {method} 指标计算失败: {e}")
    
    return comprehensive_metrics

def create_comparison_visualizations(metrics):
    """创建对比可视化图表"""
    if not metrics:
        print("无可用指标数据")
        return
    
    # 准备数据
    methods = list(metrics.keys())
    l1_acc = [metrics[m]['accuracy']['L1'] for m in methods]
    l2_acc = [metrics[m]['accuracy']['L2'] for m in methods]
    l3_acc = [metrics[m]['accuracy']['L3'] for m in methods]
    hierarchical_acc = [metrics[m]['accuracy']['Hierarchical'] for m in methods]
    
    # 设置图表样式
    plt.style.use('default')
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 各级别准确率对比
    ax1 = axes[0, 0]
    x = np.arange(len(methods))
    width = 0.25
    
    ax1.bar(x - width, l1_acc, width, label='L1', alpha=0.8)
    ax1.bar(x, l2_acc, width, label='L2', alpha=0.8)
    ax1.bar(x + width, l3_acc, width, label='L3', alpha=0.8)
    
    ax1.set_xlabel('Methods')
    ax1.set_ylabel('Accuracy')
    ax1.set_title('Accuracy Comparison by Level')
    ax1.set_xticks(x)
    ax1.set_xticklabels(methods, rotation=45, ha='right')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 层次化准确率对比
    ax2 = axes[0, 1]
    bars = ax2.bar(methods, hierarchical_acc, alpha=0.8, color='skyblue')
    ax2.set_xlabel('Methods')
    ax2.set_ylabel('Hierarchical Accuracy')
    ax2.set_title('Hierarchical Accuracy Comparison')
    ax2.set_xticklabels(methods, rotation=45, ha='right')
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, acc in zip(bars, hierarchical_acc):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{acc:.3f}', ha='center', va='bottom')
    
    # 3. F1分数对比
    ax3 = axes[1, 0]
    f1_l1 = [metrics[m]['f1_macro']['L1'] for m in methods]
    f1_l2 = [metrics[m]['f1_macro']['L2'] for m in methods]
    f1_l3 = [metrics[m]['f1_macro']['L3'] for m in methods]
    
    ax3.bar(x - width, f1_l1, width, label='L1', alpha=0.8)
    ax3.bar(x, f1_l2, width, label='L2', alpha=0.8)
    ax3.bar(x + width, f1_l3, width, label='L3', alpha=0.8)
    
    ax3.set_xlabel('Methods')
    ax3.set_ylabel('F1 Score (Macro)')
    ax3.set_title('F1 Score Comparison by Level')
    ax3.set_xticks(x)
    ax3.set_xticklabels(methods, rotation=45, ha='right')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 推理时间对比
    ax4 = axes[1, 1]
    inference_times = [metrics[m]['statistics']['average_inference_time'] for m in methods]
    bars = ax4.bar(methods, inference_times, alpha=0.8, color='lightcoral')
    ax4.set_xlabel('Methods')
    ax4.set_ylabel('Average Inference Time (seconds)')
    ax4.set_title('Inference Time Comparison')
    ax4.set_xticklabels(methods, rotation=45, ha='right')
    ax4.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, time in zip(bars, inference_times):
        height = bar.get_height()
        if height > 0:
            ax4.text(bar.get_x() + bar.get_width()/2., height + max(inference_times)*0.01,
                    f'{time:.2f}s', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('nasa_topics_comprehensive_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✓ 对比图表已保存: nasa_topics_comprehensive_comparison.png")

def generate_comprehensive_report(metrics):
    """生成综合评估报告"""
    if not metrics:
        print("无可用指标数据")
        return
    
    report = []
    report.append("# NASA Topics数据集综合评估报告")
    report.append("=" * 50)
    report.append("")
    
    # 方法排名
    report.append("## 方法性能排名")
    report.append("")
    
    # 按层次化准确率排序
    hierarchical_ranking = sorted(metrics.items(), 
                                 key=lambda x: x[1]['accuracy']['Hierarchical'], 
                                 reverse=True)
    
    report.append("### 按层次化准确率排名:")
    for i, (method, metric) in enumerate(hierarchical_ranking, 1):
        acc = metric['accuracy']['Hierarchical']
        report.append(f"{i}. {method}: {acc:.4f} ({acc*100:.2f}%)")
    
    report.append("")
    
    # 详细性能表格
    report.append("## 详细性能对比")
    report.append("")
    report.append("| 方法 | L1准确率 | L2准确率 | L3准确率 | 层次化准确率 | L1 F1 | L2 F1 | L3 F1 | 推理时间(s) |")
    report.append("|------|----------|----------|----------|--------------|-------|-------|-------|------------|")
    
    for method, metric in metrics.items():
        acc = metric['accuracy']
        f1 = metric['f1_macro']
        time = metric['statistics']['average_inference_time']
        
        report.append(f"| {method} | {acc['L1']:.4f} | {acc['L2']:.4f} | {acc['L3']:.4f} | "
                     f"{acc['Hierarchical']:.4f} | {f1['L1']:.4f} | {f1['L2']:.4f} | "
                     f"{f1['L3']:.4f} | {time:.2f} |")
    
    report.append("")
    
    # 关键发现
    report.append("## 关键发现")
    report.append("")
    
    best_method = hierarchical_ranking[0][0]
    best_acc = hierarchical_ranking[0][1]['accuracy']['Hierarchical']
    
    report.append(f"1. **最佳方法**: {best_method} (层次化准确率: {best_acc:.4f})")
    
    if 'KG-HTC' in metrics:
        kg_htc_rank = next(i for i, (m, _) in enumerate(hierarchical_ranking, 1) if m == 'KG-HTC')
        report.append(f"2. **KG-HTC排名**: 第{kg_htc_rank}位")
    
    # 消融分析
    ablation_methods = ['KG-HTC', 'LLM-Only', 'Full-KG', 'Vector-Only']
    available_ablation = [m for m in ablation_methods if m in metrics]
    
    if len(available_ablation) >= 2:
        report.append("3. **消融分析**:")
        for method in available_ablation:
            acc = metrics[method]['accuracy']['Hierarchical']
            report.append(f"   - {method}: {acc:.4f}")
    
    # 保存报告
    report_text = "\n".join(report)
    with open("nasa_topics_comprehensive_report.md", 'w', encoding='utf-8') as f:
        f.write(report_text)
    
    print("✓ 综合评估报告已保存: nasa_topics_comprehensive_report.md")
    
    return report_text

def main():
    """主函数"""
    print("=== NASA Topics综合评估 ===")
    
    # 加载所有结果
    results_dict = load_all_results()
    
    if not results_dict:
        print("未找到任何实验结果文件")
        return
    
    # 计算综合指标
    metrics = calculate_comprehensive_metrics(results_dict)
    
    if not metrics:
        print("无法计算任何指标")
        return
    
    # 创建可视化
    create_comparison_visualizations(metrics)
    
    # 生成报告
    report = generate_comprehensive_report(metrics)
    
    # 保存完整指标
    with open("nasa_topics_comprehensive_metrics.json", 'w', encoding='utf-8') as f:
        json.dump(metrics, f, indent=2, ensure_ascii=False)
    
    print("\n✓ 综合评估完成")
    print("生成文件:")
    print("  - nasa_topics_comprehensive_comparison.png")
    print("  - nasa_topics_comprehensive_report.md")
    print("  - nasa_topics_comprehensive_metrics.json")

if __name__ == "__main__":
    main()
