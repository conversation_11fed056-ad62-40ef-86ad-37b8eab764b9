# DCL项目运行指南

## 项目概述
DCL (Retrieval-style In-Context Learning) 是一个用于少样本层次文本分类的深度学习项目，发表在TACL2024会议上。该项目结合了检索式上下文学习和层次分类，通过对比学习来提升少样本场景下的层次文本分类性能。

## 环境要求
- Python 3.9+
- CUDA支持的GPU（推荐）
- 至少8GB内存

## 1. 环境安装

### 创建虚拟环境
```bash
conda create -n htc python=3.9
conda activate htc
```

### 安装依赖包
```bash
pip install torch tqdm openprompt transformers==4.35.2 scikit-learn
```

## 2. 数据准备

### WOS数据集结构
项目使用Web of Science (WOS) 数据集，包含两层标签结构：
- **Level 0 (粗粒度)**: 7个主要领域 (CS, Medical, Civil, ECE, biochemistry, MAE, Psychology)
- **Level 1 (细粒度)**: 134个具体子领域

### 数据文件位置
确保以下数据文件位于正确位置：
```
dataset/WebOfScience/
├── wos_train.json      # 训练数据 (30,071条)
├── wos_val.json        # 验证数据
├── wos_test.json       # 测试数据
├── slot.pt             # 层次结构映射文件
└── formatted_data/
    ├── label0.txt      # Level 0标签列表
    └── label1.txt      # Level 1标签列表
```

### 数据格式说明
每条数据包含以下字段：
```json
{
    "doc_token": "文档文本内容",
    "doc_label": ["粗粒度标签", "细粒度标签"],
    "doc_topic": [],
    "doc_keyword": []
}
```

## 3. 运行步骤

### Step 1: 训练索引器
```bash
python train.py --dataset wos --shot 16 --seed 171 --contrastive_loss 1 --contrastive_alpha 0.99
```

**主要参数说明：**
- `--dataset`: 数据集名称 (wos/dbp)
- `--shot`: few-shot样本数量，默认16
- `--seed`: 随机种子，默认171
- `--contrastive_loss`: 是否使用DCL对比损失，1为启用
- `--contrastive_alpha`: 对比损失权重，默认0.99
- `--lr`: 主模型学习率，默认5e-5
- `--lr2`: verbalizer学习率，默认1e-4
- `--batch_size`: 批次大小，默认5
- `--max_epochs`: 最大训练轮数，默认20
- `--early_stop`: 早停轮数，默认10

**训练过程：**
1. 加载WOS数据集和处理器
2. 构建层次化模板和verbalizer
3. 初始化BERT模型和优化器
4. 训练循环：计算分类损失、DCL损失、语言模型损失
5. 验证集评估和早停
6. 保存最佳模型到`ckpts/`目录

### Step 2: 提取嵌入表示
```bash
python embedding.py --dataset wos --shot 16 --seed 171
```

**功能说明：**
1. 加载训练好的模型
2. 对训练数据进行推理，提取文本嵌入表示
3. 保存嵌入向量用于后续检索

### Step 3: Top-K检索和评估
```bash
python topk.py --dataset wos --shot 16 --seed 171 --topk 1 --label_description 1
```

**主要参数：**
- `--topk`: 检索的相似样本数量
- `--label_description`: 是否使用标签描述，1为启用

**功能说明：**
1. 加载预计算的嵌入向量
2. 对测试集进行检索式评估
3. 为每个测试样本找到最相似的训练样本
4. 输出分类性能指标

## 4. 关键配置参数

### 模型参数
```python
# 核心架构参数
--multi_mask 1              # 使用多mask模板
--multi_verb 1              # 使用多verbalizer
--depth 2                   # 层级深度
--use_hier_mean 1           # 使用层次均值初始化

# 损失函数参数
--contrastive_loss 1        # 启用DCL对比损失
--contrastive_alpha 0.99    # 对比损失权重
--contrastive_level 1       # 对比损失层级
--lm_training 1             # 启用语言模型训练
--lm_alpha 0.999           # 语言模型损失权重

# 优化参数
--lr 5e-5                   # 主模型学习率
--lr2 1e-4                  # verbalizer学习率
--batch_size 5              # 批次大小
--max_grad_norm 1.0         # 梯度裁剪
```

### 数据参数
```python
--shot 16                   # few-shot样本数
--seed 171                  # 随机种子
--shuffle 0                 # 是否打乱训练数据
--max_seq_lens 512          # 最大序列长度
```

## 5. 输出文件说明

### 模型文件
- `ckpts/{run_id}-macro.ckpt`: 最佳macro F1模型
- `ckpts/{run_id}-micro.ckpt`: 最佳micro F1模型

### 结果文件
- `result/few_shot_train.txt`: 训练和测试结果记录
- `{dataset}_similar_samples/`: 检索到的相似样本

### 嵌入文件
- `_{shot}shot_none_{seed}_embed_doc_{label_description}.pkl`: 训练数据嵌入向量

## 6. 评估指标

项目使用多种评估指标：
- **Macro F1**: 宏平均F1分数
- **Micro F1**: 微平均F1分数
- **准确率**: 精确匹配准确率
- **层次化评估**: 考虑层次结构的评估指标

## 7. 常见问题解决

### 内存不足
- 减小`batch_size`参数
- 减少`max_seq_lens`长度
- 使用梯度累积：增加`gradient_accumulation_steps`

### 训练不收敛
- 调整学习率`lr`和`lr2`
- 修改对比损失权重`contrastive_alpha`
- 增加训练轮数`max_epochs`

### 数据加载错误
- 检查数据文件路径是否正确
- 确认数据格式符合要求
- 验证标签文件完整性

## 8. 高级用法

### 自定义数据集
1. 按照WOS格式准备数据文件
2. 修改`processor.py`中的数据处理逻辑
3. 更新标签映射文件

### 调整模型架构
1. 修改`models/hierVerb.py`中的模型结构
2. 调整损失函数权重
3. 自定义verbalizer初始化策略

### 实验对比
1. 使用不同的`seed`值进行多次实验
2. 调整`shot`参数比较不同few-shot设置
3. 开启/关闭`contrastive_loss`进行消融实验

## 9. 性能优化建议

1. **GPU使用**: 确保CUDA可用，设置合适的`device`参数
2. **数据预处理**: 预先生成dataloader文件以加速训练
3. **模型保存**: 定期保存检查点，避免训练中断
4. **参数调优**: 使用网格搜索或贝叶斯优化调整超参数

## 10. 引用信息

如果您使用了这个项目，请引用：
```
Retrieval-style In-Context Learning for Few-shot Hierarchical Text Classification
TACL 2024
```
