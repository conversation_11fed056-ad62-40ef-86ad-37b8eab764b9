"""
快速测试新封装的 SiliconFlow 客户端
基于原始测试代码的风格，验证新客户端的功能
"""

import os
import sys
from siliconflow_client import SiliconFlowClient, SiliconFlowChatClient, SiliconFlowEmbeddingClient
from config import SiliconFlowConfig

def quick_test():
    """快速测试函数，类似原始代码的风格"""
    
    # API 配置 - 请替换为您的实际 API 密钥
    API_KEY = ""  # 在这里填入您的 SiliconFlow API 密钥
    
    if not API_KEY:
        # 尝试从环境变量获取
        API_KEY = os.getenv("SILICONFLOW_API_KEY")
    
    if not API_KEY:
        print("❌ 请设置 API_KEY 或环境变量 SILICONFLOW_API_KEY")
        return
    
    print("🚀 SiliconFlow 新客户端快速测试")
    print("=" * 50)
    
    # 测试1: 统一客户端
    print("\n📦 测试1: 统一客户端")
    print("-" * 30)
    
    try:
        # 创建统一客户端
        client = SiliconFlowClient(api_key=API_KEY)
        
        # 测试连接
        print("🔌 测试连接状态...")
        connection_results = client.test_connection()
        
        if connection_results.get("chat", False):
            print("✅ 推理模型连接成功")
        else:
            print("❌ 推理模型连接失败")
        
        if connection_results.get("embeddings", False):
            print("✅ 嵌入模型连接成功")
        else:
            print("❌ 嵌入模型连接失败")
            
    except Exception as e:
        print(f"❌ 统一客户端测试失败: {e}")
    
    # 测试2: 推理模型
    print("\n🤖 测试2: 推理模型")
    print("-" * 30)
    
    try:
        chat_client = SiliconFlowChatClient(api_key=API_KEY)
        
        # 简单对话测试
        print("💬 测试简单对话...")
        response = chat_client.simple_chat("你好，请简单介绍一下自己")
        
        if response:
            print(f"✅ 推理成功!")
            print(f"📝 回复内容: {response[:100]}...")
        else:
            print("❌ 推理失败!")
            
        # 多轮对话测试
        print("\n💬 测试多轮对话...")
        messages = [
            {"role": "user", "content": "什么是人工智能？"},
            {"role": "assistant", "content": "人工智能是计算机科学的一个分支..."},
            {"role": "user", "content": "能举个例子吗？"}
        ]
        
        result = chat_client.chat_completions(
            messages=messages,
            temperature=0.7,
            max_tokens=200
        )
        
        if result and "choices" in result:
            content = result["choices"][0]["message"]["content"]
            print(f"✅ 多轮对话成功!")
            print(f"📝 回复内容: {content[:100]}...")
            
            # 显示使用统计
            if "usage" in result:
                usage = result["usage"]
                print(f"📊 Token使用: 输入={usage.get('prompt_tokens', 'N/A')}, "
                      f"输出={usage.get('completion_tokens', 'N/A')}, "
                      f"总计={usage.get('total_tokens', 'N/A')}")
        else:
            print("❌ 多轮对话失败!")
            
    except Exception as e:
        print(f"❌ 推理模型测试失败: {e}")
    
    # 测试3: 嵌入模型
    print("\n🔤 测试3: 嵌入模型")
    print("-" * 30)
    
    try:
        embedding_client = SiliconFlowEmbeddingClient(api_key=API_KEY)
        
        # 单个文本嵌入测试
        print("📄 测试单个文本嵌入...")
        text = "人工智能是计算机科学的重要分支"
        vector = embedding_client.get_embedding_vector(text)
        
        if vector:
            print(f"✅ 单个文本嵌入成功!")
            print(f"📊 向量维度: {len(vector)}")
            print(f"🔢 向量前5个值: {vector[:5]}")
        else:
            print("❌ 单个文本嵌入失败!")
        
        # 批量文本嵌入测试
        print("\n📚 测试批量文本嵌入...")
        texts = [
            "机器学习是人工智能的核心技术",
            "深度学习使用神经网络进行学习",
            "自然语言处理帮助计算机理解人类语言"
        ]
        
        batch_result = embedding_client.create_embeddings(texts)
        
        if batch_result and "data" in batch_result:
            print(f"✅ 批量文本嵌入成功!")
            print(f"📊 处理文本数量: {len(batch_result['data'])}")
            
            for i, item in enumerate(batch_result["data"]):
                print(f"   文本{i+1}向量维度: {len(item['embedding'])}")
        else:
            print("❌ 批量文本嵌入失败!")
        
        # 文本相似度测试
        if vector and batch_result:
            print("\n🔍 测试文本相似度计算...")
            
            # 使用嵌入客户端的相似度方法
            similarity = embedding_client.get_text_similarity(
                "人工智能是计算机科学的重要分支",
                "机器学习是人工智能的核心技术"
            )
            
            if similarity is not None:
                print(f"✅ 相似度计算成功!")
                print(f"📊 文本相似度: {similarity:.4f}")
            else:
                print("❌ 相似度计算失败!")
            
    except Exception as e:
        print(f"❌ 嵌入模型测试失败: {e}")
    
    # 测试4: 配置管理
    print("\n⚙️ 测试4: 配置管理")
    print("-" * 30)
    
    try:
        # 创建配置
        config = SiliconFlowConfig()
        config.set("api_key", API_KEY)
        
        # 验证配置
        is_valid, message = config.validate()
        print(f"📋 配置验证: {message}")
        
        # 显示可用模型
        print("🎯 可用模型:")
        print(f"   聊天模型: {config.get_available_chat_models()[:3]}...")  # 只显示前3个
        print(f"   嵌入模型: {config.get_available_embedding_models()[:3]}...")  # 只显示前3个
        
        # 使用配置创建客户端
        config_client = SiliconFlowClient(config=config)
        model_info = config_client.get_model_info()
        print(f"🔧 当前配置:")
        print(f"   默认聊天模型: {model_info['default_chat_model']}")
        print(f"   默认嵌入模型: {model_info['default_embedding_model']}")
        
    except Exception as e:
        print(f"❌ 配置管理测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("📊 快速测试完成!")
    print("💡 如需更详细的测试，请运行: python test_siliconflow.py")
    print("📖 查看使用示例，请运行: python example_usage.py")


def main():
    """主函数，类似原始代码的结构"""
    
    # 禁用不安全请求的警告（保持与原始代码一致）
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    print("🌟 SiliconFlow API 新客户端快速验证")
    print("基于原始测试代码封装的推理模型和嵌入模型客户端")
    print("=" * 60)
    
    # 运行快速测试
    quick_test()
    
    print("\n🎉 测试完成!")
    print("\n📚 更多功能:")
    print("1. 完整测试套件: python test_siliconflow.py")
    print("2. 详细使用示例: python example_usage.py") 
    print("3. 查看文档: README_SiliconFlow.md")


if __name__ == "__main__":
    main()
