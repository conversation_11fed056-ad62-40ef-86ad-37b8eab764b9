#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
层次文本分类 - 基于Fine-tuned GPT-2模型实现（优化版）

本脚本实现了使用GPT-2模型进行层次文本分类的完整流程，包括：
1. 数据预处理和标签编码
2. 层次化模型架构（共享编码器）
3. 层次约束损失函数
4. 分阶段训练策略
5. 层次感知评估指标
6. WandB实验跟踪

作者: eedisgpu, Bouchiha
创建时间: 2024年7月-8月
优化时间: 2024年12月
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, f1_score
from transformers import GPT2Tokenizer, GPT2Model, Trainer, TrainingArguments, DataCollatorWithPadding
import torch
import torch.nn as nn
from torch.utils.data import Dataset
import wandb
import os
from datetime import datetime

# ==================== WandB初始化 ====================
"""
WandB配置：
- 项目名称和实验配置
- 超参数记录
- 模型监控设置
"""

# 设置WandB环境
os.environ["WANDB_MODE"] = "online"

# 初始化WandB项目
wandb.init(
    project="hierarchical-gpt2-classification",
    name=f"hierarchical-gpt2-{datetime.now().strftime('%Y%m%d-%H%M%S')}",
    config={
        "model_name": "gpt2",
        "max_length": 512,
        "batch_size": 1,
        "stage1_epochs": 2,
        "stage2_epochs": 3,
        "stage1_warmup_steps": 300,
        "stage2_warmup_steps": 500,
        "weight_decay": 0.01,
        "stage2_learning_rate": 1e-5,
        "hierarchical_loss_alpha": 0.1,
        "dropout_rate": 0.1,
        "shared_hidden_size": 512,
    },
    tags=["hierarchical-classification", "gpt2", "text-classification"]
)

config = wandb.config

# ==================== 数据加载和预处理 ====================
"""
数据预处理阶段：
1. 加载原始数据集
2. 构建层次化文本表示
3. 为GPT-2模型准备输入格式
"""

print("加载数据集...")
# 加载训练数据集（包含文本、类别、超类别信息）
data = pd.read_csv('train_40k_Adapted.csv')

# 记录数据集统计信息
wandb.log({
    "dataset_size": len(data),
    "num_categories": data['category'].nunique(),
    "num_super_categories": data['super_category'].nunique(),
})

print(f"数据集大小: {len(data)}")
print(f"类别数量: {data['category'].nunique()}")
print(f"超类别数量: {data['super_category'].nunique()}")

# ==================== 层次化映射关系构建 ====================
def build_hierarchy_mapping(data):
    """
    构建类别到超类别的映射关系
    
    参数:
    - data: 包含category和super_category列的DataFrame
    
    返回:
    - category_to_super: 类别到超类别的映射字典
    """
    category_to_super = {}
    for _, row in data.iterrows():
        cat = row['category']
        super_cat = row['super_category']
        if cat not in category_to_super:
            category_to_super[cat] = super_cat
    
    return category_to_super

def create_hierarchical_text(text, category, super_category):
    """
    创建包含层次信息的文本表示
    
    参数:
    - text: 原始文本
    - category: 类别标签
    - super_category: 超类别标签
    
    返回:
    - hierarchical_text: 增强的层次化文本
    """
    return f"{text} [SEP] Category: {category} [SEP] Super Category: {super_category}"

# 构建层次映射关系
hierarchy_mapping = build_hierarchy_mapping(data)

# 构建层次化文本表示
data['hierarchical_text'] = data.apply(
    lambda row: create_hierarchical_text(row['text'], row['category'], row['super_category']), 
    axis=1
)

# ==================== 分词器和模型初始化 ====================
"""
GPT-2模型配置：
- 使用预训练的GPT-2分词器和模型
- 设置填充标记以处理不同长度的文本
- 配置序列分类任务
"""

print("初始化分词器...")
# 初始化GPT-2分词器
tokenizer = GPT2Tokenizer.from_pretrained(config.model_name)
# GPT-2原本没有填充标记，使用结束标记作为填充标记
tokenizer.pad_token = tokenizer.eos_token

# ==================== 层次化GPT-2模型架构 ====================
class HierarchicalGPT2Model(nn.Module):
    """
    层次化GPT-2模型：共享编码器，分离分类头
    
    架构特点：
    1. 共享GPT-2编码器提取通用特征
    2. 独立的分类头处理不同层级的分类任务
    3. 支持层次约束损失计算
    """
    def __init__(self, model_name, num_categories, num_super_categories, config):
        super().__init__()
        self.gpt2 = GPT2Model.from_pretrained(model_name)
        self.dropout = nn.Dropout(config.dropout_rate)
        
        # 共享特征提取层
        self.shared_classifier = nn.Linear(self.gpt2.config.hidden_size, config.shared_hidden_size)
        
        # 分层分类头
        self.category_classifier = nn.Linear(config.shared_hidden_size, num_categories)
        self.super_category_classifier = nn.Linear(config.shared_hidden_size, num_super_categories)
        
        # 调整词汇表大小
        self.gpt2.resize_token_embeddings(len(tokenizer))
        
    def forward(self, input_ids, attention_mask=None):
        """
        前向传播
        
        参数:
        - input_ids: 输入token序列
        - attention_mask: 注意力掩码
        
        返回:
        - category_logits: 类别分类logits
        - super_category_logits: 超类别分类logits
        """
        outputs = self.gpt2(input_ids=input_ids, attention_mask=attention_mask)
        # 使用最后一个token的隐藏状态
        pooled_output = outputs.last_hidden_state[:, -1, :]
        
        # 共享特征提取
        shared_features = self.dropout(torch.relu(self.shared_classifier(pooled_output)))
        
        # 分层分类
        category_logits = self.category_classifier(shared_features)
        super_category_logits = self.super_category_classifier(shared_features)
        
        return category_logits, super_category_logits

# ==================== 层次约束损失函数 ====================
def hierarchical_constraint_loss(category_logits, super_category_logits, 
                               category_labels, super_category_labels, 
                               category_to_super_idx, alpha=0.1):
    """
    层次约束损失：确保子类别预测与父类别预测一致
    
    参数:
    - category_logits: 类别分类logits
    - super_category_logits: 超类别分类logits
    - category_labels: 真实类别标签
    - super_category_labels: 真实超类别标签
    - category_to_super_idx: 类别索引到超类别索引的映射
    - alpha: 层次一致性损失权重
    
    返回:
    - total_loss: 总损失（分类损失 + 层次约束损失）
    - loss_components: 损失组件字典
    """
    # 标准分类损失
    ce_loss = nn.CrossEntropyLoss()
    cat_loss = ce_loss(category_logits, category_labels)
    super_cat_loss = ce_loss(super_category_logits, super_category_labels)
    
    # 层次一致性损失
    batch_size = category_logits.size(0)
    consistency_loss = 0
    
    for i in range(batch_size):
        # 获取类别预测概率
        cat_probs = torch.softmax(category_logits[i], dim=0)
        super_cat_probs = torch.softmax(super_category_logits[i], dim=0)
        
        # 计算从类别到超类别的期望概率
        expected_super_probs = torch.zeros_like(super_cat_probs)
        for cat_idx, super_idx in category_to_super_idx.items():
            expected_super_probs[super_idx] += cat_probs[cat_idx]
        
        # KL散度损失
        consistency_loss += torch.nn.functional.kl_div(
            torch.log(super_cat_probs + 1e-8), expected_super_probs, reduction='sum'
        )
    
    consistency_loss = consistency_loss / batch_size
    total_loss = cat_loss + super_cat_loss + alpha * consistency_loss
    
    # 返回损失组件用于记录
    loss_components = {
        'category_loss': cat_loss.item(),
        'super_category_loss': super_cat_loss.item(),
        'consistency_loss': consistency_loss.item(),
        'total_loss': total_loss.item()
    }
    
    return total_loss, loss_components

# ==================== 自定义数据集类 ====================
class HierarchicalDataset(Dataset):
    """
    层次化数据集类
    
    功能：
    1. 封装编码后的文本数据和双层标签
    2. 提供批量数据加载接口
    3. 支持层次化模型训练
    """
    def __init__(self, encodings, category_labels, super_category_labels):
        """
        初始化数据集
        
        参数:
        - encodings: 分词器编码后的文本数据
        - category_labels: 类别标签
        - super_category_labels: 超类别标签
        """
        self.encodings = encodings
        self.category_labels = category_labels
        self.super_category_labels = super_category_labels
    
    def __len__(self):
        """返回数据集大小"""
        return len(self.category_labels)
    
    def __getitem__(self, idx):
        """
        获取单个数据样本
        
        返回:
        - item: 包含input_ids, attention_mask和双层标签的字典
        """
        item = {key: torch.tensor(val[idx]) for key, val in self.encodings.items()}
        item['category_labels'] = torch.tensor(self.category_labels[idx])
        item['super_category_labels'] = torch.tensor(self.super_category_labels[idx])
        return item

# ==================== 标签编码 ====================
"""
标签数字化处理：
将文本标签转换为数字索引，便于模型训练
"""

print("编码标签...")
# 为类别标签创建映射字典（文本标签 -> 数字索引）
category_labels = {label: i for i, label in enumerate(data['category'].unique())}
# 为超类别标签创建映射字典
super_category_labels = {label: i for i, label in enumerate(data['super_category'].unique())}

# 构建类别索引到超类别索引的映射
category_to_super_idx = {}
for cat_text, cat_idx in category_labels.items():
    super_cat_text = hierarchy_mapping[cat_text]
    super_cat_idx = super_category_labels[super_cat_text]
    category_to_super_idx[cat_idx] = super_cat_idx

# 将文本标签转换为数字标签
labels_category = data['category'].map(category_labels).tolist()
labels_super_category = data['super_category'].map(super_category_labels).tolist()

# ==================== 数据集划分 ====================
"""
训练/测试集划分：
- 70%用于训练，30%用于测试
- 同时划分类别和超类别标签
- 使用固定随机种子确保结果可复现
"""

print("划分数据集...")
train_texts, test_texts, train_labels_category, test_labels_category, train_labels_super_category, test_labels_super_category = train_test_split(
    data['hierarchical_text'].tolist(), labels_category, labels_super_category, 
    test_size=0.3, random_state=42)

# 记录数据集划分信息
wandb.log({
    "train_size": len(train_texts),
    "test_size": len(test_texts),
    "train_ratio": len(train_texts) / len(data),
    "test_ratio": len(test_texts) / len(data)
})

# ==================== 文本编码 ====================
"""
文本预处理：
- 截断：限制最大长度为512个token
- 填充：统一序列长度便于批处理
- 生成attention_mask：标识有效token位置
"""

print("编码文本...")
# 编码训练集和测试集
train_encodings = tokenizer(train_texts, truncation=True, padding=True, max_length=config.max_length)
test_encodings = tokenizer(test_texts, truncation=True, padding=True, max_length=config.max_length)

# ==================== 创建数据集对象 ====================
"""
将编码后的数据封装为PyTorch数据集
"""

# 层次化数据集
train_dataset = HierarchicalDataset(train_encodings, train_labels_category, train_labels_super_category)
test_dataset = HierarchicalDataset(test_encodings, test_labels_category, test_labels_super_category)

# ==================== 自定义训练器 ====================
class HierarchicalTrainer(Trainer):
    """
    层次化训练器：支持层次约束损失和WandB记录
    """
    def __init__(self, category_to_super_idx, stage_name="", *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.category_to_super_idx = category_to_super_idx
        self.stage_name = stage_name
        self.step_count = 0
    
    def compute_loss(self, model, inputs, return_outputs=False):
        """
        计算层次约束损失并记录到WandB
        """
        category_labels = inputs.pop("category_labels")
        super_category_labels = inputs.pop("super_category_labels")
        
        category_logits, super_category_logits = model(**inputs)
        
        loss, loss_components = hierarchical_constraint_loss(
            category_logits, super_category_logits,
            category_labels, super_category_labels,
            self.category_to_super_idx,
            alpha=config.hierarchical_loss_alpha
        )
        
        # 记录损失组件到WandB
        if self.state.global_step % self.args.logging_steps == 0:
            wandb.log({
                f"{self.stage_name}/category_loss": loss_components['category_loss'],
                f"{self.stage_name}/super_category_loss": loss_components['super_category_loss'],
                f"{self.stage_name}/consistency_loss": loss_components['consistency_loss'],
                f"{self.stage_name}/total_loss": loss_components['total_loss'],
                f"{self.stage_name}/step": self.state.global_step
            })
        
        return (loss, (category_logits, super_category_logits)) if return_outputs else loss

# ==================== 分阶段训练策略 ====================
def staged_training(model, train_dataset, test_dataset, category_to_super_idx):
    """
    分阶段训练：先训练超类别，再联合训练
    
    参数:
    - model: 层次化GPT-2模型
    - train_dataset: 训练数据集
    - test_dataset: 测试数据集
    - category_to_super_idx: 类别到超类别的索引映射
    
    返回:
    - trained_model: 训练完成的模型
    """
    
    # 数据整理器
    data_collator = DataCollatorWithPadding(tokenizer=tokenizer)
    
    # 第一阶段：只训练超类别分类
    print("第一阶段：训练超类别分类...")
    wandb.log({"training_stage": "stage1_super_category"})
    
    # 冻结类别分类头
    for param in model.category_classifier.parameters():
        param.requires_grad = False
    
    # 第一阶段训练参数
    training_args_stage1 = TrainingArguments(
        output_dir='./stage1_results',
        num_train_epochs=config.stage1_epochs,
        per_device_train_batch_size=config.batch_size,
        per_device_eval_batch_size=config.batch_size,
        warmup_steps=config.stage1_warmup_steps,
        weight_decay=config.weight_decay,
        logging_dir='./logs_stage1',
        logging_steps=10,
        evaluation_strategy="epoch",
        save_strategy="no",
        load_best_model_at_end=False,
        report_to=None,  # 禁用自动报告，手动控制WandB记录
    )
    
    trainer_stage1 = HierarchicalTrainer(
        category_to_super_idx=category_to_super_idx,
        stage_name="stage1",
        model=model,
        args=training_args_stage1,
        train_dataset=train_dataset,
        eval_dataset=test_dataset,
        data_collator=data_collator
    )
    
    # 监控模型参数
    wandb.watch(model, log="all", log_freq=100)
    
    trainer_stage1.train()
    
    # 第二阶段：联合训练
    print("第二阶段：联合训练...")
    wandb.log({"training_stage": "stage2_joint_training"})
    
    # 解冻所有参数
    for param in model.parameters():
        param.requires_grad = True
    
    # 第二阶段训练参数
    training_args_stage2 = TrainingArguments(
        output_dir='./stage2_results',
        num_train_epochs=config.stage2_epochs,
        per_device_train_batch_size=config.batch_size,
        per_device_eval_batch_size=config.batch_size,
        warmup_steps=config.stage2_warmup_steps,
        weight_decay=config.weight_decay,
        learning_rate=config.stage2_learning_rate,
        logging_dir='./logs_stage2',
        logging_steps=10,
        evaluation_strategy="epoch",
        save_strategy="no",
        load_best_model_at_end=False,
        report_to=None,  # 禁用自动报告，手动控制WandB记录
    )
    
    trainer_stage2 = HierarchicalTrainer(
        category_to_super_idx=category_to_super_idx,
        stage_name="stage2",
        model=model,
        args=training_args_stage2,
        train_dataset=train_dataset,
        eval_dataset=test_dataset,
        data_collator=data_collator
    )
    trainer_stage2.train()
    
    return model, trainer_stage2

# ==================== 模型初始化和训练 ====================
"""
层次化GPT-2模型初始化和分阶段训练
"""

print("初始化模型...")
# 初始化层次化模型
model = HierarchicalGPT2Model(
    model_name=config.model_name,
    num_categories=len(category_labels),
    num_super_categories=len(super_category_labels),
    config=config
)

# 记录模型参数数量
total_params = sum(p.numel() for p in model.parameters())
trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

wandb.log({
    "model_total_parameters": total_params,
    "model_trainable_parameters": trainable_params,
    "model_architecture": "HierarchicalGPT2Model"
})

print(f"模型总参数: {total_params:,}")
print(f"可训练参数: {trainable_params:,}")

model.to('cuda')

print("开始分阶段训练...")
trained_model, final_trainer = staged_training(
    model, train_dataset, test_dataset, category_to_super_idx
)

# ==================== 模型评估 ====================
"""
评估过程：
1. 使用训练好的模型对测试集进行预测
2. 获取预测概率分布
3. 转换为预测标签
4. 计算层次感知评估指标
"""

print("正在评估模型性能...")
wandb.log({"evaluation_stage": "final_evaluation"})

# 获取预测结果
predictions = final_trainer.predict(test_dataset)
category_logits, super_category_logits = predictions.predictions

# 将预测概率转换为预测标签
y_pred_cat = np.argmax(category_logits, axis=1)
y_true_cat = np.array(test_labels_category)

y_pred_super_cat = np.argmax(super_category_logits, axis=1)
y_true_super_cat = np.array(test_labels_super_category)

# ==================== 层次感知评估指标 ====================
def hierarchical_metrics(y_true_cat, y_pred_cat, y_true_super_cat, y_pred_super_cat, 
                        category_to_super_idx):
    """
    计算层次化评估指标
    
    参数:
    - y_true_cat: 真实类别标签
    - y_pred_cat: 预测类别标签
    - y_true_super_cat: 真实超类别标签
    - y_pred_super_cat: 预测超类别标签
    - category_to_super_idx: 类别到超类别的索引映射
    
    返回:
    - metrics: 包含各种层次化指标的字典
    """
    # 严格层次F1（两级都正确）
    strict_correct = (y_true_cat == y_pred_cat) & (y_true_super_cat == y_pred_super_cat)
    strict_f1 = f1_score(strict_correct, [True] * len(strict_correct), average='weighted')
    
    # 层次一致性（预测的类别与超类别匹配）
    consistency_correct = []
    for i in range(len(y_pred_cat)):
        pred_cat = y_pred_cat[i]
        pred_super = y_pred_super_cat[i]
        expected_super = category_to_super_idx.get(pred_cat, -1)
        consistency_correct.append(pred_super == expected_super)
    
    consistency_rate = sum(consistency_correct) / len(consistency_correct)
    
    # 部分层次F1（至少一级正确）
    partial_correct = (y_true_cat == y_pred_cat) | (y_true_super_cat == y_pred_super_cat)
    partial_f1 = f1_score(partial_correct, [True] * len(partial_correct), average='weighted')
    
    # 单独的准确率和F1分数
    category_accuracy = accuracy_score(y_true_cat, y_pred_cat)
    category_f1 = f1_score(y_true_cat, y_pred_cat, average='weighted')
    super_category_accuracy = accuracy_score(y_true_super_cat, y_pred_super_cat)
    super_category_f1 = f1_score(y_true_super_cat, y_pred_super_cat, average='weighted')
    
    return {
        'category_accuracy': category_accuracy,
        'category_f1': category_f1,
        'super_category_accuracy': super_category_accuracy,
        'super_category_f1': super_category_f1,
        'strict_hierarchical_f1': strict_f1,
        'consistency_rate': consistency_rate,
        'partial_hierarchical_f1': partial_f1
    }

# ==================== 性能指标计算 ====================
"""
计算层次感知评估指标并记录到WandB
"""

metrics = hierarchical_metrics(
    y_true_cat, y_pred_cat, y_true_super_cat, y_pred_super_cat, 
    category_to_super_idx
)

# 记录最终评估指标到WandB
wandb.log({
    "final_metrics/category_accuracy": metrics["category_accuracy"],
    "final_metrics/category_f1": metrics["category_f1"],
    "final_metrics/super_category_accuracy": metrics["super_category_accuracy"],
    "final_metrics/super_category_f1": metrics["super_category_f1"],
    "final_metrics/strict_hierarchical_f1": metrics["strict_hierarchical_f1"],
    "final_metrics/consistency_rate": metrics["consistency_rate"],
    "final_metrics/partial_hierarchical_f1": metrics["partial_hierarchical_f1"]
})

# ==================== 模型保存 ====================
print("保存训练完成的模型...")

# 保存层次化模型
model_save_path = './best_hierarchical_model.pth'
torch.save(trained_model.state_dict(), model_save_path)
tokenizer.save_pretrained('./best_hierarchical_model')

# 保存模型到WandB
wandb.save(model_save_path)
wandb.save('./best_hierarchical_model/*')

print("模型保存完成！")

# ==================== 结果输出 ====================
print("\n\n*******   优化版 Fine-tuned GPT2 层次分类   -GPU  ******")
print(f'类别准确率 (Category Accuracy): {metrics["category_accuracy"]:.4f}')
print(f'类别F1分数 (Category F1 Score): {metrics["category_f1"]:.4f}')
print(f'超类别准确率 (Super Category Accuracy): {metrics["super_category_accuracy"]:.4f}')
print(f'超类别F1分数 (Super Category F1 Score): {metrics["super_category_f1"]:.4f}')
print(f'严格层次F1分数 (Strict Hierarchical F1): {metrics["strict_hierarchical_f1"]:.4f}')
print(f'层次一致性率 (Consistency Rate): {metrics["consistency_rate"]:.4f}')
print(f'部分层次F1分数 (Partial Hierarchical F1): {metrics["partial_hierarchical_f1"]:.4f}')
print("*" * 60)

# 创建结果摘要表格
results_table = wandb.Table(
    columns=["Metric", "Value"],
    data=[
        ["Category Accuracy", f"{metrics['category_accuracy']:.4f}"],
        ["Category F1 Score", f"{metrics['category_f1']:.4f}"],
        ["Super Category Accuracy", f"{metrics['super_category_accuracy']:.4f}"],
        ["Super Category F1 Score", f"{metrics['super_category_f1']:.4f}"],
        ["Strict Hierarchical F1", f"{metrics['strict_hierarchical_f1']:.4f}"],
        ["Consistency Rate", f"{metrics['consistency_rate']:.4f}"],
        ["Partial Hierarchical F1", f"{metrics['partial_hierarchical_f1']:.4f}"]
    ]
)

wandb.log({"results_summary": results_table})

# 结束WandB运行
wandb.finish()

print("WandB实验记录完成！")

