{"cells": [{"cell_type": "markdown", "metadata": {"vscode": {"languageId": "markdown"}}, "source": ["# Multi-Modal RAG with Image Captioning\n", "\n", "In this notebook, I implement a Multi-Modal RAG system that extracts both text and images from documents, generates captions for images, and uses both content types to respond to queries. This approach enhances traditional RAG by incorporating visual information into the knowledge base.\n", "\n", "Traditional RAG systems only work with text, but many documents contain crucial information in images, charts, and tables. By captioning these visual elements and incorporating them into our retrieval system, we can:\n", "\n", "- Access information locked in figures and diagrams\n", "- Understand tables and charts that complement the text\n", "- Create a more comprehensive knowledge base\n", "- Answer questions that rely on visual data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the Environment\n", "We begin by importing necessary libraries."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import io\n", "import numpy as np\n", "import json\n", "import fitz\n", "from PIL import Image\n", "from openai import OpenAI\n", "import base64\n", "import re\n", "import tempfile\n", "import shutil"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the OpenAI API Client\n", "We initialize the OpenAI client to generate embeddings and responses."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize the OpenAI client with the base URL and API key\n", "client = OpenAI(\n", "    base_url=\"https://api.studio.nebius.com/v1/\",\n", "    api_key=os.getenv(\"OPENAI_API_KEY\")  # Retrieve the API key from environment variables\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Document Processing Functions"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def extract_content_from_pdf(pdf_path, output_dir=None):\n", "    \"\"\"\n", "    Extract both text and images from a PDF file.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to the PDF file\n", "        output_dir (str, optional): Directory to save extracted images\n", "        \n", "    Returns:\n", "        Tuple[List[Dict], List[Dict]]: Text data and image data\n", "    \"\"\"\n", "    # Create a temporary directory for images if not provided\n", "    temp_dir = None\n", "    if output_dir is None:\n", "        temp_dir = tempfile.mkdtemp()\n", "        output_dir = temp_dir\n", "    else:\n", "        os.makedirs(output_dir, exist_ok=True)\n", "        \n", "    text_data = []  # List to store extracted text data\n", "    image_paths = []  # List to store paths of extracted images\n", "    \n", "    print(f\"Extracting content from {pdf_path}...\")\n", "    \n", "    try:\n", "        with fitz.open(pdf_path) as pdf_file:\n", "            # Loop through every page in the PDF\n", "            for page_number in range(len(pdf_file)):\n", "                page = pdf_file[page_number]\n", "                \n", "                # Extract text from the page\n", "                text = page.get_text().strip()\n", "                if text:\n", "                    text_data.append({\n", "                        \"content\": text,\n", "                        \"metadata\": {\n", "                            \"source\": pdf_path,\n", "                            \"page\": page_number + 1,\n", "                            \"type\": \"text\"\n", "                        }\n", "                    })\n", "                \n", "                # Extract images from the page\n", "                image_list = page.get_images(full=True)\n", "                for img_index, img in enumerate(image_list):\n", "                    xref = img[0]  # XREF of the image\n", "                    base_image = pdf_file.extract_image(xref)\n", "                    \n", "                    if base_image:\n", "                        image_bytes = base_image[\"image\"]\n", "                        image_ext = base_image[\"ext\"]\n", "                        \n", "                        # Save the image to the output directory\n", "                        img_filename = f\"page_{page_number+1}_img_{img_index+1}.{image_ext}\"\n", "                        img_path = os.path.join(output_dir, img_filename)\n", "                        \n", "                        with open(img_path, \"wb\") as img_file:\n", "                            img_file.write(image_bytes)\n", "                        \n", "                        image_paths.append({\n", "                            \"path\": img_path,\n", "                            \"metadata\": {\n", "                                \"source\": pdf_path,\n", "                                \"page\": page_number + 1,\n", "                                \"image_index\": img_index + 1,\n", "                                \"type\": \"image\"\n", "                            }\n", "                        })\n", "        \n", "        print(f\"Extracted {len(text_data)} text segments and {len(image_paths)} images\")\n", "        return text_data, image_paths\n", "    \n", "    except Exception as e:\n", "        print(f\"Error extracting content: {e}\")\n", "        if temp_dir and os.path.exists(temp_dir):\n", "            shutil.rmtree(temp_dir)\n", "        raise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chunking Text Content"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def chunk_text(text_data, chunk_size=1000, overlap=200):\n", "    \"\"\"\n", "    Split text data into overlapping chunks.\n", "    \n", "    Args:\n", "        text_data (List[Dict]): Text data extracted from PDF\n", "        chunk_size (int): Size of each chunk in characters\n", "        overlap (int): Overlap between chunks in characters\n", "        \n", "    Returns:\n", "        List[Dict]: Chunked text data\n", "    \"\"\"\n", "    chunked_data = []  # Initialize an empty list to store chunked data\n", "    \n", "    for item in text_data:\n", "        text = item[\"content\"]  # Extract the text content\n", "        metadata = item[\"metadata\"]  # Extract the metadata\n", "        \n", "        # Skip if text is too short\n", "        if len(text) < chunk_size / 2:\n", "            chunked_data.append({\n", "                \"content\": text,\n", "                \"metadata\": metadata\n", "            })\n", "            continue\n", "        \n", "        # Create chunks with overlap\n", "        chunks = []\n", "        for i in range(0, len(text), chunk_size - overlap):\n", "            chunk = text[i:i + chunk_size]  # Extract a chunk of the specified size\n", "            if chunk:  # Ensure we don't add empty chunks\n", "                chunks.append(chunk)\n", "        \n", "        # Add each chunk with updated metadata\n", "        for i, chunk in enumerate(chunks):\n", "            chunk_metadata = metadata.copy()  # Copy the original metadata\n", "            chunk_metadata[\"chunk_index\"] = i  # Add chunk index to metadata\n", "            chunk_metadata[\"chunk_count\"] = len(chunks)  # Add total chunk count to metadata\n", "            \n", "            chunked_data.append({\n", "                \"content\": chunk,  # The chunk text\n", "                \"metadata\": chunk_metadata  # The updated metadata\n", "            })\n", "    \n", "    print(f\"Created {len(chunked_data)} text chunks\")  # Print the number of created chunks\n", "    return chunked_data  # Return the list of chunked data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Image Captioning with OpenAI Vision"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def encode_image(image_path):\n", "    \"\"\"\n", "    Encode an image file as base64.\n", "    \n", "    Args:\n", "        image_path (str): Path to the image file\n", "        \n", "    Returns:\n", "        str: Base64 encoded image\n", "    \"\"\"\n", "    # Open the image file in binary read mode\n", "    with open(image_path, \"rb\") as image_file:\n", "        # Read the image file and encode it to base64\n", "        encoded_image = base64.b64encode(image_file.read())\n", "        # Decode the base64 bytes to a string and return\n", "        return encoded_image.decode('utf-8')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def generate_image_caption(image_path):\n", "    \"\"\"\n", "    Generate a caption for an image using OpenAI's vision capabilities.\n", "    \n", "    Args:\n", "        image_path (str): Path to the image file\n", "        \n", "    Returns:\n", "        str: Generated caption\n", "    \"\"\"\n", "    # Check if the file exists and is an image\n", "    if not os.path.exists(image_path):\n", "        return \"Error: Image file not found\"\n", "    \n", "    try:\n", "        # Open and validate the image\n", "        Image.open(image_path)\n", "        \n", "        # Encode the image to base64\n", "        base64_image = encode_image(image_path)\n", "        \n", "        # Create the API request to generate the caption\n", "        response = client.chat.completions.create(\n", "            model=\"llava-hf/llava-1.5-7b-hf\", # Use the llava-1.5-7b model\n", "            messages=[\n", "                {\n", "                    \"role\": \"system\",\n", "                    \"content\": \"You are an assistant specialized in describing images from academic papers. \"\n", "                    \"Provide detailed captions for the image that capture key information. \"\n", "                    \"If the image contains charts, tables, or diagrams, describe their content and purpose clearly. \"\n", "                    \"Your caption should be optimized for future retrieval when people ask questions about this content.\"\n", "                },\n", "                {\n", "                    \"role\": \"user\",\n", "                    \"content\": [\n", "                        {\"type\": \"text\", \"text\": \"Describe this image in detail, focusing on its academic content:\"},\n", "                        {\n", "                            \"type\": \"image_url\",\n", "                            \"image_url\": {\n", "                                \"url\": f\"data:image/jpeg;base64,{base64_image}\"\n", "                            }\n", "                        }\n", "                    ]\n", "                }\n", "            ],\n", "            max_tokens=300\n", "        )\n", "        \n", "        # Extract the caption from the response\n", "        caption = response.choices[0].message.content\n", "        return caption\n", "    \n", "    except Exception as e:\n", "        # Return an error message if an exception occurs\n", "        return f\"Error generating caption: {str(e)}\""]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def process_images(image_paths):\n", "    \"\"\"\n", "    Process all images and generate captions.\n", "    \n", "    Args:\n", "        image_paths (List[Dict]): Paths to extracted images\n", "        \n", "    Returns:\n", "        List[Dict]: Image data with captions\n", "    \"\"\"\n", "    image_data = []  # Initialize an empty list to store image data with captions\n", "    \n", "    print(f\"Generating captions for {len(image_paths)} images...\")  # Print the number of images to process\n", "    for i, img_item in enumerate(image_paths):\n", "        print(f\"Processing image {i+1}/{len(image_paths)}...\")  # Print the current image being processed\n", "        img_path = img_item[\"path\"]  # Get the image path\n", "        metadata = img_item[\"metadata\"]  # Get the image metadata\n", "        \n", "        # Generate caption for the image\n", "        caption = generate_image_caption(img_path)\n", "        \n", "        # Add the image data with caption to the list\n", "        image_data.append({\n", "            \"content\": caption,  # The generated caption\n", "            \"metadata\": metadata,  # The image metadata\n", "            \"image_path\": img_path  # The path to the image\n", "        })\n", "    \n", "    return image_data  # Return the list of image data with captions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Simple Vector Store Implementation"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["class MultiModalVectorStore:\n", "    \"\"\"\n", "    A simple vector store implementation for multi-modal content.\n", "    \"\"\"\n", "    def __init__(self):\n", "        # Initialize lists to store vectors, contents, and metadata\n", "        self.vectors = []\n", "        self.contents = []\n", "        self.metadata = []\n", "    \n", "    def add_item(self, content, embedding, metadata=None):\n", "        \"\"\"\n", "        Add an item to the vector store.\n", "        \n", "        Args:\n", "            content (str): The content (text or image caption)\n", "            embedding (List[float]): The embedding vector\n", "            metadata (Dict, optional): Additional metadata\n", "        \"\"\"\n", "        # Append the embedding vector, content, and metadata to their respective lists\n", "        self.vectors.append(np.array(embedding))\n", "        self.contents.append(content)\n", "        self.metadata.append(metadata or {})\n", "    \n", "    def add_items(self, items, embeddings):\n", "        \"\"\"\n", "        Add multiple items to the vector store.\n", "        \n", "        Args:\n", "            items (List[Dict]): List of content items\n", "            embeddings (List[List[float]]): List of embedding vectors\n", "        \"\"\"\n", "        # Loop through items and embeddings and add each to the vector store\n", "        for item, embedding in zip(items, embeddings):\n", "            self.add_item(\n", "                content=item[\"content\"],\n", "                embedding=embedding,\n", "                metadata=item.get(\"metadata\", {})\n", "            )\n", "    \n", "    def similarity_search(self, query_embedding, k=5):\n", "        \"\"\"\n", "        Find the most similar items to a query embedding.\n", "        \n", "        Args:\n", "            query_embedding (List[float]): Query embedding vector\n", "            k (int): Number of results to return\n", "            \n", "        Returns:\n", "            List[Dict]: Top k most similar items\n", "        \"\"\"\n", "        # Return an empty list if there are no vectors in the store\n", "        if not self.vectors:\n", "            return []\n", "        \n", "        # Convert query embedding to numpy array\n", "        query_vector = np.array(query_embedding)\n", "        \n", "        # Calculate similarities using cosine similarity\n", "        similarities = []\n", "        for i, vector in enumerate(self.vectors):\n", "            similarity = np.dot(query_vector, vector) / (np.linalg.norm(query_vector) * np.linalg.norm(vector))\n", "            similarities.append((i, similarity))\n", "        \n", "        # Sort by similarity (descending)\n", "        similarities.sort(key=lambda x: x[1], reverse=True)\n", "        \n", "        # Return top k results\n", "        results = []\n", "        for i in range(min(k, len(similarities))):\n", "            idx, score = similarities[i]\n", "            results.append({\n", "                \"content\": self.contents[idx],\n", "                \"metadata\": self.metadata[idx],\n", "                \"similarity\": float(score)  # Convert to float for JSON serialization\n", "            })\n", "        \n", "        return results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating Embeddings"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def create_embeddings(texts, model=\"BAAI/bge-en-icl\"):\n", "    \"\"\"\n", "    Create embeddings for the given texts.\n", "    \n", "    Args:\n", "        texts (List[str]): Input texts\n", "        model (str): Embedding model name\n", "        \n", "    Returns:\n", "        List[List[float]]: Embedding vectors\n", "    \"\"\"\n", "    # Handle empty input\n", "    if not texts:\n", "        return []\n", "        \n", "    # Process in batches if needed (OpenAI API limits)\n", "    batch_size = 100\n", "    all_embeddings = []\n", "    \n", "    # Iterate over the input texts in batches\n", "    for i in range(0, len(texts), batch_size):\n", "        batch = texts[i:i + batch_size]  # Get the current batch of texts\n", "        \n", "        # Create embeddings for the current batch\n", "        response = client.embeddings.create(\n", "            model=model,\n", "            input=batch\n", "        )\n", "        \n", "        # Extract embeddings from the response\n", "        batch_embeddings = [item.embedding for item in response.data]\n", "        all_embeddings.extend(batch_embeddings)  # Add the batch embeddings to the list\n", "    \n", "    return all_embeddings  # Return all embeddings"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Complete Processing Pipeline"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def process_document(pdf_path, chunk_size=1000, chunk_overlap=200):\n", "    \"\"\"\n", "    Process a document for multi-modal RAG.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to the PDF file\n", "        chunk_size (int): Size of each chunk in characters\n", "        chunk_overlap (int): Overlap between chunks in characters\n", "        \n", "    Returns:\n", "        Tuple[MultiModalVectorStore, Dict]: Vector store and document info\n", "    \"\"\"\n", "    # Create a directory for extracted images\n", "    image_dir = \"extracted_images\"\n", "    os.makedirs(image_dir, exist_ok=True)\n", "    \n", "    # Extract text and images from the PDF\n", "    text_data, image_paths = extract_content_from_pdf(pdf_path, image_dir)\n", "    \n", "    # Chunk the extracted text\n", "    chunked_text = chunk_text(text_data, chunk_size, chunk_overlap)\n", "    \n", "    # Process the extracted images to generate captions\n", "    image_data = process_images(image_paths)\n", "    \n", "    # Combine all content items (text chunks and image captions)\n", "    all_items = chunked_text + image_data\n", "    \n", "    # Extract content for embedding\n", "    contents = [item[\"content\"] for item in all_items]\n", "    \n", "    # Create embeddings for all content\n", "    print(\"Creating embeddings for all content...\")\n", "    embeddings = create_embeddings(contents)\n", "    \n", "    # Build the vector store and add items with their embeddings\n", "    vector_store = MultiModalVectorStore()\n", "    vector_store.add_items(all_items, embeddings)\n", "    \n", "    # Prepare document info with counts of text chunks and image captions\n", "    doc_info = {\n", "        \"text_count\": len(chunked_text),\n", "        \"image_count\": len(image_data),\n", "        \"total_items\": len(all_items),\n", "    }\n", "    \n", "    # Print summary of added items\n", "    print(f\"Added {len(all_items)} items to vector store ({len(chunked_text)} text chunks, {len(image_data)} image captions)\")\n", "    \n", "    # Return the vector store and document info\n", "    return vector_store, doc_info"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Query Processing and Response Generation"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def query_multimodal_rag(query, vector_store, k=5):\n", "    \"\"\"\n", "    Query the multi-modal RAG system.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        vector_store (MultiModalVectorStore): Vector store with document content\n", "        k (int): Number of results to retrieve\n", "        \n", "    Returns:\n", "        Dict: Query results and generated response\n", "    \"\"\"\n", "    print(f\"\\n=== Processing query: {query} ===\\n\")\n", "    \n", "    # Generate embedding for the query\n", "    query_embedding = create_embeddings(query)\n", "    \n", "    # Retrieve relevant content from the vector store\n", "    results = vector_store.similarity_search(query_embedding, k=k)\n", "    \n", "    # Separate text and image results\n", "    text_results = [r for r in results if r[\"metadata\"].get(\"type\") == \"text\"]\n", "    image_results = [r for r in results if r[\"metadata\"].get(\"type\") == \"image\"]\n", "    \n", "    print(f\"Retrieved {len(results)} relevant items ({len(text_results)} text, {len(image_results)} image captions)\")\n", "    \n", "    # Generate a response using the retrieved content\n", "    response = generate_response(query, results)\n", "    \n", "    return {\n", "        \"query\": query,\n", "        \"results\": results,\n", "        \"response\": response,\n", "        \"text_results_count\": len(text_results),\n", "        \"image_results_count\": len(image_results)\n", "    }"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["\n", "def generate_response(query, results):\n", "    \"\"\"\n", "    Generate a response based on the query and retrieved results.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        results (List[Dict]): Retrieved content\n", "        \n", "    Returns:\n", "        str: Generated response\n", "    \"\"\"\n", "    # Format the context from the retrieved results\n", "    context = \"\"\n", "    \n", "    for i, result in enumerate(results):\n", "        # Determine the type of content (text or image caption)\n", "        content_type = \"Text\" if result[\"metadata\"].get(\"type\") == \"text\" else \"Image caption\"\n", "        # Get the page number from the metadata\n", "        page_num = result[\"metadata\"].get(\"page\", \"unknown\")\n", "        \n", "        # Append the content type and page number to the context\n", "        context += f\"[{content_type} from page {page_num}]\\n\"\n", "        # Append the actual content to the context\n", "        context += result[\"content\"]\n", "        context += \"\\n\\n\"\n", "    \n", "    # System message to guide the AI assistant\n", "    system_message = \"\"\"You are an AI assistant specializing in answering questions about documents \n", "    that contain both text and images. You have been given relevant text passages and image captions \n", "    from the document. Use this information to provide a comprehensive, accurate response to the query.\n", "    If information comes from an image or chart, mention this in your answer.\n", "    If the retrieved information doesn't fully answer the query, acknowledge the limitations.\"\"\"\n", "\n", "    # User message containing the query and the formatted context\n", "    user_message = f\"\"\"Query: {query}\n", "\n", "    Retrieved content:\n", "    {context}\n", "\n", "    Please answer the query based on the retrieved content.\n", "    \"\"\"\n", "    \n", "    # Generate the response using the OpenAI API\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.2-3B-Instruct\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_message},\n", "            {\"role\": \"user\", \"content\": user_message}\n", "        ],\n", "        temperature=0.1\n", "    )\n", "    \n", "    # Return the generated response\n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluation Against Text-Only RAG"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def build_text_only_store(pdf_path, chunk_size=1000, chunk_overlap=200):\n", "    \"\"\"\n", "    Build a text-only vector store for comparison.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to the PDF file\n", "        chunk_size (int): Size of each chunk in characters\n", "        chunk_overlap (int): Overlap between chunks in characters\n", "        \n", "    Returns:\n", "        MultiModalVectorStore: Text-only vector store\n", "    \"\"\"\n", "    # Extract text from PDF (reuse function but ignore images)\n", "    text_data, _ = extract_content_from_pdf(pdf_path, None)\n", "    \n", "    # Chunk text\n", "    chunked_text = chunk_text(text_data, chunk_size, chunk_overlap)\n", "    \n", "    # Extract content for embedding\n", "    contents = [item[\"content\"] for item in chunked_text]\n", "    \n", "    # Create embeddings\n", "    print(\"Creating embeddings for text-only content...\")\n", "    embeddings = create_embeddings(contents)\n", "    \n", "    # Build vector store\n", "    vector_store = MultiModalVectorStore()\n", "    vector_store.add_items(chunked_text, embeddings)\n", "    \n", "    print(f\"Added {len(chunked_text)} text items to text-only vector store\")\n", "    return vector_store"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def evaluate_multimodal_vs_textonly(pdf_path, test_queries, reference_answers=None):\n", "    \"\"\"\n", "    Compare multi-modal RAG with text-only RAG.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to the PDF file\n", "        test_queries (List[str]): Test queries\n", "        reference_answers (List[str], optional): Reference answers\n", "        \n", "    Returns:\n", "        Dict: Evaluation results\n", "    \"\"\"\n", "    print(\"=== EVALUATING MULTI-MODAL RAG VS TEXT-ONLY RAG ===\\n\")\n", "    \n", "    # Process document for multi-modal RAG\n", "    print(\"\\nProcessing document for multi-modal RAG...\")\n", "    mm_vector_store, mm_doc_info = process_document(pdf_path)\n", "    \n", "    # Build text-only store\n", "    print(\"\\nProcessing document for text-only RAG...\")\n", "    text_vector_store = build_text_only_store(pdf_path)\n", "    \n", "    # Run evaluation for each query\n", "    results = []\n", "    \n", "    for i, query in enumerate(test_queries):\n", "        print(f\"\\n\\n=== Evaluating Query {i+1}: {query} ===\")\n", "        \n", "        # Get reference answer if available\n", "        reference = None\n", "        if reference_answers and i < len(reference_answers):\n", "            reference = reference_answers[i]\n", "        \n", "        # Run multi-modal RAG\n", "        print(\"\\nRunning multi-modal RAG...\")\n", "        mm_result = query_multimodal_rag(query, mm_vector_store)\n", "        \n", "        # Run text-only RAG\n", "        print(\"\\nRunning text-only RAG...\")\n", "        text_result = query_multimodal_rag(query, text_vector_store)\n", "        \n", "        # Compare responses\n", "        comparison = compare_responses(query, mm_result[\"response\"], text_result[\"response\"], reference)\n", "        \n", "        # Add to results\n", "        results.append({\n", "            \"query\": query,\n", "            \"multimodal_response\": mm_result[\"response\"],\n", "            \"textonly_response\": text_result[\"response\"],\n", "            \"multimodal_results\": {\n", "                \"text_count\": mm_result[\"text_results_count\"],\n", "                \"image_count\": mm_result[\"image_results_count\"]\n", "            },\n", "            \"reference_answer\": reference,\n", "            \"comparison\": comparison\n", "        })\n", "    \n", "    # Generate overall analysis\n", "    overall_analysis = generate_overall_analysis(results)\n", "    \n", "    return {\n", "        \"results\": results,\n", "        \"overall_analysis\": overall_analysis,\n", "        \"multimodal_doc_info\": mm_doc_info\n", "    }"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def compare_responses(query, mm_response, text_response, reference=None):\n", "    \"\"\"\n", "    Compare multi-modal and text-only responses.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        mm_response (str): Multi-modal response\n", "        text_response (str): Text-only response\n", "        reference (str, optional): Reference answer\n", "        \n", "    Returns:\n", "        str: Comparison analysis\n", "    \"\"\"\n", "    # System prompt for the evaluator\n", "    system_prompt = \"\"\"You are an expert evaluator comparing two RAG systems:\n", "    1. Multi-modal RAG: Retrieves from both text and image captions\n", "    2. Text-only RAG: Retrieves only from text\n", "\n", "    Evaluate which response better answers the query based on:\n", "    - Accuracy and correctness\n", "    - Completeness of information\n", "    - Relevance to the query\n", "    - Unique information from visual elements (for multi-modal)\"\"\"\n", "\n", "    # User prompt with query and responses\n", "    user_prompt = f\"\"\"Query: {query}\n", "\n", "    Multi-modal RAG Response:\n", "    {mm_response}\n", "\n", "    Text-only RAG Response:\n", "    {text_response}\n", "    \"\"\"\n", "\n", "    if reference:\n", "        user_prompt += f\"\"\"\n", "    Reference Answer:\n", "    {reference}\n", "    \"\"\"\n", "\n", "        user_prompt += \"\"\"\n", "    Compare these responses and explain which one better answers the query and why.\n", "    Note any specific information that came from images in the multi-modal response.\n", "    \"\"\"\n", "\n", "    # Generate comparison using meta-llama/Llama-3.2-3B-Instruct\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.2-3B-Instruct\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ],\n", "        temperature=0\n", "    )\n", "    \n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def generate_overall_analysis(results):\n", "    \"\"\"\n", "    Generate an overall analysis of multi-modal vs text-only RAG.\n", "    \n", "    Args:\n", "        results (List[Dict]): Evaluation results for each query\n", "        \n", "    Returns:\n", "        str: Overall analysis\n", "    \"\"\"\n", "    # System prompt for the evaluator\n", "    system_prompt = \"\"\"You are an expert evaluator of RAG systems. Provide an overall analysis comparing \n", "    multi-modal RAG (text + images) versus text-only RAG based on multiple test queries.\n", "\n", "    Focus on:\n", "    1. Types of queries where multi-modal RAG outperforms text-only\n", "    2. Specific advantages of incorporating image information\n", "    3. Any disadvantages or limitations of the multi-modal approach\n", "    4. Overall recommendation on when to use each approach\"\"\"\n", "\n", "    # Create summary of evaluations\n", "    evaluations_summary = \"\"\n", "    for i, result in enumerate(results):\n", "        evaluations_summary += f\"Query {i+1}: {result['query']}\\n\"\n", "        evaluations_summary += f\"Multi-modal retrieved {result['multimodal_results']['text_count']} text chunks and {result['multimodal_results']['image_count']} image captions\\n\"\n", "        evaluations_summary += f\"Comparison summary: {result['comparison'][:200]}...\\n\\n\"\n", "\n", "    # User prompt with evaluations summary\n", "    user_prompt = f\"\"\"Based on the following evaluations of multi-modal vs text-only RAG across {len(results)} queries, \n", "    provide an overall analysis comparing these two approaches:\n", "\n", "    {evaluations_summary}\n", "\n", "    Please provide a comprehensive analysis of the relative strengths and weaknesses of multi-modal RAG \n", "    compared to text-only RAG, with specific attention to how image information contributed (or didn't contribute) to response quality.\"\"\"\n", "\n", "    # Generate overall analysis using meta-llama/Llama-3.2-3B-Instruct\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.2-3B-Instruct\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ],\n", "        temperature=0\n", "    )\n", "    \n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluation on Multi-Modal RAG vs Text-Only RAG"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== EVALUATING MULTI-MODAL RAG VS TEXT-ONLY RAG ===\n", "\n", "\n", "Processing document for multi-modal RAG...\n", "Extracting content from data/attention_is_all_you_need.pdf...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Extracted 15 text segments and 3 images\n", "Created 59 text chunks\n", "Generating captions for 3 images...\n", "Processing image 1/3...\n", "Processing image 2/3...\n", "Processing image 3/3...\n", "Creating embeddings for all content...\n", "Added 62 items to vector store (59 text chunks, 3 image captions)\n", "\n", "Processing document for text-only RAG...\n", "Extracting content from data/attention_is_all_you_need.pdf...\n", "Extracted 15 text segments and 3 images\n", "Created 59 text chunks\n", "Creating embeddings for text-only content...\n", "Added 59 text items to text-only vector store\n", "\n", "\n", "=== Evaluating Query 1: What is the BLEU score of the Transformer (base model)? ===\n", "\n", "Running multi-modal RAG...\n", "\n", "=== Processing query: What is the BLEU score of the Transformer (base model)? ===\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_14692\\2117883450.py:75: DeprecationWarning: Conversion of an array with ndim > 0 to a scalar is deprecated, and will error in future. Ensure you extract a single element from your array before performing this operation. (Deprecated NumPy 1.25.)\n", "  \"similarity\": float(score)  # Convert to float for JSON serialization\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Retrieved 5 relevant items (5 text, 0 image captions)\n", "\n", "Running text-only RAG...\n", "\n", "=== Processing query: What is the BLEU score of the Transformer (base model)? ===\n", "\n", "Retrieved 5 relevant items (5 text, 0 image captions)\n", "\n", "=== OVERALL ANALYSIS ===\n", "\n", "**Overall Analysis: Multi-Modal RAG vs Text-Only RAG**\n", "\n", "Our analysis compares the performance of multi-modal RAG (text + images) and text-only RAG across multiple test queries. We evaluate the strengths and weaknesses of each approach, focusing on the types of queries where multi-modal RAG outperforms text-only, the advantages of incorporating image information, and the limitations of the multi-modal approach.\n", "\n", "**Advantages of Multi-Modal RAG**\n", "\n", "1. **Improved Contextual Understanding**: Multi-modal RAG can leverage both text and image information to better understand the context of a query. This can lead to more accurate and informative responses, especially when the query requires a deeper understanding of the topic.\n", "2. **Enhanced Visual Cues**: Images can provide visual cues that can help disambiguate ambiguous queries or provide additional context that is not explicitly stated in the text. For example, in Query 1, the image of the Transformer model could provide visual confirmation of the query, which was not present in the text-only response.\n", "3. **Increased Retrieval Precision**: Multi-modal RAG can retrieve more relevant text chunks and image captions, leading to more accurate and precise responses. In Query 1, the multi-modal RAG retrieved 5 text chunks and 0 image captions, which may not have been sufficient to provide a clear answer.\n", "\n", "**Disadvantages of Multi-Modal RAG**\n", "\n", "1. **Increased Complexity**: Multi-modal RAG requires more complex processing and retrieval mechanisms, which can increase the computational cost and make the system more difficult to train and deploy.\n", "2. **Image Quality and Relevance**: The quality and relevance of the image captions can significantly impact the performance of the multi-modal RAG. Poor-quality or irrelevant images can lead to suboptimal responses.\n", "3. **Overreliance on Image Information**: If the image information is not relevant or accurate, the multi-modal RAG may rely too heavily on it, leading to suboptimal responses.\n", "\n", "**Types of Queries where Multi-Modal RAG Outperforms Text-Only**\n", "\n", "1. **Visual-Spatial Queries**: Multi-modal RAG can perform better on visual-spatial queries that require a deeper understanding of the visual context, such as Query 1 (What is the BLEU score of the Transformer (base model)?).\n", "2. **Ambiguous Queries**: Multi-modal RAG can help disambiguate ambiguous queries by leveraging both text and image information, leading to more accurate and informative responses.\n", "3. **Multi-Modal Queries**: Multi-modal RAG can perform better on multi-modal queries that require the integration of both text and image information, such as Query 1.\n", "\n", "**Specific Advantages of Incorporating Image Information**\n", "\n", "1. **Visual Confirmation**: Images can provide visual confirmation of the query, which can help disambiguate ambiguous queries or provide additional context that is not explicitly stated in the text.\n", "2. **Visual Cues**: Images can provide visual cues that can help the model understand the context of the query, leading to more accurate and informative responses.\n", "3. **Multimodal Fusion**: Images can be fused with text information to provide a more comprehensive understanding of the query, leading to more accurate and informative responses.\n", "\n", "**Overall Recommendation**\n", "\n", "1. **Use Text-Only RAG for Simple Queries**: Text-only RAG is sufficient for simple queries that do not require a deep understanding of the context or visual information.\n", "2. **Use Multi-Modal RAG for Complex Queries**: Multi-modal RAG is recommended for complex queries that require a deeper understanding of the context, visual information, or both.\n", "3. **Use Multi-Modal RAG for Ambiguous Queries**: Multi-modal RAG can help disambiguate ambiguous queries by leveraging both text and image information, leading to more accurate and informative responses.\n", "\n", "In conclusion, multi-modal RAG can outperform text-only RAG in certain types of queries, particularly those that require a deeper understanding of the context, visual information, or both. However, the multi-modal approach also has its limitations, such as increased complexity and the potential for overreliance on image information. The choice of approach depends on the specific use case and the type of queries being addressed.\n"]}], "source": ["# Path to your PDF document\n", "pdf_path = \"data/attention_is_all_you_need.pdf\"\n", "\n", "# Define test queries targeting both text and visual content\n", "test_queries = [\n", "    \"What is the BLEU score of the Transformer (base model)?\",\n", "]\n", "\n", "# Optional reference answers for evaluation\n", "reference_answers = [\n", "    \"The Transformer (base model) achieves a BLEU score of 27.3 on the WMT 2014 English-to-German translation task and 38.1 on the WMT 2014 English-to-French translation task.\",\n", "]\n", "\n", "# Run evaluation\n", "evaluation_results = evaluate_multimodal_vs_textonly(\n", "    pdf_path=pdf_path,\n", "    test_queries=test_queries,\n", "    reference_answers=reference_answers\n", ")\n", "\n", "# Print overall analysis\n", "print(\"\\n=== OVERALL ANALYSIS ===\\n\")\n", "print(evaluation_results[\"overall_analysis\"])"]}], "metadata": {"kernelspec": {"display_name": ".venv-new-specific-rag", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 2}