# Qdrant向量数据库迁移指南

本指南说明如何将项目从原来的ChromaDB VectorDB迁移到新的QdrantVectorDB。

## 🎯 迁移概述

### 主要变化
1. **向量数据库后端**: 从ChromaDB迁移到Qdrant
2. **存储方式**: 从本地文件存储改为服务器模式
3. **配置参数**: 新增Qdrant服务器配置参数
4. **API兼容性**: 保持查询接口的向后兼容

### 优势
- **更好的性能**: Qdrant在大规模向量检索方面性能更优
- **更强的扩展性**: 支持分布式部署和集群模式
- **更丰富的功能**: 支持更多的向量检索算法和过滤选项
- **更好的并发性**: 支持多客户端并发访问

## 🚀 快速开始

### 方式一：内存模式（推荐用于本地开发）

**无需启动外部服务器**，直接使用即可：

```python
# 最简配置，自动使用内存模式
config = {
    "data_name": "example_dataset",
    "template": {...},
    "query_params": {...}
}
```

### 方式二：服务器模式（适合生产环境）

#### 使用Docker启动Qdrant服务器
```bash
# 启动Qdrant服务器
docker run -p 6333:6333 -p 6334:6334 qdrant/qdrant

# 或者使用持久化存储
docker run -p 6333:6333 -p 6334:6334 \
    -v $(pwd)/qdrant_storage:/qdrant/storage \
    qdrant/qdrant
```

#### 使用二进制文件
```bash
# 下载并运行Qdrant
wget https://github.com/qdrant/qdrant/releases/latest/download/qdrant-x86_64-unknown-linux-gnu.tar.gz
tar -xzf qdrant-x86_64-unknown-linux-gnu.tar.gz
./qdrant
```

### 2. 更新配置文件

#### 旧配置（ChromaDB）
```python
config = {
    "data_name": "example_dataset",
    "vectdb_path": "database/example",  # 本地文件路径
    "template": {
        "sys": "prompts/system/example/llm_graph.txt",
        "user": "prompts/user/example/llm_graph.txt"
    },
    "query_params": {
        "l2_top_k": 10,
        "l3_top_k": 40
    }
}
```

#### 新配置（Qdrant内存模式 - 推荐）
```python
config = {
    "data_name": "example_dataset",
    "qdrant_use_memory": True,   # 使用内存模式，无需外部服务器
    "template": {
        "sys": "prompts/system/example/llm_graph.txt",
        "user": "prompts/user/example/llm_graph.txt"
    },
    "query_params": {
        "l2_top_k": 10,
        "l3_top_k": 40
    }
}
```

#### 新配置（Qdrant服务器模式）
```python
config = {
    "data_name": "example_dataset",
    "qdrant_host": "localhost",      # Qdrant服务器地址
    "qdrant_port": 6333,             # Qdrant服务器端口
    "qdrant_use_memory": False,      # 使用服务器模式
    "template": {
        "sys": "prompts/system/example/llm_graph.txt",
        "user": "prompts/user/example/llm_graph.txt"
    },
    "query_params": {
        "l2_top_k": 10,
        "l3_top_k": 40
    }
}
```

### 3. 更新代码导入

#### 旧代码
```python
from src.vector_db import VectorDB
```

#### 新代码
```python
from src.qdrant_vector_db import QdrantVectorDB
```

**注意**: Pipeline类已经自动更新，无需修改Pipeline的使用代码。

## 📋 详细迁移步骤

### 步骤1: 安装依赖
```bash
pip install qdrant-client openai httpx
```

### 步骤2: 选择运行模式
- **内存模式**（推荐）: 无需启动外部服务器，直接使用
- **服务器模式**: 参考上面的"启动Qdrant服务器"部分

### 步骤3: 数据迁移（可选）
如果需要迁移现有的ChromaDB数据到Qdrant：

```python
# 示例迁移脚本
from src.vector_db import VectorDB  # 旧的ChromaDB
from src.qdrant_vector_db import QdrantVectorDB  # 新的Qdrant

# 从ChromaDB读取数据
old_db = VectorDB("database/old_data", "collection_name")
all_data = old_db._collection.get()

# 迁移到Qdrant
new_db = QdrantVectorDB("localhost", 6333, "collection_name")
if all_data["documents"]:
    # 假设title和text存储在metadata中
    titles = [meta.get("title", doc[:50]) for doc, meta in 
              zip(all_data["documents"], all_data["metadatas"])]
    new_db.batch_add(titles, all_data["documents"], all_data["metadatas"])
```

### 步骤4: 更新配置文件
将所有配置文件中的`vectdb_path`替换为`qdrant_host`和`qdrant_port`。

### 步骤5: 测试迁移
运行测试脚本验证迁移是否成功：
```bash
python test_qdrant_pipeline.py
```

## 🔧 配置参数说明

### 必需参数
- `data_name`: 数据集名称，用作集合名称前缀

### 模式选择参数
- `qdrant_use_memory`: 是否使用内存模式（默认: True）
  - `True`: 内存模式，无需外部服务器，适合本地开发
  - `False`: 服务器模式，需要外部Qdrant服务器

### 服务器模式参数（仅当qdrant_use_memory=False时需要）
- `qdrant_host`: Qdrant服务器地址（默认: "localhost"）
- `qdrant_port`: Qdrant服务器端口（默认: 6333）

### 可选参数
- `query_params`: 查询参数配置
  - `l2_top_k`: L2级别查询返回的结果数量
  - `l3_top_k`: L3级别查询返回的结果数量

### 移除的参数
- `vectdb_path`: 不再需要，因为Qdrant使用内存或服务器模式

## 🔍 API兼容性

### 保持不变的方法
- `query_l1()`: 查询一级分类
- `query_l2()`: 查询二级分类  
- `query_l3()`: 查询三级分类
- `batch_add()`: 批量添加文档
- `add_single()`: 添加单个文档

### 新增的方法
- `query_by_title()`: 基于标题向量查询
- `query_by_text()`: 基于文本向量查询
- `get_collection_info()`: 获取集合信息
- `clear_collections()`: 清空集合

## 🚨 注意事项

### 1. 模式选择
- **内存模式**: 数据存储在内存中，程序结束后自动清理，适合开发测试
- **服务器模式**: 数据持久化存储，需要外部服务器，适合生产环境

### 2. 内存模式特点
- 无需启动外部服务器
- 数据仅在程序运行期间存在
- 重启程序后数据会丢失
- 适合临时测试和开发

### 3. 服务器模式要求
- 需要独立的Qdrant服务器进程
- 确保Qdrant服务器在使用前已启动
- 确保服务器端口（默认6333）可访问
- 生产环境建议使用持久化存储

### 4. 数据持久化（仅服务器模式）
- 使用Docker时建议挂载存储卷
- 定期备份Qdrant数据

### 5. 性能优化
- 根据数据量调整Qdrant配置
- 大规模数据建议使用服务器模式
- 考虑使用Qdrant集群提高性能

## 🐛 常见问题

### Q: 我想要本地开发，不想启动外部服务器
**A**: 使用内存模式（默认），配置`qdrant_use_memory: True`或不配置任何qdrant参数。

### Q: 连接Qdrant服务器失败
**A**: 检查Qdrant服务器是否启动，端口是否正确，或改用内存模式。

### Q: 数据查询结果为空
**A**: 确保数据已正确添加到Qdrant，检查元数据过滤条件。

### Q: 内存模式下数据丢失
**A**: 这是正常现象，内存模式数据仅在程序运行期间存在，需要持久化请使用服务器模式。

### Q: 性能比ChromaDB慢
**A**: 调整Qdrant配置参数，考虑使用服务器模式或更强的硬件。

### Q: 向后兼容性问题
**A**: Pipeline类已处理向后兼容，旧配置会自动使用内存模式。

## 📞 获取帮助

如果在迁移过程中遇到问题：
1. 查看Qdrant官方文档: https://qdrant.tech/documentation/
2. 运行测试脚本: `python test_qdrant_pipeline.py`
3. 检查日志输出获取详细错误信息
