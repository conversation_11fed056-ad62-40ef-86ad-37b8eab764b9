import pandas as pd
from pathlib import Path
from tqdm import tqdm
import sys
import json
import time
import itertools

# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

from src.llm import LLM
from src.graph_db import GraphDB
from src.pipeline import Pipeline

def run_parameter_sensitivity_analysis():
    """
    运行参数敏感性分析实验
    """
    
    print("=== NASA Topics参数敏感性分析 ===")
    
    # 定义参数网格
    parameter_grid = {
        "l2_top_k": [5, 8, 12, 15, 20],
        "l3_top_k": [15, 25, 35, 45, 60],
        "temperature": [0.1, 0.3, 0.5, 0.7]
    }
    
    print("参数网格:")
    for param, values in parameter_grid.items():
        print(f"  {param}: {values}")
    
    # 读取数据
    try:
        df = pd.read_csv("dataset/nasa_topics/nasa_topics_val.csv")
        print(f"\n成功读取数据: {len(df)} 条记录")
    except Exception as e:
        print(f"读取数据失败: {e}")
        return
    
    # 数据预处理
    df = df.dropna(subset=['Title', 'Text', 'Cat1', 'Cat2', 'Cat3'])
    df = df[df['Cat1'] != "unknown"]
    df = df[df['Cat2'] != "unknown"]
    df = df[df['Cat3'] != "unknown"]
    
    # 限制实验数据量（参数分析需要多次实验）
    max_samples = 50
    if len(df) > max_samples:
        df = df.sample(n=max_samples, random_state=42)
    
    ds = df.to_dict(orient="records")
    print(f"实验数据量: {len(ds)} 条记录")
    
    # 初始化组件
    try:
        llm = LLM()
        graph_db = GraphDB()
        print("✓ LLM和图数据库初始化成功")
    except Exception as e:
        print(f"✗ 初始化失败: {e}")
        return
    
    # 准备分类选项
    all_l1 = df['Cat1'].unique().tolist()
    
    # 生成参数组合
    param_combinations = []
    for l2_k in parameter_grid["l2_top_k"]:
        for l3_k in parameter_grid["l3_top_k"]:
            for temp in parameter_grid["temperature"]:
                param_combinations.append({
                    "l2_top_k": l2_k,
                    "l3_top_k": l3_k,
                    "temperature": temp
                })
    
    print(f"\n总参数组合数: {len(param_combinations)}")
    
    # 选择部分组合进行实验（避免实验时间过长）
    if len(param_combinations) > 20:
        # 选择代表性组合
        selected_combinations = [
            {"l2_top_k": 5, "l3_top_k": 15, "temperature": 0.3},
            {"l2_top_k": 8, "l3_top_k": 25, "temperature": 0.3},
            {"l2_top_k": 12, "l3_top_k": 35, "temperature": 0.3},  # 默认配置
            {"l2_top_k": 15, "l3_top_k": 45, "temperature": 0.3},
            {"l2_top_k": 20, "l3_top_k": 60, "temperature": 0.3},
            {"l2_top_k": 12, "l3_top_k": 35, "temperature": 0.1},
            {"l2_top_k": 12, "l3_top_k": 35, "temperature": 0.5},
            {"l2_top_k": 12, "l3_top_k": 35, "temperature": 0.7},
        ]
        param_combinations = selected_combinations
        print(f"选择 {len(param_combinations)} 个代表性组合进行实验")
    
    # 开始参数实验
    all_results = {}
    
    for idx, params in enumerate(param_combinations):
        print(f"\n=== 实验 {idx+1}/{len(param_combinations)}: {params} ===")
        
        # 创建配置
        config = {
            "data_name": "nasa_topics_param_study",
            "vectdb_path": "database/nasa_topics",
            "query_params": {
                "l2_top_k": params["l2_top_k"],
                "l3_top_k": params["l3_top_k"]
            },
            "temperature": params["temperature"]
        }
        
        try:
            # 初始化Pipeline
            pipeline = Pipeline(llm, config)
            
            # 运行实验
            results = []
            success_count = 0
            error_count = 0
            total_time = 0
            
            for data_idx in tqdm(range(len(ds)), desc=f"参数组合{idx+1}"):
                data = ds[data_idx].copy()
                
                try:
                    start_time = time.time()
                    
                    # 构建查询文本
                    title = data.get('Title', '').strip()
                    text = data.get('Text', '').strip()
                    query_text = f"Title: {title}\nDescription: {text[:2000]}"
                    
                    # 向量检索
                    retrieved_nodes = pipeline.query_related_nodes(query_text)
                    
                    # 构建子图
                    sub_graph = pipeline.build_linked_labels(
                        retrieved_nodes.get("l3", []), 
                        retrieved_nodes.get("l2", [])
                    )
                    
                    # L1预测
                    pred_l1 = pipeline.predict_level(
                        query_text, all_l1, sub_graph, temperature=params["temperature"]
                    )
                    
                    # L2预测
                    try:
                        child_l1 = graph_db.query_l2_from_l1(pred_l1.lower())
                        potential_l2 = list(set(child_l1 + retrieved_nodes.get("l2", [])))
                    except:
                        potential_l2 = retrieved_nodes.get("l2", [])
                    
                    pred_l2 = pipeline.predict_level(
                        query_text, potential_l2, sub_graph, temperature=params["temperature"]
                    )
                    
                    # L3预测
                    try:
                        child_l2 = graph_db.query_l3_from_l2(pred_l2.lower())
                        potential_l3 = list(set(child_l2 + retrieved_nodes.get("l3", [])))
                    except:
                        potential_l3 = retrieved_nodes.get("l3", [])
                    
                    pred_l3 = pipeline.predict_level(
                        query_text, potential_l3, sub_graph, temperature=params["temperature"]
                    )
                    
                    inference_time = time.time() - start_time
                    total_time += inference_time
                    
                    # 保存结果
                    result = {
                        'Cat1': data['Cat1'],
                        'Cat2': data['Cat2'],
                        'Cat3': data['Cat3'],
                        'pred_l1': pred_l1.lower().strip(),
                        'pred_l2': pred_l2.lower().strip(),
                        'pred_l3': pred_l3.lower().strip(),
                        'inference_time': inference_time,
                        'retrieved_l2_count': len(retrieved_nodes.get("l2", [])),
                        'retrieved_l3_count': len(retrieved_nodes.get("l3", [])),
                        'subgraph_size': len(sub_graph)
                    }
                    
                    results.append(result)
                    success_count += 1
                    
                except Exception as e:
                    error_count += 1
                    if error_count <= 3:  # 只显示前3个错误
                        print(f"处理记录时出错: {e}")
                
                # 限制每个参数组合的样本数
                if data_idx >= 30:  # 每个组合最多30个样本
                    break
            
            # 计算性能指标
            if success_count > 0:
                # 准确率
                correct_l1 = sum(1 for r in results 
                               if r['pred_l1'] == r['Cat1'].lower())
                correct_l2 = sum(1 for r in results 
                               if r['pred_l2'] == r['Cat2'].lower())
                correct_l3 = sum(1 for r in results 
                               if r['pred_l3'] == r['Cat3'].lower())
                
                acc_l1 = correct_l1 / len(results)
                acc_l2 = correct_l2 / len(results)
                acc_l3 = correct_l3 / len(results)
                
                # 层次化准确率
                hierarchical_correct = sum(1 for r in results 
                                         if (r['pred_l1'] == r['Cat1'].lower() and
                                             r['pred_l2'] == r['Cat2'].lower() and
                                             r['pred_l3'] == r['Cat3'].lower()))
                hierarchical_acc = hierarchical_correct / len(results)
                
                avg_time = total_time / len(results)
                avg_retrieval_l2 = sum(r['retrieved_l2_count'] for r in results) / len(results)
                avg_retrieval_l3 = sum(r['retrieved_l3_count'] for r in results) / len(results)
                avg_subgraph_size = sum(r['subgraph_size'] for r in results) / len(results)
                
                # 保存结果
                param_key = f"l2_{params['l2_top_k']}_l3_{params['l3_top_k']}_temp_{params['temperature']}"
                all_results[param_key] = {
                    'parameters': params,
                    'performance': {
                        'accuracy_l1': acc_l1,
                        'accuracy_l2': acc_l2,
                        'accuracy_l3': acc_l3,
                        'accuracy_hierarchical': hierarchical_acc,
                        'average_inference_time': avg_time,
                        'average_retrieval_l2': avg_retrieval_l2,
                        'average_retrieval_l3': avg_retrieval_l3,
                        'average_subgraph_size': avg_subgraph_size
                    },
                    'statistics': {
                        'total_samples': len(results),
                        'success_count': success_count,
                        'error_count': error_count,
                        'success_rate': success_count / (success_count + error_count)
                    }
                }
                
                print(f"✓ 参数组合完成:")
                print(f"  L1准确率: {acc_l1:.4f}")
                print(f"  L2准确率: {acc_l2:.4f}")
                print(f"  L3准确率: {acc_l3:.4f}")
                print(f"  层次化准确率: {hierarchical_acc:.4f}")
                print(f"  平均推理时间: {avg_time:.2f}秒")
                
            else:
                print(f"✗ 参数组合失败: 无有效结果")
                
        except Exception as e:
            print(f"✗ 参数组合失败: {e}")
    
    # 分析结果
    if all_results:
        print(f"\n=== 参数敏感性分析结果 ===")
        
        # 找到最佳参数组合
        best_l1 = max(all_results.items(), key=lambda x: x[1]['performance']['accuracy_l1'])
        best_l2 = max(all_results.items(), key=lambda x: x[1]['performance']['accuracy_l2'])
        best_l3 = max(all_results.items(), key=lambda x: x[1]['performance']['accuracy_l3'])
        best_hierarchical = max(all_results.items(), key=lambda x: x[1]['performance']['accuracy_hierarchical'])
        
        print(f"\n最佳L1准确率: {best_l1[1]['performance']['accuracy_l1']:.4f}")
        print(f"  参数: {best_l1[1]['parameters']}")
        
        print(f"\n最佳L2准确率: {best_l2[1]['performance']['accuracy_l2']:.4f}")
        print(f"  参数: {best_l2[1]['parameters']}")
        
        print(f"\n最佳L3准确率: {best_l3[1]['performance']['accuracy_l3']:.4f}")
        print(f"  参数: {best_l3[1]['parameters']}")
        
        print(f"\n最佳层次化准确率: {best_hierarchical[1]['performance']['accuracy_hierarchical']:.4f}")
        print(f"  参数: {best_hierarchical[1]['parameters']}")
        
        # 保存完整结果
        with open("dataset/nasa_topics/parameter_sensitivity_analysis.json", 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n✓ 参数敏感性分析完成")
        print(f"详细结果已保存: parameter_sensitivity_analysis.json")

if __name__ == "__main__":
    run_parameter_sensitivity_analysis()
