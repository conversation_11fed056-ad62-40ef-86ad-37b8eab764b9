/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { test } from 'node:test';
import { strict as assert } from 'assert';
import { TestRig } from './test-helper.js';

test('should be able to list a directory', async (t) => {
  const rig = new TestRig();
  rig.setup(t.name);
  rig.createFile('file1.txt', 'file 1 content');
  rig.mkdir('subdir');

  const prompt = `Can you list the files in the current directory`;
  const result = await rig.run(prompt);

  assert.ok(result.includes('file1.txt'));
  assert.ok(result.includes('subdir'));
});
