#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向量检索调试函数

专门用于调试向量数据库检索过程中的问题，提供详细的诊断信息
"""

import sys
from pathlib import Path
import json

# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

def debug_vector_retrieval_step_by_step():
    """
    逐步调试向量检索过程
    
    详细分析每个步骤，帮助定位问题所在
    """
    
    print("=== 向量检索逐步调试 ===")
    
    # 步骤1: 检查向量数据库连接
    print("\n📋 步骤1: 检查向量数据库连接")
    try:
        from src.qdrant_vector_db import QdrantVectorDB
        print("✓ 成功导入QdrantVectorDB")
        
        vector_db = QdrantVectorDB(
            collection_name="nasa",
            use_memory=True
        )
        print("✓ 向量数据库实例创建成功")
        
    except Exception as e:
        print(f"✗ 向量数据库连接失败: {e}")
        return False
    
    # 步骤2: 检查集合状态
    print("\n📋 步骤2: 检查集合状态")
    try:
        collection_info = vector_db.get_collection_info()
        print(f"集合信息: {json.dumps(collection_info, indent=2, ensure_ascii=False)}")
        
        title_count = collection_info.get('title_collection', {}).get('count', 0)
        text_count = collection_info.get('text_collection', {}).get('count', 0)
        
        print(f"Title集合文档数: {title_count}")
        print(f"Text集合文档数: {text_count}")
        
        if title_count == 0 and text_count == 0:
            print("⚠ 警告: 向量数据库为空")
            return False
            
    except Exception as e:
        print(f"✗ 获取集合信息失败: {e}")
        return False
    
    # 步骤3: 测试原始查询方法
    print("\n📋 步骤3: 测试原始查询方法")
    test_query = "artificial intelligence space technology"
    
    try:
        print(f"测试查询: '{test_query}'")
        
        # 直接调用query_l2方法
        print("\n🔍 直接调用query_l2:")
        l2_raw_result = vector_db.query_l2(test_query, n_results=5)
        print(f"原始返回类型: {type(l2_raw_result)}")
        print(f"原始返回内容: {l2_raw_result}")
        
        # 检查返回结构
        if isinstance(l2_raw_result, dict):
            print(f"返回字典的键: {list(l2_raw_result.keys())}")
            
            if "documents" in l2_raw_result:
                docs = l2_raw_result["documents"]
                print(f"documents类型: {type(docs)}")
                print(f"documents内容: {docs}")
                
                if isinstance(docs, list) and len(docs) > 0:
                    first_doc_list = docs[0]
                    print(f"第一个文档列表类型: {type(first_doc_list)}")
                    print(f"第一个文档列表长度: {len(first_doc_list) if hasattr(first_doc_list, '__len__') else 'N/A'}")
                    print(f"第一个文档列表内容: {first_doc_list}")
                else:
                    print("⚠ documents为空列表或格式异常")
            else:
                print("⚠ 返回结果中没有documents字段")
        else:
            print(f"⚠ 返回结果不是字典格式")
            
    except Exception as e:
        print(f"✗ 原始查询测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False
    
    # 步骤4: 测试Pipeline的query_related_nodes方法
    print("\n📋 步骤4: 测试Pipeline的query_related_nodes方法")
    try:
        from src.llm import LLM
        from src.pipeline import Pipeline
        
        config = {
            "data_name": "nasa",
            "template": {
                "sys": "prompts/system/nasa/llm_graph.txt",
                "user": "prompts/user/nasa/llm_graph.txt"
            },
            "query_params": {
                "l2_top_k": 5,
                "l3_top_k": 5
            }
        }
        
        llm = LLM()
        pipeline = Pipeline(llm, config)
        print("✓ Pipeline初始化成功")
        
        # 调用query_related_nodes
        print(f"\n🔍 调用query_related_nodes:")
        query_text = f"Title: {test_query}\n"
        print(f"查询文本: '{query_text}'")
        
        retrieved_nodes = pipeline.query_related_nodes(query_text)
        print(f"返回结果类型: {type(retrieved_nodes)}")
        print(f"返回结果: {retrieved_nodes}")
        
        # 详细检查返回结果
        if isinstance(retrieved_nodes, dict):
            l2_nodes = retrieved_nodes.get("l2")
            l3_nodes = retrieved_nodes.get("l3")
            
            print(f"\nL2节点:")
            print(f"  类型: {type(l2_nodes)}")
            print(f"  内容: {l2_nodes}")
            print(f"  长度: {len(l2_nodes) if l2_nodes and hasattr(l2_nodes, '__len__') else 'N/A'}")
            
            print(f"\nL3节点:")
            print(f"  类型: {type(l3_nodes)}")
            print(f"  内容: {l3_nodes}")
            print(f"  长度: {len(l3_nodes) if l3_nodes and hasattr(l3_nodes, '__len__') else 'N/A'}")
            
            # 检查是否会导致list index out of range
            print(f"\n🔍 检查潜在的索引错误:")
            if l2_nodes is None:
                print("⚠ L2节点为None，可能导致错误")
            elif isinstance(l2_nodes, list) and len(l2_nodes) == 0:
                print("⚠ L2节点为空列表，可能导致错误")
            else:
                print("✓ L2节点格式正常")
                
            if l3_nodes is None:
                print("⚠ L3节点为None，可能导致错误")
            elif isinstance(l3_nodes, list) and len(l3_nodes) == 0:
                print("⚠ L3节点为空列表，可能导致错误")
            else:
                print("✓ L3节点格式正常")
        
    except Exception as e:
        print(f"✗ Pipeline测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False
    
    # 步骤5: 测试build_linked_labels方法
    print("\n📋 步骤5: 测试build_linked_labels方法")
    try:
        l3_test_nodes = retrieved_nodes.get("l3", []) if retrieved_nodes else []
        l2_test_nodes = retrieved_nodes.get("l2", []) if retrieved_nodes else []
        
        print(f"准备测试build_linked_labels:")
        print(f"  L3节点: {l3_test_nodes}")
        print(f"  L2节点: {l2_test_nodes}")
        
        sub_graph = pipeline.build_linked_labels(l3_test_nodes, l2_test_nodes)
        print(f"子图构建结果: {sub_graph}")
        print(f"子图大小: {len(sub_graph)}")
        
        if len(sub_graph) == 0:
            print("⚠ 子图为空，这可能是导致后续错误的原因")
        else:
            print("✓ 子图构建成功")
            
    except Exception as e:
        print(f"✗ build_linked_labels测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False
    
    print(f"\n=== 调试总结 ===")
    print("✓ 向量检索逐步调试完成")
    print("如果发现问题，请检查上述步骤中的警告信息")
    
    return True

def test_specific_query_from_nasa_data():
    """
    使用NASA数据集中的真实数据进行测试
    """
    print(f"\n=== 使用真实NASA数据测试 ===")
    
    try:
        import pandas as pd
        
        # 读取NASA验证数据
        data_path = "dataset/nasa/nasa_val.csv"
        df = pd.read_csv(data_path)
        print(f"✓ 成功读取NASA数据: {len(df)} 条记录")
        
        # 取第一条记录进行测试
        first_record = df.iloc[0]
        title = first_record.get('Title', '')
        text = first_record.get('Text', '')
        
        print(f"\n测试记录:")
        print(f"  Title: {title[:100]}...")
        print(f"  Text: {text[:100] if text else 'N/A'}...")
        print(f"  Cat1: {first_record.get('Cat1', 'N/A')}")
        print(f"  Cat2: {first_record.get('Cat2', 'N/A')}")
        print(f"  Cat3: {first_record.get('Cat3', 'N/A')}")
        
        # 构建查询文本（与gpt_nasa.py保持一致）
        query_txt_vecdb = f"Title: {title}\n"
        print(f"\n构建的查询文本: '{query_txt_vecdb.strip()}'")
        
        # 测试向量检索
        from src.qdrant_vector_db import QdrantVectorDB
        vector_db = QdrantVectorDB(collection_name="nasa", use_memory=True)
        
        print(f"\n🔍 使用真实数据进行向量检索:")
        l2_result = vector_db.query_l2(query_txt_vecdb, n_results=5)
        l3_result = vector_db.query_l3(query_txt_vecdb, n_results=5)
        
        print(f"L2检索结果: {l2_result}")
        print(f"L3检索结果: {l3_result}")
        
        # 测试Pipeline集成
        from src.llm import LLM
        from src.pipeline import Pipeline
        
        config = {
            "data_name": "nasa",
            "template": {
                "sys": "prompts/system/nasa/llm_graph.txt",
                "user": "prompts/user/nasa/llm_graph.txt"
            },
            "query_params": {
                "l2_top_k": 5,
                "l3_top_k": 5
            }
        }
        
        llm = LLM()
        pipeline = Pipeline(llm, config)
        
        print(f"\n🔍 使用Pipeline进行检索:")
        retrieved_nodes = pipeline.query_related_nodes(query_txt_vecdb)
        print(f"Pipeline检索结果: {retrieved_nodes}")
        
        return True
        
    except FileNotFoundError:
        print(f"✗ 找不到NASA数据文件，请确保已运行数据预处理")
        return False
    except Exception as e:
        print(f"✗ 真实数据测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🔧 开始向量检索调试...\n")
    
    # 执行逐步调试
    debug_ok = debug_vector_retrieval_step_by_step()
    
    # 使用真实数据测试
    real_data_ok = test_specific_query_from_nasa_data()
    
    print(f"\n=== 调试结果总结 ===")
    print(f"逐步调试: {'✓ 通过' if debug_ok else '✗ 失败'}")
    print(f"真实数据测试: {'✓ 通过' if real_data_ok else '✗ 失败'}")
    
    if debug_ok and real_data_ok:
        print(f"\n🎉 向量检索功能正常！")
    else:
        print(f"\n❌ 发现问题，请查看上述调试信息进行修复")
