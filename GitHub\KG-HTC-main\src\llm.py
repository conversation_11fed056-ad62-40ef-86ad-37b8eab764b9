from openai import OpenAI
from openai.types.chat import ChatCompletion
import os
import dotenv
import httpx
# 1. 配置自定义 httpx Client，禁用 SSL 验证（仅测试用）
custom_httpx_client = httpx.Client(
    verify=False,  # 忽略 SSL 证书验证（测试环境临时使用）
    timeout=httpx.Timeout(10.0)  # 可选：设置超时时间
)



class LLM:
    def __init__(
        self, 
        temperature: float = 0.4,
        max_tokens: int = 1024,
        top_p: float = 0.4,
    ):
        self._client = OpenAI(   
            api_key="sk-azimfqcuqtonsslreokplibyzkrjmeawuiumzrqzzkbdnmoq",
            base_url="https://api.siliconflow.cn/v1/",
            http_client=custom_httpx_client  # 使用自定义的 httpx Client
        )
        self._temperature = temperature
        self._max_tokens = max_tokens
        self._top_p = top_p

    def chat(self, messages: list[dict]) -> ChatCompletion:
        try:
            response = self._client.chat.completions.create(
                model="Qwen/Qwen3-14B",
                messages=messages,
                temperature=self._temperature,
                max_tokens=self._max_tokens,
                top_p=self._top_p,
            )
            
            response = response.choices[0].message.content
            
        except Exception as e:
            response = None

        return response
    
    def construct_messages(self, sys_msg: str, user_msg: str) -> list[dict]:
        messages = [
            {"role": "system", "content": sys_msg},
            {"role": "user", "content": user_msg},
        ]

        return messages
