"""
演示脚本：展示QdrantVectorDB内存模式的使用

这个脚本演示了如何在不启动外部Qdrant服务器的情况下使用QdrantVectorDB。
内存模式特别适合：
1. 本地开发和测试
2. 临时数据处理
3. 不需要数据持久化的场景
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
root_dir = str(Path(__file__).parent)
sys.path.append(root_dir)

try:
    from src.qdrant_vector_db import QdrantVectorDB
    print("✅ 成功导入QdrantVectorDB")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保已安装qdrant-client: pip install qdrant-client")
    sys.exit(1)

def demo_basic_usage():
    """演示基本使用方法"""
    print("\n=== 演示基本使用方法 ===")
    
    # 创建内存模式的向量数据库
    print("正在创建内存模式的向量数据库...")
    vector_db = QdrantVectorDB(
        collection_name="demo_collection",
        use_memory=True  # 使用内存模式
    )
    
    # 准备测试数据
    titles = [
        "机器学习基础",
        "深度学习入门", 
        "自然语言处理",
        "计算机视觉",
        "数据挖掘技术"
    ]
    
    texts = [
        "机器学习是人工智能的一个重要分支，通过算法让计算机从数据中学习",
        "深度学习使用多层神经网络来模拟人脑的学习过程",
        "自然语言处理让计算机能够理解和生成人类语言",
        "计算机视觉使计算机能够识别和理解图像内容",
        "数据挖掘从大量数据中发现有用的模式和知识"
    ]
    
    metadatas = [
        {"level": "Category2", "domain": "AI", "difficulty": "beginner"},
        {"level": "Category2", "domain": "AI", "difficulty": "intermediate"},
        {"level": "Category3", "domain": "AI", "difficulty": "advanced"},
        {"level": "Category3", "domain": "AI", "difficulty": "intermediate"},
        {"level": "Category2", "domain": "Data", "difficulty": "intermediate"}
    ]
    
    # 批量添加数据
    print("正在添加测试数据...")
    vector_db.batch_add(titles, texts, metadatas)
    print(f"✅ 成功添加 {len(titles)} 条数据")
    
    return vector_db

def demo_query_operations(vector_db):
    """演示查询操作"""
    print("\n=== 演示查询操作 ===")
    
    query_text = "人工智能和机器学习"
    
    # 测试不同级别的查询
    print(f"查询文本: '{query_text}'")
    
    # L2级别查询
    print("\n1. L2级别查询:")
    l2_results = vector_db.query_l2(query_text, n_results=3)
    for i, doc in enumerate(l2_results["documents"][0]):
        distance = l2_results["distances"][0][i]
        print(f"   {i+1}. {doc} (距离: {distance:.3f})")
    
    # L3级别查询
    print("\n2. L3级别查询:")
    l3_results = vector_db.query_l3(query_text, n_results=2)
    for i, doc in enumerate(l3_results["documents"][0]):
        distance = l3_results["distances"][0][i]
        print(f"   {i+1}. {doc} (距离: {distance:.3f})")
    
    # 基于标题的查询
    print("\n3. 基于标题的查询:")
    title_results = vector_db.query_by_title(query_text, n_results=3)
    for i, doc in enumerate(title_results["documents"][0]):
        distance = title_results["distances"][0][i]
        metadata = title_results["metadatas"][0][i]
        print(f"   {i+1}. {doc} (距离: {distance:.3f}, 难度: {metadata.get('difficulty', 'N/A')})")
    
    # 所有级别查询
    print("\n4. 所有级别查询:")
    all_results = vector_db.query_all_levels(query_text, n_results=5)
    for i, doc in enumerate(all_results["documents"][0]):
        distance = all_results["distances"][0][i]
        metadata = all_results["metadatas"][0][i]
        level = metadata.get('level', 'N/A')
        domain = metadata.get('domain', 'N/A')
        print(f"   {i+1}. {doc} (距离: {distance:.3f}, 级别: {level}, 领域: {domain})")

def demo_collection_info(vector_db):
    """演示集合信息查询"""
    print("\n=== 演示集合信息查询 ===")
    
    try:
        info = vector_db.get_collection_info()
        print("集合信息:")
        for collection_name, collection_info in info.items():
            print(f"  - {collection_name}:")
            print(f"    文档数量: {collection_info['count']}")
            print(f"    向量维度: {collection_info['vector_size']}")
    except Exception as e:
        print(f"获取集合信息失败: {e}")

def demo_memory_mode_features():
    """演示内存模式的特点"""
    print("\n=== 演示内存模式特点 ===")
    
    print("1. 无需外部服务器")
    print("   ✅ 直接创建QdrantVectorDB实例即可使用")
    
    print("\n2. 数据存储在内存中")
    print("   ✅ 读写速度快，适合临时处理")
    
    print("\n3. 程序结束后数据自动清理")
    print("   ⚠️  数据不会持久化，重启程序后丢失")
    
    print("\n4. 适用场景:")
    print("   - 本地开发和测试")
    print("   - 临时数据分析")
    print("   - 原型验证")
    print("   - 不需要数据持久化的应用")
    
    print("\n5. 不适用场景:")
    print("   - 生产环境（需要数据持久化）")
    print("   - 大规模数据存储")
    print("   - 多进程共享数据")

def main():
    """主演示函数"""
    print("🚀 QdrantVectorDB内存模式演示")
    print("=" * 50)
    
    try:
        # 基本使用演示
        vector_db = demo_basic_usage()
        
        # 查询操作演示
        demo_query_operations(vector_db)
        
        # 集合信息演示
        demo_collection_info(vector_db)
        
        # 内存模式特点演示
        demo_memory_mode_features()
        
        print("\n" + "=" * 50)
        print("🎉 演示完成！")
        print("\n💡 总结:")
        print("- 内存模式无需外部服务器，开箱即用")
        print("- 适合本地开发、测试和临时数据处理")
        print("- 数据存储在内存中，程序结束后自动清理")
        print("- 如需数据持久化，请使用服务器模式")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        print("请检查:")
        print("1. 是否已安装qdrant-client: pip install qdrant-client")
        print("2. 是否已安装openai: pip install openai")
        print("3. 网络连接是否正常（用于调用嵌入API）")

if __name__ == "__main__":
    main()
