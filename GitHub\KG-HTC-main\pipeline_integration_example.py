#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pipeline 集成示例

展示如何将增强版 EnhancedQdrantVectorDB 集成到现有的 Pipeline 类中，
实现无缝替换和功能增强。

作者：AI Assistant
日期：2025-01-28
版本：2.0
"""

import os
import sys
import pandas as pd
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入必要的模块
from src.enhanced_qdrant_vector_db import EnhancedQdrantVectorDB
from src.graph_db import GraphDB

class EnhancedPipeline:
    """
    增强版 Pipeline 类
    
    集成了增强版向量数据库，提供更强大的功能：
    - 支持 OpenAI 标准嵌入模型
    - 增强的查询和过滤功能
    - 完整的数据管理功能
    - 向后兼容现有接口
    """
    
    def __init__(self, config: dict):
        """
        初始化增强版 Pipeline 对象
        
        参数:
            config: 配置字典，包含各种参数设置
        """
        self._config = config
        self._graph_db = GraphDB()  # 初始化图数据库
        
        # 初始化增强版 Qdrant 向量数据库
        embedding_config = config.get("embedding_config", {})
        self._vector_db = EnhancedQdrantVectorDB(
            host=config.get("qdrant_host"),
            port=config.get("qdrant_port", 6333),
            collection_name=config.get("data_name", "default"),
            use_memory=config.get("qdrant_use_memory", True),
            storage_path=config.get("vector_storage_path", "./enhanced_vector_storage"),
            text_field=config.get("text_field", "text"),
            auto_persist=config.get("auto_persist", True),
            embedding_config=embedding_config
        )
        
        print(f"✓ 增强版 Pipeline 初始化完成")
        print(f"  - 集合名称: {config.get('data_name', 'default')}")
        print(f"  - 内存模式: {config.get('qdrant_use_memory', True)}")
        print(f"  - 嵌入模型: {embedding_config.get('model_name', 'text-embedding-ada-002')}")
    
    def initialize_from_csv(self, csv_path: str, hierarchical_columns: Dict[str, str] = None) -> Dict[str, Any]:
        """
        从 CSV 文件初始化向量数据库
        
        参数:
            csv_path: CSV 文件路径
            hierarchical_columns: 层级标签列映射
            
        返回:
            初始化结果统计
        """
        print(f"🔄 从 CSV 文件初始化向量数据库: {csv_path}")
        
        # 默认层级标签映射
        if hierarchical_columns is None:
            hierarchical_columns = {
                "Category1": "Cat1",
                "Category2": "Cat2",
                "Category3": "Cat3"
            }
        
        # 加载 CSV 数据
        result = self._vector_db.load_csv_data(
            csv_path=csv_path,
            text_column=self._config.get("text_column"),  # 可以自动检测
            hierarchical_columns=hierarchical_columns
        )
        
        if result.get("success"):
            print(f"✓ CSV 数据加载成功:")
            print(f"  - 总行数: {result['total_rows']}")
            print(f"  - 处理行数: {result['processed_rows']}")
            print(f"  - 文本列: {result['text_column']}")
            print(f"  - 所有列: {result['columns']}")
        else:
            print(f"✗ CSV 数据加载失败: {result.get('error')}")
        
        return result
    
    def query_related_nodes(self, text: str) -> Dict[str, Any]:
        """
        查询与输入文本相关的节点（增强版）
        
        参数:
            text: 输入文本
            
        返回:
            包含相关L2和L3节点的字典
        """
        # 获取查询参数
        l2_top_k = self._config.get("query_params", {}).get("l2_top_k", 15)
        l3_top_k = self._config.get("query_params", {}).get("l3_top_k", 50)
        score_threshold = self._config.get("query_params", {}).get("score_threshold", 0.3)
        
        # 使用增强版查询接口
        l2_result = self._vector_db.query_hierarchical(
            query_text=text,
            level="Category2",
            n_results=l2_top_k,
            score_threshold=score_threshold
        )
        
        l3_result = None
        if l3_top_k > 0:
            l3_result = self._vector_db.query_hierarchical(
                query_text=text,
                level="Category3",
                n_results=l3_top_k,
                score_threshold=score_threshold
            )
        
        return {
            "l2": l2_result.documents,
            "l3": l3_result.documents if l3_result else None,
            "l2_scores": l2_result.scores,
            "l3_scores": l3_result.scores if l3_result else None
        }
    
    def query_with_filters(self, text: str, filters: Dict[str, Any] = None, n_results: int = 10) -> Dict[str, Any]:
        """
        使用过滤条件查询
        
        参数:
            text: 查询文本
            filters: 过滤条件字典
            n_results: 返回结果数量
            
        返回:
            查询结果
        """
        result = self._vector_db.query(
            query_text=text,
            n_results=n_results,
            where=filters,
            score_threshold=self._config.get("query_params", {}).get("score_threshold", 0.3)
        )
        
        return {
            "documents": result.documents,
            "metadatas": result.metadatas,
            "scores": result.scores,
            "distances": result.distances,
            "ids": result.ids
        }
    
    def add_new_documents(self, texts: List[str], metadatas: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        添加新文档到向量数据库
        
        参数:
            texts: 文本列表
            metadatas: 元数据列表
            
        返回:
            添加结果
        """
        result = self._vector_db.batch_add_texts(texts, metadatas)
        
        if result.get("success"):
            print(f"✓ 成功添加 {result['added_count']} 个文档")
        else:
            print(f"✗ 添加文档失败: {result.get('error')}")
        
        return result
    
    def get_pipeline_statistics(self) -> Dict[str, Any]:
        """
        获取 Pipeline 统计信息
        
        返回:
            统计信息字典
        """
        vector_stats = self._vector_db.get_statistics()
        
        pipeline_stats = {
            "pipeline_config": {
                "data_name": self._config.get("data_name"),
                "use_memory": self._config.get("qdrant_use_memory"),
                "text_field": self._config.get("text_field", "text")
            },
            "vector_database": vector_stats,
            "query_params": self._config.get("query_params", {})
        }
        
        return pipeline_stats
    
    def backup_pipeline_data(self, backup_path: str = None) -> str:
        """
        备份 Pipeline 数据
        
        参数:
            backup_path: 备份路径
            
        返回:
            备份文件路径
        """
        return self._vector_db.backup_data(backup_path)
    
    def restore_pipeline_data(self, backup_path: str) -> bool:
        """
        恢复 Pipeline 数据
        
        参数:
            backup_path: 备份文件路径
            
        返回:
            恢复是否成功
        """
        return self._vector_db.restore_data(backup_path)
    
    # 向后兼容方法
    def build_linked_labels(self, l3_nodes: List[str], related_l2_nodes: List[str]) -> List[str]:
        """
        构建链接标签（向后兼容）
        
        参数:
            l3_nodes: L3节点列表
            related_l2_nodes: 相关的L2节点列表
            
        返回:
            格式化的链接标签列表
        """
        labels = []
        for l3_node in l3_nodes:
            l2_node = self._graph_db.query_l2_from_l3(l3_node)
            l1_node = self._graph_db.query_l1_from_l2(l2_node)
            if l2_node in related_l2_nodes:
                labels.append(f"{l1_node} -> {l2_node} -> {l3_node}")
        return labels

def example_enhanced_pipeline_usage():
    """
    增强版 Pipeline 使用示例
    """
    print("🚀 增强版 Pipeline 使用示例")
    print("=" * 50)
    
    # 配置参数
    config = {
        "data_name": "enhanced_nasa_topics",
        "qdrant_host": None,  # 使用内存模式
        "qdrant_port": 6333,
        "qdrant_use_memory": True,
        "vector_storage_path": "./enhanced_pipeline_storage",
        "text_field": "text",
        "auto_persist": True,
        
        # 嵌入模型配置
        "embedding_config": {
            "model_name": "text-embedding-ada-002",
            "batch_size": 50,
            "max_retries": 3
        },
        
        # 查询参数
        "query_params": {
            "l2_top_k": 15,
            "l3_top_k": 50,
            "score_threshold": 0.3
        }
    }
    
    # 初始化增强版 Pipeline
    pipeline = EnhancedPipeline(config)
    
    # 创建示例数据
    sample_data = {
        'Title': [
            'NASA火星探测任务',
            '深度学习在医疗诊断中的应用',
            '气候变化对生态系统的影响',
            '量子计算的最新进展'
        ],
        'Text': [
            'NASA的火星探测任务旨在寻找火星上生命存在的证据，通过先进的探测器和科学仪器收集数据。',
            '深度学习技术在医疗影像诊断中展现出巨大潜力，能够帮助医生更准确地识别疾病。',
            '全球气候变化正在对各种生态系统产生深远影响，包括物种迁移和栖息地变化。',
            '量子计算技术的发展为解决复杂计算问题提供了新的可能性，在密码学和优化领域有重要应用。'
        ],
        'Cat1': ['earth_science', 'computer_science', 'earth_science', 'computer_science'],
        'Cat2': ['space_science', 'artificial_intelligence', 'climate_science', 'quantum_computing'],
        'Cat3': ['mars_exploration', 'medical_ai', 'ecology', 'quantum_algorithms']
    }
    
    # 保存为CSV文件
    df = pd.DataFrame(sample_data)
    csv_path = "enhanced_pipeline_example.csv"
    df.to_csv(csv_path, index=False, encoding='utf-8')
    
    try:
        # 从CSV初始化
        init_result = pipeline.initialize_from_csv(csv_path)
        
        if init_result.get("success"):
            # 查询相关节点
            print("\n🔍 查询相关节点:")
            related_nodes = pipeline.query_related_nodes("人工智能和机器学习")
            print(f"L2节点: {related_nodes['l2']}")
            print(f"L3节点: {related_nodes['l3']}")
            
            # 使用过滤条件查询
            print("\n🔍 使用过滤条件查询:")
            filtered_result = pipeline.query_with_filters(
                text="科学研究",
                filters={"Cat1": "earth_science"},
                n_results=5
            )
            print(f"过滤查询结果: {len(filtered_result['documents'])} 个")
            
            # 添加新文档
            print("\n📝 添加新文档:")
            new_texts = ["区块链技术在金融领域的应用前景广阔"]
            new_metadatas = [{"Cat1": "computer_science", "Cat2": "blockchain", "Cat3": "fintech"}]
            add_result = pipeline.add_new_documents(new_texts, new_metadatas)
            
            # 获取统计信息
            print("\n📊 Pipeline 统计信息:")
            stats = pipeline.get_pipeline_statistics()
            print(f"总文档数: {stats['vector_database']['basic_info']['total_documents']}")
            print(f"向量维度: {stats['vector_database']['basic_info']['vector_dimension']}")
            
            # 备份数据
            print("\n💾 备份数据:")
            backup_path = pipeline.backup_pipeline_data()
            print(f"备份路径: {backup_path}")
            
        else:
            print(f"✗ 初始化失败: {init_result.get('error')}")
    
    finally:
        # 清理临时文件
        if os.path.exists(csv_path):
            os.remove(csv_path)
    
    print("\n🎉 增强版 Pipeline 示例完成！")

if __name__ == "__main__":
    example_enhanced_pipeline_usage()
