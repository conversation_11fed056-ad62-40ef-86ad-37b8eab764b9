# QdrantVectorDB 重新设计文档

## 概述

本项目重新设计并实现了一个基于 Qdrant 的嵌入模型向量化数据库类，具有以下特性：

- **内存模式运行**：使用 Qdrant 内存模式，无需外部服务器
- **本地持久化存储**：支持数据的本地序列化和恢复
- **动态 Payload 结构**：根据 CSV 文件列名自动构建 payload
- **CSV 数据处理**：专门的 CSV 数据加载和处理功能
- **向后兼容**：保持与现有代码的接口兼容性

## 主要改进

### 1. 架构重新设计

- **单一集合设计**：简化为主集合 + 兼容集合的架构
- **动态字段映射**：根据数据源自动检测和配置字段结构
- **智能文本检测**：自动识别用于向量化的文本字段

### 2. 数据处理增强

- **CSV 自动加载**：支持从 CSV 文件直接加载数据
- **数据清洗**：自动处理缺失值和无效数据
- **增量更新**：支持数据的增量添加和更新

### 3. 持久化功能

- **自动持久化**：数据自动保存到本地文件
- **备份恢复**：支持数据备份和恢复功能
- **缓存管理**：智能的数据缓存和重载机制

## 安装和依赖

```bash
pip install qdrant-client openai pandas pathlib
```

## 快速开始

### 基本使用

```python
from src.qdrant_vector_db import QdrantVectorDB

# 初始化向量数据库
vector_db = QdrantVectorDB(
    collection_name="my_collection",
    use_memory=True,
    auto_persist=True
)

# 添加文本数据
vector_db.add_text(
    text="这是一个测试文本",
    metadata={"category": "test", "level": "Category1"}
)

# 查询相似文本
results = vector_db.query("测试", n_results=5)
print(results)
```

### CSV 数据加载

```python
# 从 CSV 文件加载数据
result = vector_db.load_csv_data(
    csv_path="data.csv",
    text_column="Text",  # 可选，系统会自动检测
    encoding='utf-8'
)

print(f"加载了 {result['processed_rows']} 行数据")
```

### 向后兼容使用

```python
# 使用旧的接口（完全兼容）
vector_db.batch_add(
    titles=["标题1", "标题2"],
    texts=["文本1", "文本2"],
    metadatas=[{"level": "Category1"}, {"level": "Category2"}]
)

# 使用旧的查询接口
l1_results = vector_db.query_l1("查询文本")
l2_results = vector_db.query_l2("查询文本")
l3_results = vector_db.query_l3("查询文本")
```

## API 参考

### 初始化参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `host` | str | None | Qdrant服务器地址 |
| `port` | int | 6333 | Qdrant服务器端口 |
| `collection_name` | str | "default" | 集合名称 |
| `use_memory` | bool | True | 是否使用内存模式 |
| `storage_path` | str | None | 本地存储路径 |
| `text_field` | str | "text" | 文本字段名称 |
| `auto_persist` | bool | True | 是否自动持久化 |

### 主要方法

#### 数据添加

- `add_text(text, metadata)` - 添加单个文本
- `batch_add(titles, texts, metadatas)` - 批量添加（兼容接口）
- `load_csv_data(csv_path, text_column, encoding)` - 加载CSV数据
- `incremental_update(new_data, text_field)` - 增量更新

#### 数据查询

- `query(query_text, n_results, where, collection)` - 通用查询
- `query_by_title(query_text, n_results, where)` - 按标题查询（兼容）
- `query_by_text(query_text, n_results, where)` - 按文本查询（兼容）
- `query_l1/l2/l3(query_text, n_results, search_type)` - 分级查询（兼容）

#### 数据管理

- `delete_by_ids(ids)` - 按ID删除
- `delete_by_metadata(where)` - 按元数据删除
- `clear_collections()` - 清空所有集合

#### 信息获取

- `get_collection_info()` - 获取集合信息
- `get_statistics()` - 获取统计信息
- `export_data(output_path)` - 导出数据

#### 持久化功能

- `backup_data(backup_path)` - 备份数据
- `restore_data(backup_path)` - 恢复数据

## 数据格式

### CSV 文件格式

CSV 文件应包含以下结构：

```csv
Title,Text,Cat1,Cat2,Cat3
"标题1","文本内容1","一级分类","二级分类","三级分类"
"标题2","文本内容2","一级分类","二级分类","三级分类"
```

- `Text` 列用于向量化（可自定义）
- 其他列作为 payload 存储
- 支持任意数量的自定义列

### 查询结果格式

```python
{
    "documents": [["文档1", "文档2", ...]],
    "metadatas": [[{"key": "value"}, ...], ...],
    "distances": [[0.1, 0.2, ...]]
}
```

## 配置说明

### 环境变量

```bash
# OpenAI API 配置（已内置，可自定义）
OPENAI_API_KEY="your-api-key"
OPENAI_BASE_URL="https://api.siliconflow.cn/v1/"
OPENAI_EMBEDDING_MODEL="BAAI/bge-m3"
```

### 存储结构

```
storage_path/
├── metadata_schema.json    # 元数据结构
├── data_cache.pkl         # 数据缓存
└── backup_*.json          # 备份文件
```

## 性能优化

1. **批量操作**：使用 `batch_add` 而不是多次 `add_text`
2. **内存模式**：使用内存模式获得最佳性能
3. **缓存管理**：合理设置 `auto_persist` 参数
4. **查询优化**：使用元数据过滤减少搜索范围

## 错误处理

所有方法都包含完善的错误处理和日志记录：

```python
import logging
logging.basicConfig(level=logging.INFO)

# 查看详细日志
vector_db = QdrantVectorDB(collection_name="test")
```

## 测试

运行测试套件：

```bash
python test_qdrant_vector_db.py
```

运行使用示例：

```bash
python qdrant_vector_db_usage_examples.py
```

## 与现有代码集成

### Pipeline 类集成

```python
from src.pipeline import Pipeline
from src.llm import LLM

config = {
    "data_name": "nasa",
    "qdrant_use_memory": True,
    # ... 其他配置
}

llm = LLM(config)
pipeline = Pipeline(llm, config)  # 自动使用新的 QdrantVectorDB
```

### 现有代码兼容性

新实现完全兼容现有代码，无需修改：

- 所有现有的方法调用都能正常工作
- 返回格式保持一致
- 配置参数向后兼容

## 故障排除

### 常见问题

1. **向量维度不匹配**
   - 确保使用相同的嵌入模型
   - 检查 API 配置是否正确

2. **持久化失败**
   - 检查存储路径权限
   - 确保磁盘空间充足

3. **查询结果为空**
   - 检查数据是否正确加载
   - 验证查询条件和过滤器

### 日志调试

```python
import logging
logging.getLogger('qdrant_vector_db').setLevel(logging.DEBUG)
```

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 许可证

本项目遵循原项目的许可证。
