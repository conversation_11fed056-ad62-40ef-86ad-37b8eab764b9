#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DCL项目快速启动脚本
用于自动化运行DCL项目的三个主要步骤：训练、嵌入提取、检索评估

使用方法:
python quick_start.py --mode all --shot 16 --seed 171
python quick_start.py --mode train --shot 16 --seed 171
python quick_start.py --mode embedding --shot 16 --seed 171
python quick_start.py --mode topk --shot 16 --seed 171
"""

import os
import sys
import argparse
import subprocess
import shutil
from pathlib import Path

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 9):
        print("❌ Python版本需要3.9或更高")
        return False
    
    # 检查必要的包
    required_packages = ['torch', 'transformers', 'openprompt', 'tqdm', 'sklearn']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少必要的包: {', '.join(missing_packages)}")
        print("请运行: pip install torch tqdm openprompt transformers==4.35.2 scikit-learn")
        return False
    
    print("✅ 环境检查通过")
    return True

def check_data_files():
    """检查数据文件是否存在"""
    print("🔍 检查数据文件...")
    
    data_dir = Path("dataset/WebOfScience")
    required_files = [
        "wos_train.json",
        "wos_val.json", 
        "wos_test.json",
        "slot.pt",
        "formatted_data/label0.txt",
        "formatted_data/label1.txt"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not (data_dir / file_path).exists():
            missing_files.append(str(data_dir / file_path))
    
    if missing_files:
        print(f"❌ 缺少数据文件:")
        for file in missing_files:
            print(f"   - {file}")
        
        # 尝试从wos/data目录复制文件
        wos_data_dir = Path("wos/data")
        if wos_data_dir.exists():
            print("🔄 尝试从wos/data目录复制数据文件...")
            try:
                for json_file in ["wos_train.json", "wos_val.json", "wos_test.json"]:
                    src = wos_data_dir / json_file
                    dst = data_dir / json_file
                    if src.exists() and not dst.exists():
                        shutil.copy2(src, dst)
                        print(f"✅ 复制 {json_file}")
                
                # 重新检查
                missing_files = []
                for file_path in required_files:
                    if not (data_dir / file_path).exists():
                        missing_files.append(str(data_dir / file_path))
                
                if not missing_files:
                    print("✅ 数据文件准备完成")
                    return True
            except Exception as e:
                print(f"❌ 复制文件失败: {e}")
        
        return False
    
    print("✅ 数据文件检查通过")
    return True

def run_command(cmd, description):
    """运行命令并显示进度"""
    print(f"🚀 {description}")
    print(f"执行命令: {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, 
                              capture_output=True, text=True)
        print("✅ 执行成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 执行失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def run_training(args):
    """运行训练步骤"""
    cmd = f"""python train.py \
        --dataset {args.dataset} \
        --shot {args.shot} \
        --seed {args.seed} \
        --contrastive_loss {args.contrastive_loss} \
        --contrastive_alpha {args.contrastive_alpha} \
        --lr {args.lr} \
        --lr2 {args.lr2} \
        --batch_size {args.batch_size} \
        --max_epochs {args.max_epochs} \
        --device {args.device}"""
    
    return run_command(cmd, "Step 1: 训练索引器")

def run_embedding(args):
    """运行嵌入提取步骤"""
    cmd = f"""python embedding.py \
        --dataset {args.dataset} \
        --shot {args.shot} \
        --seed {args.seed} \
        --device {args.device}"""
    
    return run_command(cmd, "Step 2: 提取嵌入表示")

def run_topk(args):
    """运行Top-K检索步骤"""
    cmd = f"""python topk.py \
        --dataset {args.dataset} \
        --shot {args.shot} \
        --seed {args.seed} \
        --topk {args.topk} \
        --label_description {args.label_description} \
        --device {args.device}"""
    
    return run_command(cmd, "Step 3: Top-K检索和评估")

def create_directories():
    """创建必要的目录"""
    dirs = ["ckpts", "result", "template", f"{args.dataset}_similar_samples"]
    for dir_name in dirs:
        Path(dir_name).mkdir(exist_ok=True)
    print("✅ 创建必要目录")

def main():
    parser = argparse.ArgumentParser(description="DCL项目快速启动脚本")
    
    # 运行模式
    parser.add_argument("--mode", type=str, default="all", 
                       choices=["all", "train", "embedding", "topk"],
                       help="运行模式: all(全部), train(仅训练), embedding(仅嵌入), topk(仅检索)")
    
    # 数据集参数
    parser.add_argument("--dataset", type=str, default="wos", help="数据集名称")
    parser.add_argument("--shot", type=int, default=16, help="few-shot样本数")
    parser.add_argument("--seed", type=int, default=171, help="随机种子")
    
    # 训练参数
    parser.add_argument("--contrastive_loss", type=int, default=1, help="是否使用对比损失")
    parser.add_argument("--contrastive_alpha", type=float, default=0.99, help="对比损失权重")
    parser.add_argument("--lr", type=float, default=5e-5, help="主模型学习率")
    parser.add_argument("--lr2", type=float, default=1e-4, help="verbalizer学习率")
    parser.add_argument("--batch_size", type=int, default=5, help="批次大小")
    parser.add_argument("--max_epochs", type=int, default=20, help="最大训练轮数")
    
    # 检索参数
    parser.add_argument("--topk", type=int, default=1, help="检索的相似样本数量")
    parser.add_argument("--label_description", type=int, default=1, help="是否使用标签描述")
    
    # 设备参数
    parser.add_argument("--device", type=int, default=0, help="GPU设备号，-1为CPU")
    
    global args
    args = parser.parse_args()
    
    print("🎯 DCL项目快速启动脚本")
    print("=" * 50)
    
    # 环境检查
    if not check_environment():
        sys.exit(1)
    
    if not check_data_files():
        sys.exit(1)
    
    # 创建必要目录
    create_directories()
    
    # 根据模式运行相应步骤
    success = True
    
    if args.mode in ["all", "train"]:
        success &= run_training(args)
    
    if args.mode in ["all", "embedding"] and success:
        success &= run_embedding(args)
    
    if args.mode in ["all", "topk"] and success:
        success &= run_topk(args)
    
    if success:
        print("\n🎉 所有步骤执行完成！")
        print("📊 查看结果文件:")
        print("   - result/few_shot_train.txt")
        print(f"   - {args.dataset}_similar_samples/")
    else:
        print("\n❌ 执行过程中出现错误，请检查日志")
        sys.exit(1)

if __name__ == "__main__":
    main()
