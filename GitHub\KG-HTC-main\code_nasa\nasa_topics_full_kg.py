import pandas as pd
from pathlib import Path
from tqdm import tqdm
import sys
import json
import time
from sklearn.metrics import accuracy_score, f1_score, classification_report

# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

from src.llm import LLM
from src.graph_db import GraphDB

def evaluate_full_kg_results(results):
    """
    评估Full-KG实验结果
    """
    print("\n=== Full-KG性能评估 ===")
    
    # 过滤有效预测结果
    valid_results = [r for r in results if r.get('full_kg_l2') != 'error' and r.get('full_kg_l3') != 'error']
    print(f"有效预测记录数: {len(valid_results)}/{len(results)}")
    
    if len(valid_results) == 0:
        print("没有有效的预测结果")
        return {}
    
    # 准备真实标签和预测标签
    true_l2 = [r['Cat2'].lower() for r in valid_results]
    true_l3 = [r['Cat3'].lower() for r in valid_results]
    pred_l2 = [r['full_kg_l2'] for r in valid_results]
    pred_l3 = [r['full_kg_l3'] for r in valid_results]
    
    # L2级别评估
    acc_l2 = accuracy_score(true_l2, pred_l2)
    f1_l2_macro = f1_score(true_l2, pred_l2, average='macro', zero_division=0)
    f1_l2_micro = f1_score(true_l2, pred_l2, average='micro', zero_division=0)
    
    # L3级别评估
    acc_l3 = accuracy_score(true_l3, pred_l3)
    f1_l3_macro = f1_score(true_l3, pred_l3, average='macro', zero_division=0)
    f1_l3_micro = f1_score(true_l3, pred_l3, average='micro', zero_division=0)
    
    # L2-L3路径评估（层次化准确率）
    true_paths = [f"{l2}->{l3}" for l2, l3 in zip(true_l2, true_l3)]
    pred_paths = [f"{l2}->{l3}" for l2, l3 in zip(pred_l2, pred_l3)]
    
    path_acc = accuracy_score(true_paths, pred_paths)
    path_f1_macro = f1_score(true_paths, pred_paths, average='macro', zero_division=0)
    path_f1_micro = f1_score(true_paths, pred_paths, average='micro', zero_division=0)
    
    # 层次化准确率（L2和L3都正确）
    hierarchical_correct = sum(1 for i in range(len(valid_results)) 
                              if true_l2[i] == pred_l2[i] and true_l3[i] == pred_l3[i])
    hierarchical_acc = hierarchical_correct / len(valid_results)
    
    metrics = {
        'accuracy': {
            'L2': acc_l2,
            'L3': acc_l3,
            'L2_L3_Path': path_acc,
            'Hierarchical': hierarchical_acc
        },
        'f1_macro': {
            'L2': f1_l2_macro,
            'L3': f1_l3_macro,
            'L2_L3_Path': path_f1_macro
        },
        'f1_micro': {
            'L2': f1_l2_micro,
            'L3': f1_l3_micro,
            'L2_L3_Path': path_f1_micro
        },
        'statistics': {
            'total_samples': len(results),
            'valid_samples': len(valid_results),
            'success_rate': len(valid_results) / len(results)
        }
    }
    
    # 打印评估结果
    print("\n1. 准确率 (Accuracy):")
    for level, acc in metrics['accuracy'].items():
        print(f"   {level}: {acc:.4f} ({acc*100:.2f}%)")
    
    print("\n2. F1分数 - Macro平均:")
    for level, f1 in metrics['f1_macro'].items():
        print(f"   {level}: {f1:.4f}")
    
    print("\n3. F1分数 - Micro平均:")
    for level, f1 in metrics['f1_micro'].items():
        print(f"   {level}: {f1:.4f}")
    
    return metrics

def run_full_kg_experiment():
    """
    运行Full-KG实验：使用完整知识图谱，无向量检索筛选
    """
    
    # 配置参数
    config = {
        "data_name": "nasa_topics_full_kg",
        "data_path": "dataset/nasa/nasa_val.csv",
        "output_path": "dataset/nasa/full_kg_results.json",
        "template": {
            "sys": "prompts/system/nasa/llm_graph.txt",
            "user": "prompts/user/nasa/llm_graph.txt"
        },
    }
    
    print("=== NASA Topics Full-KG实验 ===")
    print("实验设计: 使用完整知识图谱，不进行向量检索筛选")
    
    # 读取数据
    try:
        df = pd.read_csv(config["data_path"])
        print(f"成功读取数据: {len(df)} 条记录")
    except Exception as e:
        print(f"读取数据失败: {e}")
        return
    
    # 数据预处理
    df = df.dropna(subset=['Title', 'Text', 'Cat1', 'Cat2', 'Cat3'])
    df = df[df['Cat1'] != "unknown"]
    df = df[df['Cat2'] != "unknown"]
    df = df[df['Cat3'] != "unknown"]
    
    # 限制实验数据量
    max_samples = 500
    if len(df) > max_samples:
        df = df.sample(n=max_samples, random_state=42)
    
    ds = df.to_dict(orient="records")
    print(f"实验数据量: {len(ds)} 条记录")
    
    # 初始化组件
    try:
        llm = LLM()
        graph_db = GraphDB()
        print("✓ LLM和图数据库初始化成功")
    except Exception as e:
        print(f"✗ 初始化失败: {e}")
        return
    
    # 获取完整知识图谱结构
    def get_full_knowledge_graph():
        """获取完整的知识图谱结构"""
        try:
            # 获取所有路径
            query = """
            MATCH (l1:Category1)-[:contains]->(l2:Category2)-[:contains]->(l3:Category3)
            RETURN l1.name as l1, l2.name as l2, l3.name as l3
            """
            result = graph_db._query_database(query)
            
            paths = []
            for record in result.records:
                path = f"{record['l1']} -> {record['l2']} -> {record['l3']}"
                paths.append(path)
            
            return paths
        except Exception as e:
            print(f"获取知识图谱失败: {e}")
            return []
    
    full_kg = get_full_knowledge_graph()
    print(full_kg)
    print(f"完整知识图谱路径数量: {len(full_kg)}")
    
    if len(full_kg) == 0:
        print("错误: 无法获取知识图谱，请先运行初始化脚本")
        return
    
    # 准备所有分类选项
    all_l1 = df['Cat1'].unique().tolist()
    all_l2 = df['Cat2'].unique().tolist()
    all_l3 = df['Cat3'].unique().tolist()
    
    # 开始实验
    print("\n=== 开始Full-KG分类 ===")
    results = []
    success_count = 0
    error_count = 0
    
    for idx in tqdm(range(len(ds)), desc="Full-KG分类"):
        data = ds[idx].copy()
        
        try:
            # 构建查询文本
                # 构建查询文本
            title = data.get('Title', '').strip()
            text = data.get('Text', '').strip()
            #query_text = f"Title: {title}\nDescription: {text[:500]}"  # 限制长度
            query_text = f"Title: {title}\n"  # 限制长度
            
            # 构建包含完整知识图谱的提示
            kg_context = "\n".join(full_kg)  # 限制长度避免超出token限制
            
            # 跳过L1级别预测，直接进行L2级别预测
            l2_prompt = f"""Classify this NASA Earth science dataset into one of these topic categories: {', '.join(all_l2)}.

                Knowledge Graph Context:
                {kg_context}

                Dataset:
                {query_text}

                Topic Category:"""
            
            # 使用LLM的chat方法而不是generate方法
            messages_l2 = [
                {"role": "system", "content": "You are a NASA Earth science dataset classifier. Output only the exact category name.Please provide the final answer directly without showing the reasoning process. "},
                {"role": "user", "content": l2_prompt}
            ]
            pred_l2_raw = llm.chat(messages_l2)
            pred_l2 = pred_l2_raw.strip().lower() if pred_l2_raw else "error"
            
            # L3级别预测
            # 获取L2对应的L3选项
            try:
                l3_options = graph_db.query_l3_from_l2(pred_l2)
                if not l3_options:
                    l3_options = all_l3[:30]  # 限制数量
            except:
                l3_options = all_l3[:30]
            
            l3_prompt = f"""Classify this NASA Earth science dataset into one of these specific terms: {', '.join(l3_options)}.

                    Knowledge Graph Context:
                    {kg_context}

                    Dataset:
                    {query_text}

                    Specific Term:"""
            
            # 使用LLM的chat方法而不是generate方法
            messages_l3 = [
                {"role": "system", "content": "You are a NASA Earth science dataset classifier. Output only the exact term.Please provide the final answer directly without showing the reasoning process. "},
                {"role": "user", "content": l3_prompt}
            ]
            pred_l3_raw = llm.chat(messages_l3)
            pred_l3 = pred_l3_raw.strip().lower() if pred_l3_raw else "error"
            
            # 保存结果（不包含L1预测）
            data["full_kg_l2"] = pred_l2
            data["full_kg_l3"] = pred_l3
            data["method"] = "full_kg"
            data["kg_paths_used"] = len(full_kg)
            
            results.append(data)
            success_count += 1
            
        except Exception as e:
            error_count += 1
            print(f"\n处理第 {idx+1} 条记录时出错: {e}")
            
            data["full_kg_l2"] = "error"
            data["full_kg_l3"] = "error"
            data["error_message"] = str(e)
            data["method"] = "full_kg"
            results.append(data)
        
        # API限制延迟
        
    
    # 保存结果
    try:
        with open(config["output_path"], "w", encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"✓ Full-KG结果已保存: {config['output_path']}")
    except Exception as e:
        print(f"✗ 保存结果失败: {e}")
    
    # 统计信息
    print(f"\n=== Full-KG实验统计 ===")
    print(f"成功: {success_count}, 失败: {error_count}")
    print(f"成功率: {success_count/len(results)*100:.2f}%")
    
    # 评估结果
    metrics = evaluate_full_kg_results(results)
    
    # 保存评估指标
    if metrics:
        metrics_path = config["output_path"].replace('.json', '_metrics.json')
        try:
            with open(metrics_path, "w", encoding='utf-8') as f:
                json.dump(metrics, f, indent=2, ensure_ascii=False)
            print(f"✓ 评估指标已保存: {metrics_path}")
        except Exception as e:
            print(f"✗ 保存评估指标失败: {e}")

if __name__ == "__main__":
    run_full_kg_experiment()



