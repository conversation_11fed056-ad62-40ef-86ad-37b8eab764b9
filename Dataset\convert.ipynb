{"cells": [{"cell_type": "code", "execution_count": 5, "id": "e2f62540", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["文件已成功保存至：DBpedia/test-00000-of-00001.csv\n"]}], "source": ["import pandas as pd\n", "\n", "def parquet_to_csv(parquet_path: str, csv_path: str):\n", "    \"\"\"\n", "    将Parquet文件转换为CSV文件\n", "    \n", "    Args:\n", "        parquet_path (str): 输入的Parquet文件路径\n", "        csv_path (str): 输出的CSV文件路径\n", "    \"\"\"\n", "    # 读取Parquet文件\n", "    df = pd.read_parquet(parquet_path)\n", "    \n", "    # 将数据保存为CSV（不保存索引）\n", "    df.to_csv(csv_path, index=False)\n", "    print(f\"文件已成功保存至：{csv_path}\")\n", "\n", "# 使用示例\n", "parquet_file = \"DBpedia/test-00000-of-00001.parquet\"\n", "csv_file = \"DBpedia/test-00000-of-00001.csv\"\n", "parquet_to_csv(parquet_path=parquet_file, csv_path=csv_file)\n"]}, {"cell_type": "code", "execution_count": null, "id": "1dc20afc", "metadata": {}, "outputs": [], "source": ["def parquet_to_csv_large_file(parquet_path: str, csv_path: str, chunk_size: int = 10000):\n", "    \"\"\"\n", "    分块读取并转换大文件（减少内存占用）\n", "    \"\"\"\n", "    # 初始化写入器\n", "    with pd.DataFrame().to_csv(csv_path, mode='w', index=False, header=True) as writer:\n", "        # 分块读取Parquet\n", "        for chunk in pd.read_parquet(parquet_path, engine='pyarrow', chunksize=chunk_size):\n", "            chunk.to_csv(csv_path, mode='a', index=False, header=False)\n", "    print(f\"大文件已分块保存至：{csv_path}\")\n", "\n", "# 使用示例\n", "parquet_to_csv_large_file(\"large_data.parquet\", \"large_output.csv\", chunk_size=50000)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "9769fcf2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["文件已保存至：SST5/test.csv\n"]}], "source": ["import json\n", "import pandas as pd\n", "\n", "def jsonl_to_csv(jsonl_file_path: str, csv_file_path: str):\n", "    \"\"\"\n", "    将JSONL文件转换为CSV文件\n", "    \n", "    Args:\n", "        jsonl_file_path (str): 输入的JSONL文件路径\n", "        csv_file_path (str): 输出的CSV文件路径\n", "    \"\"\"\n", "    # 读取JSONL文件并解析为字典列表\n", "    data = []\n", "    with open(jsonl_file_path, 'r', encoding='utf-8') as f:\n", "        for line in f:\n", "            line = line.strip()\n", "            if line:\n", "                json_obj = json.loads(line)\n", "                data.append(json_obj)\n", "    \n", "    # 转换为DataFrame\n", "    df = pd.DataFrame(data)\n", "    \n", "    # 保存为CSV（不保存索引）\n", "    df.to_csv(csv_file_path, index=False, encoding='utf-8')\n", "    print(f\"文件已保存至：{csv_file_path}\")\n", "\n", "# 使用示例\n", "jsonl_file = \"SST5/test.jsonl\"  # 替换为实际路径\n", "csv_file = \"SST5/test.csv\"     # 输出路径\n", "jsonl_to_csv(jsonl_file, csv_file)\n"]}], "metadata": {"kernelspec": {"display_name": "py310", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}