#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DCL项目数据准备脚本
用于准备和验证WOS数据集

使用方法:
python prepare_data.py --check_only  # 仅检查数据
python prepare_data.py --copy_data   # 复制并检查数据
python prepare_data.py --validate    # 验证数据格式
"""

import os
import json
import shutil
import argparse
from pathlib import Path
from collections import Counter

def copy_data_files():
    """从wos/data目录复制数据文件到正确位置"""
    print("🔄 复制数据文件...")
    
    source_dir = Path("wos/data")
    target_dir = Path("dataset/WebOfScience")
    
    # 确保目标目录存在
    target_dir.mkdir(parents=True, exist_ok=True)
    
    # 复制JSON文件
    json_files = ["wos_train.json", "wos_val.json", "wos_test.json"]
    
    for json_file in json_files:
        source_file = source_dir / json_file
        target_file = target_dir / json_file
        
        if source_file.exists():
            if not target_file.exists():
                shutil.copy2(source_file, target_file)
                print(f"✅ 复制 {json_file}")
            else:
                print(f"⚠️  {json_file} 已存在，跳过")
        else:
            print(f"❌ 源文件不存在: {source_file}")
    
    print("✅ 数据文件复制完成")

def check_data_files():
    """检查数据文件是否存在"""
    print("🔍 检查数据文件...")
    
    data_dir = Path("dataset/WebOfScience")
    required_files = {
        "wos_train.json": "训练数据",
        "wos_val.json": "验证数据", 
        "wos_test.json": "测试数据",
        "slot.pt": "层次结构映射文件",
        "formatted_data/label0.txt": "Level 0标签列表",
        "formatted_data/label1.txt": "Level 1标签列表"
    }
    
    missing_files = []
    existing_files = []
    
    for file_path, description in required_files.items():
        full_path = data_dir / file_path
        if full_path.exists():
            existing_files.append((file_path, description, full_path.stat().st_size))
        else:
            missing_files.append((file_path, description))
    
    print("\n📁 现有文件:")
    for file_path, description, size in existing_files:
        size_mb = size / (1024 * 1024)
        print(f"   ✅ {file_path} ({description}) - {size_mb:.2f} MB")
    
    if missing_files:
        print("\n❌ 缺少文件:")
        for file_path, description in missing_files:
            print(f"   - {file_path} ({description})")
        return False
    
    print("\n✅ 所有必要文件都存在")
    return True

def validate_json_data():
    """验证JSON数据格式和内容"""
    print("🔍 验证数据格式...")
    
    data_dir = Path("dataset/WebOfScience")
    json_files = ["wos_train.json", "wos_val.json", "wos_test.json"]
    
    total_stats = {
        "total_samples": 0,
        "label0_counter": Counter(),
        "label1_counter": Counter(),
        "text_lengths": []
    }
    
    for json_file in json_files:
        file_path = data_dir / json_file
        if not file_path.exists():
            print(f"❌ 文件不存在: {json_file}")
            continue
        
        print(f"\n📊 分析 {json_file}:")
        
        try:
            samples = []
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        data = json.loads(line.strip())
                        samples.append(data)
                    except json.JSONDecodeError as e:
                        print(f"❌ 第{line_num}行JSON格式错误: {e}")
                        continue
            
            print(f"   📈 样本数量: {len(samples)}")
            total_stats["total_samples"] += len(samples)
            
            # 验证数据格式
            valid_samples = 0
            for i, sample in enumerate(samples[:5]):  # 检查前5个样本
                if validate_sample_format(sample, i+1):
                    valid_samples += 1
            
            if valid_samples == min(5, len(samples)):
                print("   ✅ 数据格式正确")
            
            # 统计标签分布
            label0_dist = Counter()
            label1_dist = Counter()
            text_lengths = []
            
            for sample in samples:
                if 'doc_label' in sample and len(sample['doc_label']) >= 2:
                    label0_dist[sample['doc_label'][0]] += 1
                    label1_dist[sample['doc_label'][1]] += 1
                    total_stats["label0_counter"][sample['doc_label'][0]] += 1
                    total_stats["label1_counter"][sample['doc_label'][1]] += 1
                
                if 'doc_token' in sample:
                    text_len = len(sample['doc_token'].split())
                    text_lengths.append(text_len)
                    total_stats["text_lengths"].append(text_len)
            
            print(f"   📊 Level 0标签数量: {len(label0_dist)}")
            print(f"   📊 Level 1标签数量: {len(label1_dist)}")
            
            if text_lengths:
                avg_len = sum(text_lengths) / len(text_lengths)
                print(f"   📏 平均文本长度: {avg_len:.1f} 词")
                print(f"   📏 文本长度范围: {min(text_lengths)} - {max(text_lengths)} 词")
        
        except Exception as e:
            print(f"❌ 处理文件时出错: {e}")
    
    # 打印总体统计
    print(f"\n📈 总体统计:")
    print(f"   总样本数: {total_stats['total_samples']}")
    print(f"   Level 0标签数: {len(total_stats['label0_counter'])}")
    print(f"   Level 1标签数: {len(total_stats['label1_counter'])}")
    
    if total_stats['text_lengths']:
        avg_len = sum(total_stats['text_lengths']) / len(total_stats['text_lengths'])
        print(f"   平均文本长度: {avg_len:.1f} 词")
    
    # 显示标签分布
    print(f"\n🏷️  Level 0标签分布:")
    for label, count in total_stats['label0_counter'].most_common():
        percentage = count / total_stats['total_samples'] * 100
        print(f"   {label}: {count} ({percentage:.1f}%)")
    
    return True

def validate_sample_format(sample, sample_num):
    """验证单个样本的格式"""
    required_fields = ['doc_token', 'doc_label', 'doc_topic', 'doc_keyword']
    
    for field in required_fields:
        if field not in sample:
            print(f"❌ 样本{sample_num}缺少字段: {field}")
            return False
    
    # 检查doc_label格式
    if not isinstance(sample['doc_label'], list) or len(sample['doc_label']) != 2:
        print(f"❌ 样本{sample_num}的doc_label格式错误，应为包含2个元素的列表")
        return False
    
    # 检查doc_token是否为字符串
    if not isinstance(sample['doc_token'], str):
        print(f"❌ 样本{sample_num}的doc_token应为字符串")
        return False
    
    return True

def validate_label_files():
    """验证标签文件"""
    print("🔍 验证标签文件...")
    
    data_dir = Path("dataset/WebOfScience")
    
    # 检查label0.txt
    label0_file = data_dir / "formatted_data/label0.txt"
    if label0_file.exists():
        with open(label0_file, 'r', encoding='utf-8') as f:
            label0_list = [line.strip() for line in f if line.strip()]
        print(f"   ✅ Level 0标签: {len(label0_list)}个")
        print(f"      {', '.join(label0_list)}")
    else:
        print("   ❌ label0.txt 不存在")
    
    # 检查label1.txt
    label1_file = data_dir / "formatted_data/label1.txt"
    if label1_file.exists():
        with open(label1_file, 'r', encoding='utf-8') as f:
            label1_list = [line.strip() for line in f if line.strip()]
        print(f"   ✅ Level 1标签: {len(label1_list)}个")
    else:
        print("   ❌ label1.txt 不存在")

def main():
    parser = argparse.ArgumentParser(description="DCL项目数据准备脚本")
    parser.add_argument("--check_only", action="store_true", help="仅检查数据文件")
    parser.add_argument("--copy_data", action="store_true", help="复制数据文件")
    parser.add_argument("--validate", action="store_true", help="验证数据格式")
    
    args = parser.parse_args()
    
    print("📋 DCL项目数据准备脚本")
    print("=" * 50)
    
    if args.copy_data:
        copy_data_files()
    
    if args.check_only or args.copy_data or not any([args.check_only, args.copy_data, args.validate]):
        if not check_data_files():
            print("\n💡 提示: 如果数据文件在wos/data目录下，请运行:")
            print("   python prepare_data.py --copy_data")
            return
    
    if args.validate or not any([args.check_only, args.copy_data, args.validate]):
        validate_label_files()
        validate_json_data()
    
    print("\n✅ 数据准备完成！")
    print("🚀 现在可以运行训练脚本:")
    print("   python quick_start.py --mode all --shot 16 --seed 171")

if __name__ == "__main__":
    main()
