"""
示例配置文件：展示如何使用新的QdrantVectorDB

这个文件展示了如何配置Pipeline以使用QdrantVectorDB而不是原来的ChromaDB VectorDB。
主要变化：
1. 移除了vectdb_path配置项（Qdrant使用服务器而不是本地文件）
2. 添加了qdrant_host和qdrant_port配置项
3. 保持其他配置项不变，确保向后兼容
"""

# 使用Qdrant内存模式的配置示例（推荐用于本地开发）
memory_config = {
    "data_name": "example_dataset",  # 数据集名称，用作集合名称前缀
    "qdrant_use_memory": True,       # 使用内存模式，无需外部服务器（默认True）
    "template": {
        "sys": "prompts/system/example/llm_graph.txt",
        "user": "prompts/user/example/llm_graph.txt"
    },
    "query_params": {
        "l2_top_k": 10,              # L2级别查询返回的top-k结果数量
        "l3_top_k": 40               # L3级别查询返回的top-k结果数量
    }
}

# 使用Qdrant服务器模式的配置示例
server_config = {
    "data_name": "example_dataset",  # 数据集名称，用作集合名称前缀
    "qdrant_host": "localhost",      # Qdrant服务器地址
    "qdrant_port": 6333,             # Qdrant服务器端口，默认6333
    "qdrant_use_memory": False,      # 不使用内存模式，连接外部服务器
    "template": {
        "sys": "prompts/system/example/llm_graph.txt",
        "user": "prompts/user/example/llm_graph.txt"
    },
    "query_params": {
        "l2_top_k": 10,              # L2级别查询返回的top-k结果数量
        "l3_top_k": 40               # L3级别查询返回的top-k结果数量
    }
}

# 兼容性配置：最简配置，自动使用内存模式
compatible_config = {
    "data_name": "example_dataset",
    # 注意：不再需要vectdb_path，因为Qdrant使用内存或服务器模式
    # "vectdb_path": "database/example",  # 这个配置项不再使用
    "template": {
        "sys": "prompts/system/example/llm_graph.txt",
        "user": "prompts/user/example/llm_graph.txt"
    },
    "query_params": {
        "l2_top_k": 10,
        "l3_top_k": 40
    }
    # 如果未指定qdrant相关配置，将自动使用内存模式（qdrant_use_memory=True）
}

# 远程Qdrant服务器配置示例
remote_qdrant_config = {
    "data_name": "remote_dataset",
    "qdrant_host": "*************",  # 远程Qdrant服务器IP
    "qdrant_port": 6333,             # 远程Qdrant服务器端口
    "template": {
        "sys": "prompts/system/example/llm_graph.txt",
        "user": "prompts/user/example/llm_graph.txt"
    },
    "query_params": {
        "l2_top_k": 15,
        "l3_top_k": 50
    }
}

# 使用示例
if __name__ == "__main__":
    print("=== Qdrant配置示例 ===")
    print("\n1. 内存模式配置（推荐用于本地开发）:")
    for key, value in memory_config.items():
        print(f"  {key}: {value}")

    print("\n2. 服务器模式配置:")
    for key, value in server_config.items():
        print(f"  {key}: {value}")

    print("\n3. 兼容性配置（自动使用内存模式）:")
    for key, value in compatible_config.items():
        print(f"  {key}: {value}")

    print("\n4. 远程Qdrant配置:")
    for key, value in remote_qdrant_config.items():
        print(f"  {key}: {value}")

    print("\n=== 配置说明 ===")
    print("- data_name: 数据集名称，将作为Qdrant集合名称的前缀")
    print("- qdrant_use_memory: 是否使用内存模式（默认True，无需外部服务器）")
    print("- qdrant_host: Qdrant服务器地址（仅服务器模式需要）")
    print("- qdrant_port: Qdrant服务器端口（仅服务器模式需要）")
    print("- 不再需要vectdb_path配置项")
    print("- 内存模式适合本地开发和测试，数据存储在内存中")
    print("- 服务器模式适合生产环境，需要启动外部Qdrant服务器")
