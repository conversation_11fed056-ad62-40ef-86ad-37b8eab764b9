import pandas as pd
import numpy as np
from sklearn.metrics import f1_score, accuracy_score, classification_report
from sklearn.metrics import confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import json
from pathlib import Path
from collections import defaultdict

def load_results(result_path: str):
    """加载实验结果"""
    try:
        with open(result_path, 'r', encoding='utf-8') as f:
            results = json.load(f)
        return pd.DataFrame(results)
    except Exception as e:
        print(f"加载结果文件失败: {e}")
        return None

def standardize_labels(df: pd.DataFrame):
    """标准化标签格式"""
    def standardize_label(label):
        if pd.isna(label):
            return "unknown"
        return str(label).lower().strip()
    
    # 标准化真实标签
    df['Cat1_std'] = df['Cat1'].apply(standardize_label)
    df['Cat2_std'] = df['Cat2'].apply(standardize_label)
    df['Cat3_std'] = df['Cat3'].apply(standardize_label)
    
    # 标准化预测标签
    df['pred_l1_std'] = df['gpt3_graph_l1'].apply(standardize_label)
    df['pred_l2_std'] = df['gpt3_graph_l2'].apply(standardize_label)
    df['pred_l3_std'] = df['gpt3_graph_l3'].apply(standardize_label)
    
    return df

def calculate_metrics(df: pd.DataFrame):
    """计算各种评估指标"""
    # 过滤掉错误记录
    df_valid = df[df['gpt3_graph_l1'] != 'error'].copy()
    
    if len(df_valid) == 0:
        print("没有有效的预测结果")
        return None
    
    print(f"有效预测记录数: {len(df_valid)}/{len(df)}")
    
    # 标准化标签
    df_valid = standardize_labels(df_valid)
    
    # 计算准确率
    acc_l1 = accuracy_score(df_valid['Cat1_std'], df_valid['pred_l1_std'])
    acc_l2 = accuracy_score(df_valid['Cat2_std'], df_valid['pred_l2_std'])
    acc_l3 = accuracy_score(df_valid['Cat3_std'], df_valid['pred_l3_std'])
    
    # 计算F1分数
    f1_l1_macro = f1_score(df_valid['Cat1_std'], df_valid['pred_l1_std'], average='macro', zero_division=0)
    f1_l2_macro = f1_score(df_valid['Cat2_std'], df_valid['pred_l2_std'], average='macro', zero_division=0)
    f1_l3_macro = f1_score(df_valid['Cat3_std'], df_valid['pred_l3_std'], average='macro', zero_division=0)
    
    f1_l1_micro = f1_score(df_valid['Cat1_std'], df_valid['pred_l1_std'], average='micro', zero_division=0)
    f1_l2_micro = f1_score(df_valid['Cat2_std'], df_valid['pred_l2_std'], average='micro', zero_division=0)
    f1_l3_micro = f1_score(df_valid['Cat3_std'], df_valid['pred_l3_std'], average='micro', zero_division=0)
    
    # 层次化准确率（所有层级都正确）
    hierarchical_acc = ((df_valid['Cat1_std'] == df_valid['pred_l1_std']) & 
                       (df_valid['Cat2_std'] == df_valid['pred_l2_std']) & 
                       (df_valid['Cat3_std'] == df_valid['pred_l3_std'])).mean()
    
    # 部分层次化准确率
    l1_l2_acc = ((df_valid['Cat1_std'] == df_valid['pred_l1_std']) & 
                 (df_valid['Cat2_std'] == df_valid['pred_l2_std'])).mean()
    
    metrics = {
        'accuracy': {
            'L1': acc_l1,
            'L2': acc_l2,
            'L3': acc_l3,
            'L1_L2': l1_l2_acc,
            'Hierarchical': hierarchical_acc
        },
        'f1_macro': {
            'L1': f1_l1_macro,
            'L2': f1_l2_macro,
            'L3': f1_l3_macro
        },
        'f1_micro': {
            'L1': f1_l1_micro,
            'L2': f1_l2_micro,
            'L3': f1_l3_micro
        }
    }
    
    return metrics, df_valid

def print_metrics(metrics):
    """打印评估指标"""
    print("\n=== NASA Topics数据集KG-HTC性能评估 ===")
    print("\n1. 准确率 (Accuracy):")
    for level, acc in metrics['accuracy'].items():
        print(f"   {level}: {acc:.4f} ({acc*100:.2f}%)")
    
    print("\n2. F1分数 - Macro平均:")
    for level, f1 in metrics['f1_macro'].items():
        print(f"   {level}: {f1:.4f}")
    
    print("\n3. F1分数 - Micro平均:")
    for level, f1 in metrics['f1_micro'].items():
        print(f"   {level}: {f1:.4f}")

def analyze_errors(df_valid: pd.DataFrame):
    """错误分析"""
    print("\n=== 错误分析 ===")
    
    # L1级别错误分析
    l1_errors = df_valid[df_valid['Cat1_std'] != df_valid['pred_l1_std']]
    print(f"\nL1级别错误数量: {len(l1_errors)}/{len(df_valid)} ({len(l1_errors)/len(df_valid)*100:.2f}%)")
    
    if len(l1_errors) > 0:
        print("L1级别最常见的错误:")
        error_pairs = l1_errors.groupby(['Cat1_std', 'pred_l1_std']).size().sort_values(ascending=False)
        for (true_label, pred_label), count in error_pairs.head(5).items():
            print(f"   {true_label} -> {pred_label}: {count} 次")
    
    # L2级别错误分析
    l2_errors = df_valid[df_valid['Cat2_std'] != df_valid['pred_l2_std']]
    print(f"\nL2级别错误数量: {len(l2_errors)}/{len(df_valid)} ({len(l2_errors)/len(df_valid)*100:.2f}%)")
    
    if len(l2_errors) > 0:
        print("L2级别最常见的错误:")
        error_pairs = l2_errors.groupby(['Cat2_std', 'pred_l2_std']).size().sort_values(ascending=False)
        for (true_label, pred_label), count in error_pairs.head(5).items():
            print(f"   {true_label} -> {pred_label}: {count} 次")
    
    # L3级别错误分析
    l3_errors = df_valid[df_valid['Cat3_std'] != df_valid['pred_l3_std']]
    print(f"\nL3级别错误数量: {len(l3_errors)}/{len(df_valid)} ({len(l3_errors)/len(df_valid)*100:.2f}%)")

def analyze_retrieval_performance(df_valid: pd.DataFrame):
    """分析检索性能"""
    print("\n=== 检索性能分析 ===")
    
    # 检查检索到的标签是否包含真实标签
    l2_retrieval_hits = 0
    l3_retrieval_hits = 0
    
    for _, row in df_valid.iterrows():
        if 'retrieved_l2' in row and isinstance(row['retrieved_l2'], list):
            retrieved_l2_std = [label.lower().strip() for label in row['retrieved_l2']]
            if row['Cat2_std'] in retrieved_l2_std:
                l2_retrieval_hits += 1
        
        if 'retrieved_l3' in row and isinstance(row['retrieved_l3'], list):
            retrieved_l3_std = [label.lower().strip() for label in row['retrieved_l3']]
            if row['Cat3_std'] in retrieved_l3_std:
                l3_retrieval_hits += 1
    
    print(f"L2检索命中率: {l2_retrieval_hits}/{len(df_valid)} ({l2_retrieval_hits/len(df_valid)*100:.2f}%)")
    print(f"L3检索命中率: {l3_retrieval_hits}/{len(df_valid)} ({l3_retrieval_hits/len(df_valid)*100:.2f}%)")
    
    # 分析子图大小
    if 'subgraph_size' in df_valid.columns:
        avg_subgraph_size = df_valid['subgraph_size'].mean()
        max_subgraph_size = df_valid['subgraph_size'].max()
        min_subgraph_size = df_valid['subgraph_size'].min()
        print(f"子图大小统计: 平均={avg_subgraph_size:.2f}, 最大={max_subgraph_size}, 最小={min_subgraph_size}")
    
    # 分析API调用时间
    if 'api_call_time' in df_valid.columns:
        avg_api_time = df_valid['api_call_time'].mean()
        max_api_time = df_valid['api_call_time'].max()
        min_api_time = df_valid['api_call_time'].min()
        print(f"API调用时间: 平均={avg_api_time:.2f}秒, 最大={max_api_time:.2f}秒, 最小={min_api_time:.2f}秒")

def analyze_by_category(df_valid: pd.DataFrame):
    """按分类分析性能"""
    print("\n=== 按分类分析性能 ===")
    
    # L2分类性能分析
    print("\nL2分类性能:")
    l2_performance = {}
    for cat2 in df_valid['Cat2_std'].unique():
        subset = df_valid[df_valid['Cat2_std'] == cat2]
        if len(subset) >= 5:  # 只分析样本数>=5的分类
            accuracy = (subset['Cat2_std'] == subset['pred_l2_std']).mean()
            l2_performance[cat2] = {
                'accuracy': accuracy,
                'count': len(subset)
            }
    
    # 按准确率排序
    sorted_l2 = sorted(l2_performance.items(), key=lambda x: x[1]['accuracy'], reverse=True)
    for cat, perf in sorted_l2[:10]:  # 显示前10个
        print(f"  {cat}: {perf['accuracy']:.3f} ({perf['count']} 样本)")
    
    # L3分类性能分析
    print("\nL3分类性能 (Top 15):")
    l3_performance = {}
    for cat3 in df_valid['Cat3_std'].unique():
        subset = df_valid[df_valid['Cat3_std'] == cat3]
        if len(subset) >= 3:  # 只分析样本数>=3的分类
            accuracy = (subset['Cat3_std'] == subset['pred_l3_std']).mean()
            l3_performance[cat3] = {
                'accuracy': accuracy,
                'count': len(subset)
            }
    
    # 按准确率排序
    sorted_l3 = sorted(l3_performance.items(), key=lambda x: x[1]['accuracy'], reverse=True)
    for cat, perf in sorted_l3[:15]:  # 显示前15个
        print(f"  {cat}: {perf['accuracy']:.3f} ({perf['count']} 样本)")

def plot_performance_by_category(df_valid: pd.DataFrame, save_path: str = None):
    """绘制按分类的性能图表"""
    # L2分类性能
    l2_performance = {}
    for cat2 in df_valid['Cat2_std'].unique():
        subset = df_valid[df_valid['Cat2_std'] == cat2]
        if len(subset) >= 5:
            accuracy = (subset['Cat2_std'] == subset['pred_l2_std']).mean()
            l2_performance[cat2] = accuracy
    
    if l2_performance:
        plt.figure(figsize=(12, 8))
        categories = list(l2_performance.keys())
        accuracies = list(l2_performance.values())
        
        plt.bar(range(len(categories)), accuracies)
        plt.xlabel('L2 Categories')
        plt.ylabel('Accuracy')
        plt.title('L2 Classification Accuracy by Category')
        plt.xticks(range(len(categories)), categories, rotation=45, ha='right')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(f"{save_path}_l2_performance.png", dpi=300, bbox_inches='tight')
            print(f"L2性能图表已保存: {save_path}_l2_performance.png")
        
        plt.show()

def main():
    """主函数"""
    # 结果文件路径
    result_path = "dataset/nasa_topics/llm_graph_gpt3.json"
    
    # 检查文件是否存在
    if not Path(result_path).exists():
        print(f"结果文件不存在: {result_path}")
        print("请先运行以下步骤:")
        print("1. python code_nasa/preprocess_nasa_topics.py")
        print("2. python code_nasa/init_nasa_topics.py")
        print("3. python code_nasa/gpt_nasa_topics.py")
        return
    
    # 加载结果
    print("加载实验结果...")
    df = load_results(result_path)
    if df is None:
        return
    
    print(f"加载了 {len(df)} 条结果记录")
    
    # 计算指标
    print("计算评估指标...")
    result = calculate_metrics(df)
    if result is None:
        return
    
    metrics, df_valid = result
    
    # 打印指标
    print_metrics(metrics)
    
    # 错误分析
    analyze_errors(df_valid)
    
    # 检索性能分析
    analyze_retrieval_performance(df_valid)
    
    # 按分类分析性能
    analyze_by_category(df_valid)
    
    # 绘制性能图表（可选）
    plot_choice = input("\n是否绘制性能图表? (y/n): ").lower().strip()
    if plot_choice == 'y':
        plot_performance_by_category(df_valid, "nasa_topics_performance")
    
    # 保存详细报告
    report_path = "nasa_topics_evaluation_report.txt"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("NASA Topics数据集KG-HTC性能评估报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("1. 基本信息:\n")
        f.write(f"   总记录数: {len(df)}\n")
        f.write(f"   有效记录数: {len(df_valid)}\n")
        f.write(f"   成功率: {len(df_valid)/len(df)*100:.2f}%\n\n")
        
        f.write("2. 准确率:\n")
        for level, acc in metrics['accuracy'].items():
            f.write(f"   {level}: {acc:.4f} ({acc*100:.2f}%)\n")
        
        f.write("\n3. F1分数 (Macro):\n")
        for level, f1 in metrics['f1_macro'].items():
            f.write(f"   {level}: {f1:.4f}\n")
        
        f.write("\n4. F1分数 (Micro):\n")
        for level, f1 in metrics['f1_micro'].items():
            f.write(f"   {level}: {f1:.4f}\n")
    
    print(f"\n详细报告已保存: {report_path}")
    
    # 保存结果摘要
    summary = {
        "dataset": "NASA Topics with Multiple Terms",
        "total_records": len(df),
        "valid_records": len(df_valid),
        "success_rate": len(df_valid)/len(df),
        "metrics": metrics
    }
    
    with open("nasa_topics_results_summary.json", 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    print("结果摘要已保存: nasa_topics_results_summary.json")

if __name__ == "__main__":
    main()
