import pandas as pd
from pathlib import Path
from tqdm import tqdm
import sys
import json
import time
import logging

# 禁用 httpx 的详细日志
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)

# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

from src.llm import LLM
from src.graph_db import GraphDB
from src.pipeline import Pipeline

def run_nasa_experiment():
    """
    运行NASA数据集的KG-HTC分类实验

    该函数实现了基于知识图谱的层次化文本分类实验流程：
    1. 使用QdrantVectorDB进行向量检索，获取相关的L2和L3级别候选标签
    2. 使用GraphDB构建层次化知识图谱，获取标签间的层次关系
    3. 使用LLM进行逐级预测：L1 -> L2 -> L3
    4. 结合向量检索结果和图谱结构，提高分类准确性

    注意：此版本已更新为使用QdrantVectorDB（内存模式），与Pipeline类兼容
    """
    
    # 配置参数 - 已更新为支持QdrantVectorDB
    config = {
        "data_name": "nasa",
        "data_path": "dataset/nasa/nasa_val.csv",
        "output_path": "dataset/nasa/llm_graph_gpt3.json",
        # QdrantVectorDB配置参数
        "qdrant_host": None,  # None表示使用内存模式
        "qdrant_port": 6333,  # Qdrant服务器端口（内存模式下不使用）
        "qdrant_use_memory": True,  # 使用内存模式，适合本地开发和测试
        "template": {
            "sys": "prompts/system/nasa/llm_graph.txt",
            "user": "prompts/user/nasa/llm_graph.txt"
        },
        "query_params": {
            "l2_top_k": 11,  # NASA数据集二级分类较多，向量检索L2级别数量
            "l3_top_k": 53   # NASA数据集三级分类很多，向量检索L3级别数量
        }
    }

    # 构建向量数据库模式描述
    if config['qdrant_use_memory']:
        db_mode = "内存模式 (QdrantVectorDB)"
    else:
        db_mode = f"服务器模式 ({config['qdrant_host']}:{config['qdrant_port']})"
    print(f"向量数据库模式: {db_mode}")
    print(f"检索参数: L2_top_k={config['query_params']['l2_top_k']}, L3_top_k={config['query_params']['l3_top_k']}")
    
    # 读取数据
    try:
        df = pd.read_csv(config["data_path"])
        print(f"成功读取数据: {len(df)} 条记录")
    except FileNotFoundError:
        print(f"错误: 找不到数据文件 {config['data_path']}")
        print("请先运行 init_nasa.py 进行数据初始化")
        return
    
    # 数据预处理
    df = df.dropna(subset=['Title', 'Text', 'Cat1', 'Cat2', 'Cat3'])
    df = df[df['Cat1'] != "unknown"]
    df = df[df['Cat2'] != "unknown"]
    df = df[df['Cat3'] != "unknown"]
    
    # 限制实验数据量（可选）
    # if len(df) > 100:
    #     print(f"数据量较大({len(df)}条)，随机采样1000条进行实验")
    #     df = df.sample(n=20, random_state=42)
    
    ds = df.to_dict(orient="records")
    print(f"实验数据量: {len(ds)} 条记录")
    
    # 初始化模型和数据库
    print("\n=== 初始化模型和数据库 ===")
    try:
        llm = LLM()
        print("✓ LLM初始化成功")
    except Exception as e:
        print(f"✗ LLM初始化失败: {e}")
        print("请检查OpenAI API配置")
        return
    
    try:
        graph_db = GraphDB()
        print("✓ 图数据库连接成功")
    except Exception as e:
        print(f"✗ 图数据库连接失败: {e}")
        return
    
    try:
        # Pipeline会自动初始化QdrantVectorDB，使用config中的参数
        pipeline = Pipeline(llm, config)
        print("✓ Pipeline初始化成功")
        print(f"  - 运行模式: {'内存模式' if config['qdrant_use_memory'] else '服务器模式'}")
        
        # 获取向量数据库实例
        vector_db = pipeline._vector_db
        
        # 检查向量数据库是否为空，如果为空则重新初始化
        collection_info = vector_db.get_collection_info()
        title_count = collection_info['title_collection']['count']
        text_count = collection_info['text_collection']['count']

        if title_count == 0 and text_count == 0:
            print("\n⚠ 检测到向量数据库为空，正在重新初始化...")
            success = reinitialize_vector_database(vector_db, config)
            if not success:
                print("✗ 向量数据库重新初始化失败，无法继续实验")
                return
            print("✓ 向量数据库重新初始化成功")
            
    except Exception as e:
        print(f"✗ Pipeline初始化失败: {e}")
        print("可能的原因:")
        print("1. QdrantVectorDB初始化失败 - 检查qdrant-client是否已安装")
        print("2. 向量数据库为空 - 请先运行init_nasa.py初始化数据")
        print("3. LLM配置错误 - 检查OpenAI API配置")
        print("4. 模板文件不存在 - 检查prompts文件夹")
        return
    
    # # 获取所有可能的分类 - 移除L1分类
    # potential_level2 = df["Cat2"].unique()
    # potential_level3 = df["Cat3"].unique()
    # print(f"二级分类数量: {len(potential_level2)}")
    # print(f"三级分类数量: {len(potential_level3)}")
    
    # 开始推理
    print("\n=== 开始分类推理 ===")
    inference_list = []
    success_count = 0
    error_count = 0
    
    for idx in tqdm(range(len(ds)), desc="分类进度"):
        data = ds[idx].copy()
        
        try:
            # 构建查询文本 - 结合标题和描述信息
            query_txt_vecdb = f"Title: {data['Title']}\n"

            # 1. 向量检索相关节点
            # QdrantVectorDB会在text集合中搜索，使用OpenAI嵌入模型
            # 返回最相似的L2和L3级别标签，用于后续的层次化预测
            retrieved_nodes = pipeline.query_related_nodes(query_txt_vecdb)


            # 2. 构建子图
            sub_graph = pipeline.build_linked_labels(
                retrieved_nodes["l3"], 
                retrieved_nodes["l2"]
            )
            
            # 3. L2级别预测 - 直接使用所有L2候选和向量检索结果
            potential_level2_candidates = list(set(retrieved_nodes["l2"][:5]))
               
            pred_level2 = pipeline.predict_level(
                query_txt_vecdb, 
                potential_level2_candidates, 
                sub_graph
            )
            
            # 标准化L2预测结果
            
            
            # 4. L3级别预测
            try:
                # 从图数据库中查询L2对应的所有L3子类别
                #child_level2 = graph_db.query_l3_from_l2(pred_level2)
                # 结合图谱查询结果和向量检索结果
                potential_level3_candidates = list(set(retrieved_nodes["l3"][:5]))
            except:
                # 如果图查询失败，回退到向量检索结果
                potential_level3_candidates = retrieved_nodes["l3"]
            
            pred_level3 = pipeline.predict_level(
                query_txt_vecdb, 
                potential_level3_candidates, 
                sub_graph
            )
            
            # 标准化L3预测结果
            pred_level3 = pred_level3.lower().replace(' ', '_').replace('/', '_').replace('*', '').replace('\'', '').replace('\"', '')
            
            # 保存预测结果 - 移除L1预测
            data["gpt3_graph_l2"] = pred_level2
            data["gpt3_graph_l3"] = pred_level3

            # 添加检索和图谱信息用于后续分析
            # 这些信息有助于理解模型的决策过程和调试
            data["retrieved_l2"] = retrieved_nodes["l2"][:5]  # QdrantVectorDB检索的前5个L2结果
            data["retrieved_l3"] = retrieved_nodes["l3"][:5]  # QdrantVectorDB检索的前5个L3结果
            data["subgraph_size"] = len(sub_graph)  # 构建的子图大小，反映知识图谱的贡献
            
            inference_list.append(data)
            success_count += 1
            
            # 每处理100条记录保存一次中间结果
            if (idx + 1) % 100 == 0:
                temp_output_path = config["output_path"].replace('.json', f'_temp_{idx+1}.json')
                with open(temp_output_path, "w", encoding='utf-8') as f:
                    json.dump(inference_list, f, indent=2, ensure_ascii=False)
                print(f"\n中间结果已保存: {temp_output_path}")
            
            # 显示进度信息
            if (idx + 1) % 50 == 0:
                print(f"\n已处理 {idx+1}/{len(ds)} 条记录")
                print(f"成功: {success_count}, 失败: {error_count}")
                if len(inference_list) > 0:
                    latest_result = inference_list[-1]
                    print(f"最新结果: L2={latest_result['gpt3_graph_l2']}, L3={latest_result['gpt3_graph_l3']}")
            
        except Exception as e:
            error_count += 1
            print(f"\n处理第 {idx+1} 条记录时出错: {e}")

            # 添加错误记录，保持数据完整性
            # 常见错误原因：
            # 1. QdrantVectorDB查询失败 - 向量数据库为空或连接问题
            # 2. GraphDB查询失败 - Neo4j连接问题或图数据不完整
            # 3. LLM API调用失败 - OpenAI API限制或网络问题
            data["gpt3_graph_l2"] = "error"
            data["gpt3_graph_l3"] = "error"
            data["error_message"] = str(e)
            inference_list.append(data)

            # 如果错误太多，停止实验避免浪费资源
            if error_count > 50:
                print("错误数量过多，停止实验")
                break
        
        # 添加延迟避免API限制
        time.sleep(0.1)
    
    # 保存最终结果
    print(f"\n=== 保存实验结果 ===")
    try:
        with open(config["output_path"], "w", encoding='utf-8') as f:
            json.dump(inference_list, f, indent=2, ensure_ascii=False)
        print(f"✓ 结果已保存到: {config['output_path']}")
    except Exception as e:
        print(f"✗ 保存结果时出错: {e}")
    
    # 实验统计
    print(f"\n=== 实验统计 ===")
    print(f"总处理记录数: {len(inference_list)}")
    print(f"成功记录数: {success_count}")
    print(f"失败记录数: {error_count}")
    print(f"成功率: {success_count/len(inference_list)*100:.2f}%")
    
    # 简单的准确率计算 - 移除L1准确率
    if success_count > 0:
        correct_l2 = sum(1 for item in inference_list 
                        if item.get('gpt3_graph_l2', '').replace('_', ' ') == item.get('Cat2', '').lower().replace(' ', '_'))
        correct_l3 = sum(1 for item in inference_list 
                        if item.get('gpt3_graph_l3', '').replace('_', ' ') == item.get('Cat3', '').lower().replace(' ', '_'))
        
        # L2-L3路径完整准确率
        correct_l2_l3 = sum(1 for item in inference_list 
                           if (item.get('gpt3_graph_l2', '').replace('_', ' ') == item.get('Cat2', '').lower().replace(' ', '_') and
                               item.get('gpt3_graph_l3', '').replace('_', ' ') == item.get('Cat3', '').lower().replace(' ', '_')))
        
        print(f"\n=== 初步准确率 ===")
        print(f"L2准确率: {correct_l2/success_count*100:.2f}%")
        print(f"L3准确率: {correct_l3/success_count*100:.2f}%")
        print(f"L2-L3路径准确率: {correct_l2_l3/success_count*100:.2f}%")
    

def reinitialize_vector_database(vector_db, config):
    """
    重新初始化向量数据库
    先检查本地是否有已存储的向量数据库，如果有先加载存储好的，
    如果没有则对数据进行向量化，并将向量化的数据构建到本地数据库文件中

    Args:
        vector_db: 向量数据库实例
        config: 配置参数

    Returns:
        bool: 初始化是否成功
    """
    try:
        import pandas as pd

        # 1. 首先检查是否有本地存储的向量数据库
        print("  检查本地向量数据库...")
        if hasattr(vector_db, '_load_persisted_data'):
            try:
                vector_db._load_persisted_data()
                # 检查加载后的数据量
                collection_info = vector_db.get_collection_info()
                title_count = collection_info['title_collection']['count']
                text_count = collection_info['text_collection']['count']
                
                if title_count > 0 or text_count > 0:
                    print(f"  ✓ 成功加载本地向量数据库: title={title_count}, text={text_count}")
                    return True
                else:
                    print("  本地向量数据库为空，需要重新构建")
            except Exception as e:
                print(f"  加载本地向量数据库失败: {e}")

        # 2. 如果没有本地数据或加载失败，则重新构建
        print("  开始重新构建向量数据库...")
        
        # 读取训练数据
        data_path = config["data_path"].replace("nasa_val.csv", "nasa_train.csv")
        try:
            df = pd.read_csv(data_path)
            print(f"  读取训练数据: {len(df)} 条记录")
        except FileNotFoundError:
            print(f"  ✗ 找不到训练数据文件: {data_path}")
            return False

        # 数据清洗
        df = df.dropna(subset=['Title', 'Text', 'Cat1', 'Cat2', 'Cat3'])
        df = df[df['Title'] != "unknown"]
        df = df[df['Cat2'] != "unknown"]
        df = df[df['Cat3'] != "unknown"]
        print(f"  清洗后数据: {len(df)} 条记录")

        # 获取唯一标签
        label_l1 = [cat.lower().replace(' ', '_').replace('/', '_')
                    for cat in df["Title"].unique().tolist() if cat is not None]
        label_l2 = [cat.lower().replace(' ', '_').replace('/', '_')
                    for cat in df["Cat2"].unique().tolist() if cat is not None]
        label_l3 = [cat.lower().replace(' ', '_').replace('/', '_')
                    for cat in df["Cat3"].unique().tolist() if cat is not None]

        # 移除unknown标签
        label_l1 = [label for label in label_l1 if label != "unknown"]
        label_l2 = [label for label in label_l2 if label != "unknown"]
        label_l3 = [label for label in label_l3 if label != "unknown"]

        print(f"  标签统计: L1={len(label_l1)}, L2={len(label_l2)}, L3={len(label_l3)}")

        # 3. 添加到向量数据库并自动保存到本地
        if label_l1:
            vector_db.batch_add(
                titles=label_l1,
                texts=label_l1,
                metadatas=[{"level": "Category1"}] * len(label_l1)
            )
            print(f"  ✓ 已添加 {len(label_l1)} 个L1标签")

        if label_l2:
            vector_db.batch_add(
                titles=label_l2,
                texts=label_l2,
                metadatas=[{"level": "Category2"}] * len(label_l2)
            )
            print(f"  ✓ 已添加 {len(label_l2)} 个L2标签")

        if label_l3:
            vector_db.batch_add(
                titles=label_l3,
                texts=label_l3,
                metadatas=[{"level": "Category3"}] * len(label_l3)
            )
            print(f"  ✓ 已添加 {len(label_l3)} 个L3标签")

        # 4. 手动触发数据持久化保存
        if hasattr(vector_db, '_save_data_cache'):
            vector_db._save_data_cache()
            print("  ✓ 向量数据已保存到本地文件")

        # 验证添加结果
        collection_info = vector_db.get_collection_info()
        title_count = collection_info['title_collection']['count']
        text_count = collection_info['text_collection']['count']
        print(f"  验证结果: title集合={title_count}, text集合={text_count}")

        return title_count > 0 or text_count > 0

    except Exception as e:
        print(f"  ✗ 重新初始化失败: {e}")
        import traceback
        print(f"  详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    run_nasa_experiment()
    





