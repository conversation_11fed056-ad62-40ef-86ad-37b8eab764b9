import pandas as pd
from pathlib import Path
from tqdm import tqdm
import sys
import json
import time
import logging

# 禁用 httpx 的详细日志
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)

# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

from src.llm import LLM
from src.graph_db import GraphDB
from src.pipeline import Pipeline

def run_nasa_experiment():
    """
    运行NASA数据集的KG-HTC分类实验

    该函数实现了基于知识图谱的层次化文本分类实验流程：
    1. 使用QdrantVectorDB进行向量检索，获取相关的L2和L3级别候选标签
    2. 使用GraphDB构建层次化知识图谱，获取标签间的层次关系
    3. 使用LLM进行逐级预测：L1 -> L2 -> L3
    4. 结合向量检索结果和图谱结构，提高分类准确性

    注意：此版本已更新为使用QdrantVectorDB（内存模式），与Pipeline类兼容
    """
    
    # 配置参数 - 已更新为支持QdrantVectorDB
    config = {
        "data_name": "nasa",
        "data_path": "dataset/nasa/nasa_val.csv",
        "output_path": "dataset/nasa/llm_graph_gpt3.json",
        # QdrantVectorDB配置参数
        "vectdb_path": "database/nasa",
        "template": {
            "sys": "prompts/system/nasa/llm_graph.txt",
            "user": "prompts/user/nasa/llm_graph.txt"
        },
        "query_params": {
            "l2_top_k": 8,  # NASA数据集二级分类较多，向量检索L2级别数量
            "l3_top_k": 5   # NASA数据集三级分类很多，向量检索L3级别数量
        }
    }
    
    # 读取数据
    try:
        df = pd.read_csv(config["data_path"])
        print(f"成功读取数据: {len(df)} 条记录")
    except FileNotFoundError:
        print(f"错误: 找不到数据文件 {config['data_path']}")
        print("请先运行 init_nasa.py 进行数据初始化")
        return
    
    # 数据预处理
    df = df.dropna(subset=['Title', 'Text', 'Cat1', 'Cat2', 'Cat3'])
    df = df[df['Cat1'] != "unknown"]
    df = df[df['Cat2'] != "unknown"]
    df = df[df['Cat3'] != "unknown"]
    
    # 限制实验数据量（可选）
    if len(df) > 1000:
        print(f"数据量较大({len(df)}条)，随机采样1000条进行实验")
        df = df.sample(n=100, random_state=42)
    
    ds = df.to_dict(orient="records")
    print(f"实验数据量: {len(ds)} 条记录")
    
    # 初始化模型和数据库
    print("\n=== 初始化模型和数据库 ===")
    try:
        llm = LLM()
        print("✓ LLM初始化成功")
    except Exception as e:
        print(f"✗ LLM初始化失败: {e}")
        print("请检查OpenAI API配置")
        return
    
    try:
        graph_db = GraphDB()
        print("✓ 图数据库连接成功")
    except Exception as e:
        print(f"✗ 图数据库连接失败: {e}")
        return
    
    try:
        # Pipeline会自动初始化QdrantVectorDB，使用config中的参数
        pipeline = Pipeline(llm, config)
        print("✓ Pipeline初始化成功")
              
    except Exception as e:
        print(f"✗ Pipeline初始化失败: {e}")
        return
    
    # # 获取所有可能的分类 - 移除L1分类
    # potential_level2 = df["Cat2"].unique()
    # potential_level3 = df["Cat3"].unique()
    # print(f"二级分类数量: {len(potential_level2)}")
    # print(f"三级分类数量: {len(potential_level3)}")
    
    # 开始推理
    print("\n=== 开始分类推理 ===")
    inference_list = []
    success_count = 0
    error_count = 0
    
    for idx in tqdm(range(len(ds)), desc="分类进度"):
        data = ds[idx].copy()
        
        try:
            title = data.get('Title', '').strip()
            text = data.get('Text', '').strip()
            #query_text = f"Title: {title}\n"
            query_txt_vecdb = f"Title: {title}\nDescription: {text[:500]}"
            # 构建查询文本 - 结合标题和描述信息
            #query_txt_vecdb = f"Title: {data['Title']}\n"

            # 1. 向量检索相关节点
            # QdrantVectorDB会在text集合中搜索，使用OpenAI嵌入模型
            # 返回最相似的L2和L3级别标签，用于后续的层次化预测
            retrieved_nodes = pipeline.query_related_nodes(query_txt_vecdb)
            #print(retrieved_nodes)

            # 2. 构建子图
            l3_nodes = retrieved_nodes.get("l3", []) or []
            l2_nodes = retrieved_nodes.get("l2", []) or []

            #print(l3_nodes)

            sub_graph = pipeline.build_linked_labels(l3_nodes, l2_nodes)
            #print(f"构建的子图: {sub_graph}")

            # 3. L2级别预测 - 直接使用所有L2候选和向量检索结果
            potential_level2_candidates = list(set(retrieved_nodes["l2"]))
                
            pred_level2 = pipeline.predict_level_nasa(
                query_txt_vecdb, 
                potential_level2_candidates, 
                sub_graph
            )
            
            # 标准化L2预测结果
            #print(f"预测二级标签: {pred_level2}")
            # 4. L3级别预测
            try:
                # 从图数据库中查询L2对应的所有L3子类别
                child_level2 = graph_db.query_l3_from_l2(pred_level2)
                # 结合图谱查询结果和向量检索结果
                potential_level3_candidates = list(set(child_level2 + retrieved_nodes["l3"]))
            except:
                # 如果图查询失败，回退到向量检索结果
                potential_level3_candidates = retrieved_nodes["l3"]
            
            pred_level3 = pipeline.predict_level_nasa(
                query_txt_vecdb, 
                potential_level3_candidates, 
                sub_graph
            )
            
            # 标准化L3预测结果
            
            #print(f"预测三级标签: {pred_level3}")
            # 保存预测结果 - 移除L1预测
            data["gpt3_graph_l2"] = pred_level2
            data["gpt3_graph_l3"] = pred_level3

            # 添加检索和图谱信息用于后续分析
            # 这些信息有助于理解模型的决策过程和调试
            data["retrieved_l2"] = retrieved_nodes["l2"][:5]  
            data["retrieved_l3"] = retrieved_nodes["l3"][:5]  
            data["subgraph_size"] = len(sub_graph)  # 构建的子图大小，反映知识图谱的贡献
            
            inference_list.append(data)
            success_count += 1
            
            # 每处理100条记录保存一次中间结果
            if (idx + 1) % 100 == 0:
                temp_output_path = config["output_path"].replace('.json', f'_temp_{idx+1}.json')
                with open(temp_output_path, "w", encoding='utf-8') as f:
                    json.dump(inference_list, f, indent=2, ensure_ascii=False)
                print(f"\n中间结果已保存: {temp_output_path}")
            
            # 显示进度信息
            if (idx + 1) % 50 == 0:
                print(f"\n已处理 {idx+1}/{len(ds)} 条记录")
                print(f"成功: {success_count}, 失败: {error_count}")
                if len(inference_list) > 0:
                    latest_result = inference_list[-1]
                    print(f"最新结果: L2={latest_result['gpt3_graph_l2']}, L3={latest_result['gpt3_graph_l3']}")
            
        except Exception as e:
            error_count += 1
            print(f"\n处理第 {idx+1} 条记录时出错: {e}")

            data["gpt3_graph_l2"] = "error"
            data["gpt3_graph_l3"] = "error"
            data["error_message"] = str(e)
            inference_list.append(data)

            # 如果错误太多，停止实验避免浪费资源
            if error_count > 50:
                print("错误数量过多，停止实验")
                break
        
        # 添加延迟避免API限制
        time.sleep(0.02)
    
    # 保存最终结果
    print(f"\n=== 保存实验结果 ===")
    try:
        with open(config["output_path"], "w", encoding='utf-8') as f:
            json.dump(inference_list, f, indent=2, ensure_ascii=False)
        print(f"✓ 结果已保存到: {config['output_path']}")
    except Exception as e:
        print(f"✗ 保存结果时出错: {e}")
    
    # 实验统计
    print(f"\n=== 实验统计 ===")
    print(f"总处理记录数: {len(inference_list)}")
    print(f"成功记录数: {success_count}")
    print(f"失败记录数: {error_count}")
    print(f"成功率: {success_count/len(inference_list)*100:.2f}%")
    
    # 简单的准确率计算 - 移除L1准确率
    if success_count > 0:
        correct_l2 = sum(1 for item in inference_list 
                        if item.get('gpt3_graph_l2', '').replace('_', ' ') == item.get('Cat2', '').lower().replace(' ', '_'))
        correct_l3 = sum(1 for item in inference_list 
                        if item.get('gpt3_graph_l3', '').replace('_', ' ') == item.get('Cat3', '').lower().replace(' ', '_'))
        
        # L2-L3路径完整准确率
        correct_l2_l3 = sum(1 for item in inference_list 
                           if (item.get('gpt3_graph_l2', '').replace('_', ' ') == item.get('Cat2', '').lower().replace(' ', '_') and
                               item.get('gpt3_graph_l3', '').replace('_', ' ') == item.get('Cat3', '').lower().replace(' ', '_')))
        
        print(f"\n=== 初步准确率 ===")
        print(f"L2准确率: {correct_l2/success_count*100:.2f}%")
        print(f"L3准确率: {correct_l3/success_count*100:.2f}%")
        print(f"L2-L3路径准确率: {correct_l2_l3/success_count*100:.2f}%")
    

if __name__ == "__main__":
    run_nasa_experiment()
    





