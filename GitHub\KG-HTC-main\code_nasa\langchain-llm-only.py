import pandas as pd
from pathlib import Path
from tqdm import tqdm
import sys
import json
import logging
from sklearn.metrics import accuracy_score, f1_score
from typing import List, Optional
from langchain_core.pydantic_v1 import BaseModel, Field
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.runnables import RunnablePassthrough
from langchain_openai import ChatOpenAI
from tenacity import retry, stop_after_attempt, wait_exponential
import httpx
from langchain_core.output_parsers import StrOutputParser

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("nasa_classification.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置OpenAI参数
OPENAI_CONFIG = {
    "model_name": "Qwen/Qwen3-8B",  # 建议使用最新模型
    "api_key": "sk-azimfqcuqtonsslreokplibyzkrjmeawuiumzrqzzkbdnmoq",       # 替换为您的API密钥
    "base_url": "https://api.siliconflow.cn/v1/",  # 默认官方API端点
    "max_tokens": 1024,
    "temperature": 0.4,
    "timeout": 30
}

# 定义结构化输出模型
class ClassificationResult(BaseModel):
    """分类结果模型"""
    category: str = Field(description="提取的分类标签")
    confidence: Optional[str] = Field(description="分类置信度", default=None)

@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
def create_classification_chain(categories: List[str], level: str):
    """创建分类链，包含更健壮的输出处理"""
    system_prompt = f"""You are a NASA Earth science dataset classifier. 
    Classify the given dataset into one of these {level} categories: {', '.join(categories)}.
    Respond STRICTLY in this JSON format: {{"category": "category_name"}}
    Do not include any other text or explanations."""
    
    # 修正后的系统提示（移除可能被误认为变量的内容）
    # system_prompt = f"""
    # You are a NASA Earth science dataset classifier. 
    # Classify the given dataset into one of these {level} categories: {', '.join(categories)}.
    # Respond STRICTLY in this format:
    # ```json
    # {{"category": "exact_category_name"}}
    # ```
    # Do not include any other text or explanations.
    # """

    prompt_template = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        ("human", "Dataset:\nTitle: {title}\nDescription: {text}\n\nOutput:")
    ], input_variables=["title", "text"])
    
            # 初始化带完整配置的ChatOpenAI
    llm = ChatOpenAI(
            model=OPENAI_CONFIG["model_name"],
            openai_api_key=OPENAI_CONFIG["api_key"],
            base_url=OPENAI_CONFIG["base_url"],
            max_tokens=OPENAI_CONFIG["max_tokens"],
            temperature=OPENAI_CONFIG["temperature"],
        )

    # 方案1：使用StrOutputParser + 自定义解析
    chain = (
        {"title": RunnablePassthrough(), "text": RunnablePassthrough()}
        | prompt_template
        | llm
        | StrOutputParser()
        | parse_json_output  # 自定义解析函数
    )
    return chain

def parse_json_output(raw_text: str) -> dict:
    """处理可能不规范的JSON输出"""
    try:
        # 尝试直接解析
        return json.loads(raw_text)
    except json.JSONDecodeError:
        # 如果失败，尝试修复常见问题
        cleaned = raw_text.strip()
        if cleaned.startswith("{") and cleaned.endswith("}"):
            try:
                return json.loads(cleaned)
            except:
                pass
        
        # 最后尝试包装成JSON
        return {"category": cleaned.split('"')[0].split("\n")[0].strip()}

def extract_label(response: dict, candidates: List[str]) -> str:
    """带日志的标签提取"""
    try:
        if not response or "category" not in response:
            logger.warning("无效的LLM响应格式")
            return "error"
        
        predicted = response["category"].strip().lower()
        
        # 精确匹配优先
        for candidate in candidates:
            if candidate.lower() == predicted:
                return candidate.lower()
        
        # 模糊匹配
        for candidate in candidates:
            if candidate.lower() in predicted:
                logger.warning(f"使用了模糊匹配: {predicted} -> {candidate}")
                return candidate.lower()
        
        logger.warning(f"未匹配到有效标签: {predicted}")
        return "error"
    except Exception as e:
        logger.error(f"标签提取错误: {str(e)}")
        return "error"

def evaluate_results(results):
    """
    评估LLM-Only实验结果（只评估L2和L3）
    """
    print("\n=== 评估结果 ===")
    
    # 筛选有效结果
    valid_results = [r for r in results if "pred_l2" in r and "pred_l3" in r 
                    and r["pred_l2"] != "error" and r["pred_l3"] != "error"]
    
    if not valid_results:
        logger.warning("没有有效结果可评估")
        return
    
    print(f"有效预测记录数: {len(valid_results)}/{len(results)}")
    
    # 准备真实标签和预测标签
    true_l2 = [r['true_l2'].lower() for r in valid_results]
    true_l3 = [r['true_l3'].lower() for r in valid_results]
    
    pred_l2 = [r['pred_l2'].lower() for r in valid_results]
    pred_l3 = [r['pred_l3'].lower() for r in valid_results]
    
    # 计算各层级准确率
    acc_l2 = accuracy_score(true_l2, pred_l2)
    acc_l3 = accuracy_score(true_l3, pred_l3)
    
    # 计算各层级F1分数
    f1_l2_macro = f1_score(true_l2, pred_l2, average='macro', zero_division=0)
    f1_l3_macro = f1_score(true_l3, pred_l3, average='macro', zero_division=0)
    
    f1_l2_micro = f1_score(true_l2, pred_l2, average='micro', zero_division=0)
    f1_l3_micro = f1_score(true_l3, pred_l3, average='micro', zero_division=0)
    
    # 计算层次化准确率（L2和L3都正确）
    hierarchical_correct = sum(1 for i in range(len(valid_results)) 
                             if true_l2[i] == pred_l2[i] and 
                                true_l3[i] == pred_l3[i])
    hierarchical_acc = hierarchical_correct / len(valid_results)
    
    # 打印结果
    print("\n1. 准确率 (Accuracy):")
    print(f"   L2: {acc_l2:.4f} ({acc_l2*100:.2f}%)")
    print(f"   L3: {acc_l3:.4f} ({acc_l3*100:.2f}%)")
    print(f"   Hierarchical (L2+L3): {hierarchical_acc:.4f} ({hierarchical_acc*100:.2f}%)")
    
    print("\n2. F1分数 - Macro平均:")
    print(f"   L2: {f1_l2_macro:.4f}")
    print(f"   L3: {f1_l3_macro:.4f}")
    
    print("\n3. F1分数 - Micro平均:")
    print(f"   L2: {f1_l2_micro:.4f}")
    print(f"   L3: {f1_l3_micro:.4f}")
    
    
    # 保存评估报告
    metrics = {
        'accuracy': {
            'L2': acc_l2,
            'L3': acc_l3,
            'Hierarchical_L2_L3': hierarchical_acc
        },
        'f1_macro': {
            'L2': f1_l2_macro,
            'L3': f1_l3_macro
        },
        'f1_micro': {
            'L2': f1_l2_micro,
            'L3': f1_l3_micro
        },
        'statistics': {
            'total_samples': len(results),
            'valid_samples': len(valid_results),
            'success_rate': len(valid_results) / len(results)
        }
    }
    
    # 保存详细报告
    report_path = "D:/Project/GitHub/KG-HTC-main/code_nasa/dataset/nasa/llm_only_evaluation_report.txt"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("NASA Topics数据集LLM-Only性能评估报告 (L2和L3)\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("1. 基本信息:\n")
        f.write(f"   总记录数: {len(results)}\n")
        f.write(f"   有效记录数: {len(valid_results)}\n")
        f.write(f"   成功率: {len(valid_results)/len(results)*100:.2f}%\n\n")
        
        f.write("2. 准确率:\n")
        for level, acc in metrics['accuracy'].items():
            f.write(f"   {level}: {acc:.4f} ({acc*100:.2f}%)\n")
        
        f.write("\n3. F1分数 (Macro):\n")
        for level, f1 in metrics['f1_macro'].items():
            f.write(f"   {level}: {f1:.4f}\n")
        
        f.write("\n4. F1分数 (Micro):\n")
        for level, f1 in metrics['f1_micro'].items():
            f.write(f"   {level}: {f1:.4f}\n")
    
    print(f"\n详细报告已保存: {report_path}")
    
    return metrics

def run_experiment():
    """主实验函数"""
    # 实验配置
    config = {
        "data_path": "D:/Project/GitHub/KG-HTC-main/code_nasa/dataset/nasa/nasa_val.csv",
        "output_path": "D:/Project/GitHub/KG-HTC-main/code_nasa/dataset/nasa/llm_results.json",
        "max_samples": 500,
        "text_truncate": 500  # 截断文本长度
    }
    
    logger.info("=== 开始NASA分类实验 ===")
    
    try:
        # 数据加载
        df = pd.read_csv(config["data_path"])
        logger.info(f"数据加载成功，总记录数: {len(df)}")
        
        # 数据预处理
        df = df.dropna(subset=['Title', 'Text', 'Cat2', 'Cat3'])
        df = df[~df['Cat2'].isin(["unknown", "none"])]
        df = df[~df['Cat3'].isin(["unknown", "none"])]
        
        if len(df) > config["max_samples"]:
            df = df.sample(n=config["max_samples"], random_state=42)
            logger.info(f"采样至{config['max_samples']}条记录")
        
        # 准备分类链
        l2_categories = df['Cat2'].unique().tolist()
        l3_categories = df['Cat3'].unique().tolist()
        
        l2_chain = create_classification_chain(l2_categories, "L2主题")
        l3_chain = create_classification_chain(l3_categories, "L3术语")
        
        # 运行实验
        results = []
        for idx, row in tqdm(df.iterrows(), total=len(df)):
            try:
                # L2分类
                l2_res = l2_chain.invoke({
                    "title": row['Title'],
                    "text": row['Text'][:config["text_truncate"]]
                })
                pred_l2 = extract_label(l2_res, l2_categories)
                
                # L3分类
                l3_res = l3_chain.invoke({
                    "title": row['Title'],
                    "text": row['Text'][:config["text_truncate"]]
                })
                pred_l3 = extract_label(l3_res, l3_categories)
                
                results.append({
                    "id": idx,
                    "title": row['Title'],
                    "true_l2": row['Cat2'],
                    "true_l3": row['Cat3'],
                    "pred_l2": pred_l2,
                    "pred_l3": pred_l3,
                    "text_truncated": row['Text'][:config["text_truncate"]]
                })
                
            except Exception as e:
                logger.error(f"处理记录{idx}失败: {str(e)}")
                results.append({
                    "id": idx,
                    "error": str(e)
                })
        
        # 保存结果
        with open(config["output_path"], "w", encoding="utf-8") as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        logger.info(f"结果已保存到 {config['output_path']}")
        
        # 评估结果
        evaluate_results(results)
        
    except Exception as e:
        logger.critical(f"实验运行失败: {str(e)}", exc_info=True)
        raise


if __name__ == "__main__":
    try:
        run_experiment()
    except KeyboardInterrupt:
        logger.info("实验被用户中断")
    except Exception as e:
        logger.critical(f"程序异常终止: {str(e)}", exc_info=True)
        sys.exit(1)