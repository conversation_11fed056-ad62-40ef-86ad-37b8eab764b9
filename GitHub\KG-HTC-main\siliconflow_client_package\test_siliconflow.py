"""
SiliconFlow API 客户端测试文件
全面测试推理模型和嵌入模型的功能
"""

import os
import sys
import time
from typing import List, Dict, Any

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from siliconflow_client import SiliconFlowClient, SiliconFlowChatClient, SiliconFlowEmbeddingClient
    from config import SiliconFlowConfig
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保 siliconflow_client.py 和 config.py 文件在同一目录下")
    sys.exit(1)


class SiliconFlowTester:
    """SiliconFlow API 测试类"""
    
    def __init__(self, api_key: str):
        """
        初始化测试器
        
        Args:
            api_key (str): SiliconFlow API 密钥
        """
        self.api_key = api_key
        
        # 创建配置
        self.config = SiliconFlowConfig()
        self.config.set("api_key", api_key)
        
        # 初始化客户端
        self.unified_client = SiliconFlowClient(api_key=api_key, config=self.config)
        self.chat_client = SiliconFlowChatClient(api_key=api_key, config=self.config)
        self.embedding_client = SiliconFlowEmbeddingClient(api_key=api_key, config=self.config)
        
        # 测试结果
        self.test_results = {}
    
    def test_basic_connection(self) -> bool:
        """测试基本连接"""
        print("\n🔌 测试基本连接")
        print("-" * 40)
        
        try:
            # 使用统一客户端测试连接
            results = self.unified_client.test_connection()
            
            chat_ok = results.get("chat", False)
            embedding_ok = results.get("embeddings", False)
            
            self.test_results["basic_connection"] = {
                "chat": chat_ok,
                "embeddings": embedding_ok,
                "overall": chat_ok and embedding_ok
            }
            
            return chat_ok and embedding_ok
            
        except Exception as e:
            print(f"❌ 连接测试失败: {e}")
            self.test_results["basic_connection"] = {"overall": False, "error": str(e)}
            return False
    
    def test_chat_functionality(self) -> bool:
        """测试聊天功能"""
        print("\n💬 测试聊天功能")
        print("-" * 40)
        
        try:
            # 测试1: 简单对话
            print("📝 测试1: 简单对话")
            simple_response = self.chat_client.simple_chat("你好，请简单介绍一下自己")
            
            if simple_response:
                print(f"✅ 简单对话成功: {simple_response[:100]}...")
                simple_chat_ok = True
            else:
                print("❌ 简单对话失败")
                simple_chat_ok = False
            
            # 测试2: 多轮对话
            print("\n📝 测试2: 多轮对话")
            messages = [
                {"role": "user", "content": "什么是机器学习？"},
                {"role": "assistant", "content": "机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进。"},
                {"role": "user", "content": "能举个具体的例子吗？"}
            ]
            
            multi_response = self.chat_client.chat_completions(
                messages=messages,
                temperature=0.7,
                max_tokens=200
            )
            
            if multi_response and "choices" in multi_response:
                content = multi_response["choices"][0]["message"]["content"]
                print(f"✅ 多轮对话成功: {content[:100]}...")
                multi_chat_ok = True
            else:
                print("❌ 多轮对话失败")
                multi_chat_ok = False
            
            # 测试3: 参数配置
            print("\n📝 测试3: 参数配置")
            param_response = self.chat_client.chat_completions(
                messages=[{"role": "user", "content": "请用一句话解释什么是深度学习"}],
                temperature=0.1,  # 低温度，更确定性
                max_tokens=50,
                top_p=0.9
            )
            
            if param_response and "choices" in param_response:
                print(f"✅ 参数配置成功")
                param_ok = True
            else:
                print("❌ 参数配置失败")
                param_ok = False
            
            overall_chat_ok = simple_chat_ok and multi_chat_ok and param_ok
            
            self.test_results["chat_functionality"] = {
                "simple_chat": simple_chat_ok,
                "multi_chat": multi_chat_ok,
                "parameter_config": param_ok,
                "overall": overall_chat_ok
            }
            
            return overall_chat_ok
            
        except Exception as e:
            print(f"❌ 聊天功能测试失败: {e}")
            self.test_results["chat_functionality"] = {"overall": False, "error": str(e)}
            return False
    
    def test_embedding_functionality(self) -> bool:
        """测试嵌入功能"""
        print("\n🔤 测试嵌入功能")
        print("-" * 40)
        
        try:
            # 测试1: 单个文本嵌入
            print("📝 测试1: 单个文本嵌入")
            single_text = "人工智能是计算机科学的一个重要分支"
            single_vector = self.embedding_client.get_embedding_vector(single_text)
            
            if single_vector and isinstance(single_vector, list) and len(single_vector) > 0:
                print(f"✅ 单个文本嵌入成功，向量维度: {len(single_vector)}")
                single_embedding_ok = True
            else:
                print("❌ 单个文本嵌入失败")
                single_embedding_ok = False
            
            # 测试2: 批量文本嵌入
            print("\n📝 测试2: 批量文本嵌入")
            batch_texts = [
                "机器学习是人工智能的核心技术",
                "深度学习使用神经网络进行学习",
                "自然语言处理帮助计算机理解人类语言",
                "计算机视觉让机器能够理解图像"
            ]
            
            batch_result = self.embedding_client.create_embeddings(batch_texts)
            
            if (batch_result and "data" in batch_result and 
                len(batch_result["data"]) == len(batch_texts)):
                print(f"✅ 批量文本嵌入成功，处理了 {len(batch_result['data'])} 个文本")
                batch_embedding_ok = True
                
                # 验证向量维度一致性
                dimensions = [len(item["embedding"]) for item in batch_result["data"]]
                if len(set(dimensions)) == 1:
                    print(f"✅ 向量维度一致: {dimensions[0]}")
                else:
                    print(f"⚠️  向量维度不一致: {dimensions}")
                    
            else:
                print("❌ 批量文本嵌入失败")
                batch_embedding_ok = False
            
            # 测试3: 文本相似度计算
            print("\n📝 测试3: 文本相似度计算")
            if single_vector and batch_result:
                # 计算第一个文本与其他文本的相似度
                def cosine_similarity(vec1, vec2):
                    import math
                    dot_product = sum(a * b for a, b in zip(vec1, vec2))
                    magnitude1 = math.sqrt(sum(a * a for a in vec1))
                    magnitude2 = math.sqrt(sum(a * a for a in vec2))
                    return dot_product / (magnitude1 * magnitude2) if magnitude1 and magnitude2 else 0
                
                similarities = []
                for item in batch_result["data"]:
                    sim = cosine_similarity(single_vector, item["embedding"])
                    similarities.append(sim)
                
                print(f"✅ 相似度计算成功:")
                for i, (text, sim) in enumerate(zip(batch_texts, similarities)):
                    print(f"   文本{i+1}: {sim:.4f} - {text[:30]}...")
                
                similarity_ok = True
            else:
                print("❌ 相似度计算失败（缺少向量数据）")
                similarity_ok = False
            
            overall_embedding_ok = single_embedding_ok and batch_embedding_ok and similarity_ok
            
            self.test_results["embedding_functionality"] = {
                "single_embedding": single_embedding_ok,
                "batch_embedding": batch_embedding_ok,
                "similarity_calculation": similarity_ok,
                "overall": overall_embedding_ok
            }
            
            return overall_embedding_ok
            
        except Exception as e:
            print(f"❌ 嵌入功能测试失败: {e}")
            self.test_results["embedding_functionality"] = {"overall": False, "error": str(e)}
            return False
    
    def test_error_handling(self) -> bool:
        """测试错误处理"""
        print("\n🛡️  测试错误处理")
        print("-" * 40)
        
        try:
            # 测试1: 无效API密钥
            print("📝 测试1: 无效API密钥处理")
            invalid_client = SiliconFlowChatClient(api_key="invalid_key")
            invalid_response = invalid_client.simple_chat("测试")
            
            if invalid_response is None:
                print("✅ 无效API密钥正确处理")
                invalid_key_ok = True
            else:
                print("❌ 无效API密钥处理异常")
                invalid_key_ok = False
            
            # 测试2: 空消息处理
            print("\n📝 测试2: 空消息处理")
            try:
                empty_response = self.chat_client.chat_completions(messages=[])
                if empty_response is None:
                    print("✅ 空消息正确处理")
                    empty_msg_ok = True
                else:
                    print("⚠️  空消息返回了响应（可能是正常行为）")
                    empty_msg_ok = True
            except Exception:
                print("✅ 空消息触发异常（正确行为）")
                empty_msg_ok = True
            
            overall_error_ok = invalid_key_ok and empty_msg_ok
            
            self.test_results["error_handling"] = {
                "invalid_key": invalid_key_ok,
                "empty_message": empty_msg_ok,
                "overall": overall_error_ok
            }
            
            return overall_error_ok
            
        except Exception as e:
            print(f"❌ 错误处理测试失败: {e}")
            self.test_results["error_handling"] = {"overall": False, "error": str(e)}
            return False
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        print("🚀 开始 SiliconFlow API 全面测试")
        print("=" * 60)
        
        start_time = time.time()
        
        # 运行各项测试
        tests = [
            ("基本连接", self.test_basic_connection),
            ("聊天功能", self.test_chat_functionality),
            ("嵌入功能", self.test_embedding_functionality),
            ("错误处理", self.test_error_handling)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed_tests += 1
                    print(f"\n✅ {test_name}测试通过")
                else:
                    print(f"\n❌ {test_name}测试失败")
            except Exception as e:
                print(f"\n💥 {test_name}测试异常: {e}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 生成测试报告
        print("\n" + "=" * 60)
        print("📊 测试报告")
        print("=" * 60)
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {total_tests - passed_tests}")
        print(f"成功率: {(passed_tests / total_tests * 100):.1f}%")
        print(f"测试耗时: {duration:.2f} 秒")
        
        # 详细结果
        print("\n📋 详细结果:")
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result.get("overall", False) else "❌ 失败"
            print(f"  {test_name}: {status}")
            if "error" in result:
                print(f"    错误: {result['error']}")
        
        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": passed_tests / total_tests,
            "duration": duration,
            "detailed_results": self.test_results
        }


def main():
    """主函数"""
    print("🌟 SiliconFlow API 客户端测试程序")
    print("=" * 60)
    
    # 获取API密钥
    api_key = os.getenv("SILICONFLOW_API_KEY")
    
    if not api_key:
        print("⚠️  未找到API密钥")
        print("请设置环境变量 SILICONFLOW_API_KEY 或在下方输入:")
        api_key = input("API密钥: ").strip()
    
    if not api_key:
        print("❌ 未提供API密钥，测试终止")
        return
    
    # 创建测试器并运行测试
    tester = SiliconFlowTester(api_key)
    results = tester.run_all_tests()
    
    # 根据测试结果返回适当的退出码
    if results["success_rate"] == 1.0:
        print("\n🎉 所有测试通过！")
        sys.exit(0)
    else:
        print(f"\n⚠️  部分测试失败，成功率: {results['success_rate']*100:.1f}%")
        sys.exit(1)


if __name__ == "__main__":
    main()
