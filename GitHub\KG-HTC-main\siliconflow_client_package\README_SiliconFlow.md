# SiliconFlow API 客户端

基于原始测试代码封装的 SiliconFlow API 客户端，提供推理模型和嵌入模型的调用功能，兼容 OpenAI 接口设计。

## 🌟 主要特性

- **🤖 推理模型客户端** - 支持多轮对话、参数配置、自动重试
- **🔤 嵌入模型客户端** - 支持单个和批量文本向量化
- **🔧 统一客户端** - 兼容 OpenAI 接口设计，一站式访问
- **⚙️ 配置管理** - 支持环境变量、配置文件、动态配置
- **🛡️ 错误处理** - 完善的异常处理和重试机制
- **📊 详细日志** - 支持请求跟踪和使用统计

## 📁 文件结构

```
├── siliconflow_client.py    # 主客户端文件
├── config.py               # 配置管理模块
├── example_usage.py        # 使用示例
├── test_siliconflow.py     # 完整测试套件
└── README_SiliconFlow.md   # 本文档
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install requests urllib3
```

### 2. 设置 API 密钥

**方式一：环境变量（推荐）**
```bash
export SILICONFLOW_API_KEY="your-api-key-here"
```

**方式二：直接在代码中设置**
```python
api_key = "your-api-key-here"
```

### 3. 基本使用

```python
from siliconflow_client import SiliconFlowClient

# 初始化客户端
client = SiliconFlowClient(api_key="your-api-key")

# 推理模型调用
response = client.chat.simple_chat("你好，请介绍一下人工智能")
print(response)

# 嵌入模型调用
vector = client.embeddings.get_embedding_vector("这是一个测试文本")
print(f"向量维度: {len(vector)}")
```

## 📖 详细使用指南

### 推理模型使用

#### 简单对话
```python
from siliconflow_client import SiliconFlowChatClient

chat_client = SiliconFlowChatClient(api_key="your-api-key")

# 单轮对话
response = chat_client.simple_chat("什么是机器学习？")
print(response)
```

#### 多轮对话
```python
messages = [
    {"role": "user", "content": "你好"},
    {"role": "assistant", "content": "你好！有什么可以帮助你的吗？"},
    {"role": "user", "content": "请解释一下深度学习"}
]

result = chat_client.chat_completions(
    messages=messages,
    model="Qwen/Qwen2.5-7B-Instruct",
    temperature=0.7,
    max_tokens=500
)

if result and "choices" in result:
    print(result["choices"][0]["message"]["content"])
```

#### 参数配置
```python
# 高级参数配置
result = chat_client.chat_completions(
    messages=[{"role": "user", "content": "写一首关于春天的诗"}],
    model="Qwen/Qwen2.5-7B-Instruct",
    temperature=0.8,        # 创造性输出
    top_p=0.9,             # 核采样
    max_tokens=200,        # 最大输出长度
    frequency_penalty=0.1, # 频率惩罚
    presence_penalty=0.1   # 存在惩罚
)
```

### 嵌入模型使用

#### 单个文本向量化
```python
from siliconflow_client import SiliconFlowEmbeddingClient

embedding_client = SiliconFlowEmbeddingClient(api_key="your-api-key")

# 获取文本向量
text = "人工智能是计算机科学的重要分支"
vector = embedding_client.get_embedding_vector(text)

print(f"文本: {text}")
print(f"向量维度: {len(vector)}")
print(f"向量前5个值: {vector[:5]}")
```

#### 批量文本向量化
```python
texts = [
    "机器学习是人工智能的核心技术",
    "深度学习使用神经网络进行学习",
    "自然语言处理帮助计算机理解人类语言"
]

result = embedding_client.create_embeddings(texts)

if result and "data" in result:
    for i, item in enumerate(result["data"]):
        print(f"文本{i+1}向量维度: {len(item['embedding'])}")
```

#### 文本相似度计算
```python
import math

def cosine_similarity(vec1, vec2):
    """计算余弦相似度"""
    dot_product = sum(a * b for a, b in zip(vec1, vec2))
    magnitude1 = math.sqrt(sum(a * a for a in vec1))
    magnitude2 = math.sqrt(sum(a * a for a in vec2))
    return dot_product / (magnitude1 * magnitude2) if magnitude1 and magnitude2 else 0

# 计算两个文本的相似度
text1 = "机器学习是人工智能的重要分支"
text2 = "深度学习属于机器学习的范畴"

vec1 = embedding_client.get_embedding_vector(text1)
vec2 = embedding_client.get_embedding_vector(text2)

similarity = cosine_similarity(vec1, vec2)
print(f"文本相似度: {similarity:.4f}")
```

### 配置管理

#### 使用配置文件
```python
from config import SiliconFlowConfig, load_config_from_file

# 从文件加载配置
config = load_config_from_file("my_config.json")

# 创建客户端
client = SiliconFlowClient(config=config)
```

#### 动态配置
```python
from config import SiliconFlowConfig

# 创建自定义配置
config = SiliconFlowConfig()
config.set("api_key", "your-api-key")
config.set("chat_models.default", "Qwen/Qwen2.5-14B-Instruct")
config.set("default_chat_params.temperature", 0.8)

# 验证配置
is_valid, message = config.validate()
print(f"配置验证: {message}")

# 使用配置
client = SiliconFlowClient(config=config)
```

## 🔧 可用模型

### 推理模型
- `Qwen/Qwen2.5-7B-Instruct` (默认)
- `Qwen/Qwen2.5-14B-Instruct`
- `Qwen/Qwen2.5-32B-Instruct`
- `Qwen/Qwen2.5-72B-Instruct`
- `meta-llama/Meta-Llama-3.1-8B-Instruct`
- `meta-llama/Meta-Llama-3.1-70B-Instruct`
- `deepseek-ai/DeepSeek-V2.5`
- `01-ai/Yi-1.5-9B-Chat-16K`
- `01-ai/Yi-1.5-34B-Chat-16K`

### 嵌入模型
- `BAAI/bge-m3` (默认)
- `BAAI/bge-large-zh-v1.5`
- `BAAI/bge-large-en-v1.5`
- `sentence-transformers/all-MiniLM-L6-v2`
- `jinaai/jina-embeddings-v2-base-zh`
- `jinaai/jina-embeddings-v2-base-en`

## 🧪 测试

运行完整测试套件：

```bash
python test_siliconflow.py
```

测试包括：
- ✅ 基本连接测试
- ✅ 推理模型功能测试
- ✅ 嵌入模型功能测试
- ✅ 错误处理测试

## 📊 使用示例

查看完整的使用示例：

```bash
python example_usage.py
```

示例包括：
- 统一客户端使用
- 独立客户端使用
- 高级参数配置
- 文本相似度计算
- 错误处理演示

## ⚙️ 环境变量配置

支持的环境变量：

```bash
# 必需
export SILICONFLOW_API_KEY="your-api-key"

# 可选
export SILICONFLOW_BASE_URL="https://api.siliconflow.cn/v1"
export SILICONFLOW_CHAT_MODEL="Qwen/Qwen2.5-7B-Instruct"
export SILICONFLOW_EMBEDDING_MODEL="BAAI/bge-m3"
```

## 🛡️ 错误处理

客户端提供完善的错误处理：

- **自动重试** - 网络错误和速率限制自动重试
- **指数退避** - 重试间隔逐渐增加
- **详细日志** - 记录请求和错误详情
- **优雅降级** - 失败时返回 None 而不是抛出异常

## 📝 日志配置

```python
import logging

# 设置日志级别
logging.getLogger("siliconflow_client").setLevel(logging.DEBUG)

# 查看详细请求信息
client = SiliconFlowClient(api_key="your-api-key")
response = client.chat.simple_chat("测试")  # 会输出详细日志
```

## 🤝 兼容性

- **Python 3.7+**
- **兼容 OpenAI 接口设计**
- **支持异步调用**（计划中）
- **支持流式输出**（计划中）

## 📄 许可证

本项目基于原始测试代码开发，遵循相同的许可证条款。

## 🆘 支持

如果遇到问题：

1. 检查 API 密钥是否正确设置
2. 运行测试套件诊断问题
3. 查看日志输出获取详细错误信息
4. 参考示例代码确认使用方法

---

**注意**: 请确保您的 SiliconFlow API 密钥有效，并且账户有足够的配额来运行测试和示例。
