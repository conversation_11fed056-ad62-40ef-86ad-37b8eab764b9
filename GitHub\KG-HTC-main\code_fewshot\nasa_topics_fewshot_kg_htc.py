import pandas as pd
from pathlib import Path
from tqdm import tqdm
import sys
import json
import time
import random

# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

from src.llm import LLM
from src.graph_db import GraphDB
from src.pipeline import Pi<PERSON>ine

def create_enhanced_few_shot_examples(df, shots_per_class=3, level='Cat2', random_state=42):
    """
    创建增强的少样本示例，包含层次化信息
    
    Args:
        df: 数据集
        shots_per_class: 每个类别的示例数
        level: 分类级别
        random_state: 随机种子
    """
    random.seed(random_state)
    
    examples = {}
    unique_classes = df[level].unique()
    
    for class_name in unique_classes:
        class_samples = df[df[level] == class_name]
        
        n_samples = min(shots_per_class, len(class_samples))
        if n_samples > 0:
            sampled = class_samples.sample(n=n_samples, random_state=random_state)
            
            # 包含完整的层次化信息
            examples[class_name] = []
            for _, row in sampled.iterrows():
                example = {
                    'Text': row['Text'],
                    'Cat1': row['Cat1'],
                    'Cat2': row['Cat2'],
                    'Cat3': row['Cat3'],
                    'hierarchy_path': f"{row['Cat1']} -> {row['Cat2']} -> {row['Cat3']}"
                }
                examples[class_name].append(example)
    
    return examples

def build_kg_enhanced_prompt(query_text, examples_dict, candidates, sub_graph, level='Cat2'):
    """
    构建知识图谱增强的少样本提示
    
    Args:
        query_text: 查询文本
        examples_dict: 示例字典
        candidates: 候选类别
        sub_graph: 相关子图
        level: 分类级别
    """
    
    # 构建示例部分
    examples_text = []
    for class_name, examples in examples_dict.items():
        if class_name.lower() in [c.lower() for c in candidates]:  # 只包含候选类别的示例
            for example in examples:
                text_snippet = example['Text'][:200] + "..." if len(example['Text']) > 200 else example['Text']
                
                if level == 'Cat1':
                    examples_text.append(f"Text: {text_snippet}\nCategory: {example['Cat1']}")
                elif level == 'Cat2':
                    examples_text.append(f"Text: {text_snippet}\nHierarchy: {example['Cat1']} -> {example['Cat2']}\nCategory: {example['Cat2']}")
                else:  # Cat3
                    examples_text.append(f"Text: {text_snippet}\nHierarchy: {example['hierarchy_path']}\nCategory: {example['Cat3']}")
    
    # 随机打乱并限制示例数量
    random.shuffle(examples_text)
    max_examples = 8
    if len(examples_text) > max_examples:
        examples_text = examples_text[:max_examples]
    
    examples_str = "\n\n".join(examples_text)
    
    # 构建知识图谱信息
    kg_info = "\n".join(sub_graph[:10]) if sub_graph else "No specific knowledge graph paths available."
    
    # 构建完整提示
    if level == 'Cat1':
        prompt = f"""You are an expert in classifying NASA Earth science datasets. Here are some examples with their categories:

{examples_str}

Knowledge Graph Context:
{kg_info}

Now classify the following dataset into one of these categories: {', '.join(candidates)}

Text: {query_text}
Category:"""
    elif level == 'Cat2':
        prompt = f"""You are an expert in classifying NASA Earth science datasets. Here are some examples with their hierarchical categories:

{examples_str}

Knowledge Graph Context:
{kg_info}

Now classify the following dataset into one of these topic categories: {', '.join(candidates)}

Text: {query_text}
Category:"""
    else:  # Cat3
        prompt = f"""You are an expert in classifying NASA Earth science datasets. Here are some examples with their complete hierarchical paths:

{examples_str}

Knowledge Graph Context:
{kg_info}

Now classify the following dataset into one of these specific terms: {', '.join(candidates)}

Text: {query_text}
Category:"""
    
    return prompt

def run_fewshot_kg_htc_experiment():
    """
    运行少样本KG-HTC增强实验
    """
    
    print("=== NASA Topics少样本KG-HTC增强实验 ===")
    
    # 读取数据
    train_path = "dataset/nasa_topics/nasa_topics_train.csv"
    test_path = "dataset/nasa_topics/nasa_topics_val.csv"
    
    try:
        train_df = pd.read_csv(train_path)
        test_df = pd.read_csv(test_path)
        print(f"训练集: {len(train_df)} 条记录")
        print(f"测试集: {len(test_df)} 条记录")
    except Exception as e:
        print(f"读取数据失败: {e}")
        return
    
    # 数据预处理
    def preprocess_data(df):
        df = df.dropna(subset=['Text', 'Cat1', 'Cat2', 'Cat3'])
        df = df[df['Cat1'] != "unknown"]
        df = df[df['Cat2'] != "unknown"]
        df = df[df['Cat3'] != "unknown"]
        return df
    
    train_df = preprocess_data(train_df)
    test_df = preprocess_data(test_df)
    
    # 限制测试数据量
    if len(test_df) > 80:
        test_df = test_df.sample(n=80, random_state=42)
    
    print(f"实验数据 - 训练集: {len(train_df)}, 测试集: {len(test_df)}")
    
    # 初始化组件
    try:
        llm = LLM()
        graph_db = GraphDB()
        
        # 配置Pipeline
        config = {
            "data_name": "nasa_topics_fewshot",
            "vectdb_path": "database/nasa_topics",
            "query_params": {
                "l2_top_k": 12,
                "l3_top_k": 35
            }
        }
        pipeline = Pipeline(llm, config)
        
        print("✓ 所有组件初始化成功")
    except Exception as e:
        print(f"✗ 组件初始化失败: {e}")
        return
    
    # 少样本配置
    few_shot_configs = [
        {"shots_per_class": 2, "name": "2-shot"},
        {"shots_per_class": 3, "name": "3-shot"},
        {"shots_per_class": 5, "name": "5-shot"}
    ]
    
    all_results = {}
    
    for config_item in few_shot_configs:
        print(f"\n=== {config_item['name']} KG-HTC增强实验 ===")
        
        try:
            # 创建增强示例
            examples_l1 = create_enhanced_few_shot_examples(train_df, config_item["shots_per_class"], 'Cat1')
            examples_l2 = create_enhanced_few_shot_examples(train_df, config_item["shots_per_class"], 'Cat2')
            examples_l3 = create_enhanced_few_shot_examples(train_df, config_item["shots_per_class"], 'Cat3')
            
            print(f"创建示例 - L1: {len(examples_l1)}, L2: {len(examples_l2)}, L3: {len(examples_l3)}")
            
            # 获取候选类别
            all_l1 = train_df['Cat1'].unique().tolist()
            
            # 开始实验
            results = []
            success_count = 0
            error_count = 0
            total_time = 0
            
            for idx, row in tqdm(test_df.iterrows(), total=len(test_df), desc=f"{config_item['name']}分类"):
                try:
                    start_time = time.time()
                    
                    # 构建查询文本
                    title = row.get('Title', '').strip()
                    text = row.get('Text', '').strip()
                    query_text = f"Title: {title}\nDescription: {text[:1500]}"
                    
                    # 1. 向量检索相关节点
                    retrieved_nodes = pipeline.query_related_nodes(query_text)
                    
                    # 2. 构建子图
                    sub_graph = pipeline.build_linked_labels(
                        retrieved_nodes.get("l3", []), 
                        retrieved_nodes.get("l2", [])
                    )
                    
                    # 3. L1级别预测（使用增强提示）
                    l1_prompt = build_kg_enhanced_prompt(
                        query_text, examples_l1, all_l1, sub_graph, 'Cat1'
                    )
                    pred_l1 = llm.generate(l1_prompt, max_tokens=50, temperature=0.3)
                    pred_l1 = pred_l1.strip().lower()
                    
                    # 4. L2级别预测
                    try:
                        child_l1 = graph_db.query_l2_from_l1(pred_l1)
                        potential_l2 = list(set(child_l1 + retrieved_nodes.get("l2", [])))
                    except:
                        potential_l2 = retrieved_nodes.get("l2", [])
                    
                    l2_prompt = build_kg_enhanced_prompt(
                        query_text, examples_l2, potential_l2[:12], sub_graph, 'Cat2'
                    )
                    pred_l2 = llm.generate(l2_prompt, max_tokens=50, temperature=0.3)
                    pred_l2 = pred_l2.strip().lower()
                    
                    # 5. L3级别预测
                    try:
                        child_l2 = graph_db.query_l3_from_l2(pred_l2)
                        potential_l3 = list(set(child_l2 + retrieved_nodes.get("l3", [])))
                    except:
                        potential_l3 = retrieved_nodes.get("l3", [])
                    
                    l3_prompt = build_kg_enhanced_prompt(
                        query_text, examples_l3, potential_l3[:15], sub_graph, 'Cat3'
                    )
                    pred_l3 = llm.generate(l3_prompt, max_tokens=50, temperature=0.3)
                    pred_l3 = pred_l3.strip().lower()
                    
                    inference_time = time.time() - start_time
                    total_time += inference_time
                    
                    # 保存结果
                    result = {
                        'Title': row['Title'],
                        'Text': row['Text'],
                        'Cat1': row['Cat1'],
                        'Cat2': row['Cat2'],
                        'Cat3': row['Cat3'],
                        f'fewshot_kg_htc_{config_item["shots_per_class"]}_pred_l1': pred_l1,
                        f'fewshot_kg_htc_{config_item["shots_per_class"]}_pred_l2': pred_l2,
                        f'fewshot_kg_htc_{config_item["shots_per_class"]}_pred_l3': pred_l3,
                        'method': f'fewshot_kg_htc_{config_item["shots_per_class"]}',
                        'inference_time': inference_time,
                        'shots_per_class': config_item["shots_per_class"],
                        'retrieved_l2': retrieved_nodes.get("l2", [])[:5],
                        'retrieved_l3': retrieved_nodes.get("l3", [])[:5],
                        'subgraph_size': len(sub_graph)
                    }
                    
                    results.append(result)
                    success_count += 1
                    
                except Exception as e:
                    error_count += 1
                    print(f"\n处理第 {idx+1} 条记录时出错: {e}")
                    
                    result = {
                        'Title': row['Title'],
                        'Text': row['Text'],
                        'Cat1': row['Cat1'],
                        'Cat2': row['Cat2'],
                        'Cat3': row['Cat3'],
                        f'fewshot_kg_htc_{config_item["shots_per_class"]}_pred_l1': "error",
                        f'fewshot_kg_htc_{config_item["shots_per_class"]}_pred_l2': "error",
                        f'fewshot_kg_htc_{config_item["shots_per_class"]}_pred_l3': "error",
                        'method': f'fewshot_kg_htc_{config_item["shots_per_class"]}',
                        'error_message': str(e),
                        'shots_per_class': config_item["shots_per_class"]
                    }
                    results.append(result)
                
                # API限制延迟
                time.sleep(0.3)
            
            # 计算性能指标
            if success_count > 0:
                valid_results = [r for r in results if r[f'fewshot_kg_htc_{config_item["shots_per_class"]}_pred_l1'] != 'error']
                
                if valid_results:
                    # 计算准确率
                    correct_l1 = sum(1 for r in valid_results 
                                   if r[f'fewshot_kg_htc_{config_item["shots_per_class"]}_pred_l1'].lower() == r['Cat1'].lower())
                    correct_l2 = sum(1 for r in valid_results 
                                   if r[f'fewshot_kg_htc_{config_item["shots_per_class"]}_pred_l2'].lower() == r['Cat2'].lower())
                    correct_l3 = sum(1 for r in valid_results 
                                   if r[f'fewshot_kg_htc_{config_item["shots_per_class"]}_pred_l3'].lower() == r['Cat3'].lower())
                    
                    acc_l1 = correct_l1 / len(valid_results)
                    acc_l2 = correct_l2 / len(valid_results)
                    acc_l3 = correct_l3 / len(valid_results)
                    
                    # 层次化准确率
                    hierarchical_correct = sum(1 for r in valid_results 
                                             if (r[f'fewshot_kg_htc_{config_item["shots_per_class"]}_pred_l1'].lower() == r['Cat1'].lower() and
                                                 r[f'fewshot_kg_htc_{config_item["shots_per_class"]}_pred_l2'].lower() == r['Cat2'].lower() and
                                                 r[f'fewshot_kg_htc_{config_item["shots_per_class"]}_pred_l3'].lower() == r['Cat3'].lower()))
                    hierarchical_acc = hierarchical_correct / len(valid_results)
                    
                    avg_time = total_time / len(valid_results)
                    
                    print(f"\n{config_item['name']} KG-HTC增强结果:")
                    print(f"  成功率: {success_count/len(results)*100:.2f}%")
                    print(f"  L1准确率: {acc_l1:.4f} ({correct_l1}/{len(valid_results)})")
                    print(f"  L2准确率: {acc_l2:.4f} ({correct_l2}/{len(valid_results)})")
                    print(f"  L3准确率: {acc_l3:.4f} ({correct_l3}/{len(valid_results)})")
                    print(f"  层次化准确率: {hierarchical_acc:.4f} ({hierarchical_correct}/{len(valid_results)})")
                    print(f"  平均推理时间: {avg_time:.2f}秒")
                    
                    # 保存配置结果
                    all_results[config_item['name']] = {
                        'accuracy': {
                            'L1': acc_l1,
                            'L2': acc_l2,
                            'L3': acc_l3,
                            'Hierarchical': hierarchical_acc
                        },
                        'statistics': {
                            'total_samples': len(results),
                            'valid_samples': len(valid_results),
                            'success_rate': success_count / len(results),
                            'average_inference_time': avg_time
                        },
                        'shots_per_class': config_item["shots_per_class"]
                    }
            
            # 保存详细结果
            config_output_path = f"dataset/nasa_topics/fewshot_kg_htc_{config_item['shots_per_class']}_results.json"
            with open(config_output_path, "w", encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            print(f"✗ {config_item['name']} 实验失败: {e}")
    
    # 保存汇总结果
    if all_results:
        summary_path = "dataset/nasa_topics/fewshot_kg_htc_summary.json"
        with open(summary_path, "w", encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n=== 少样本KG-HTC增强性能对比 ===")
        for config_name, metrics in all_results.items():
            print(f"\n{config_name}:")
            print(f"  L1: {metrics['accuracy']['L1']:.4f}")
            print(f"  L2: {metrics['accuracy']['L2']:.4f}")
            print(f"  L3: {metrics['accuracy']['L3']:.4f}")
            print(f"  层次化: {metrics['accuracy']['Hierarchical']:.4f}")
            print(f"  推理时间: {metrics['statistics']['average_inference_time']:.2f}秒")
        
        print(f"\n✓ 少样本KG-HTC增强结果已保存: {summary_path}")

if __name__ == "__main__":
    run_fewshot_kg_htc_experiment()
