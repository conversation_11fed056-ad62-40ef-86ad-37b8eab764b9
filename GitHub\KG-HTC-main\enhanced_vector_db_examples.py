#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版 QdrantVectorDB 类使用示例

本文件展示了如何使用新的 EnhancedQdrantVectorDB 类的各种功能：
1. 基本初始化和配置
2. CSV数据加载和处理（支持层级标签）
3. 文本向量化和查询
4. 数据持久化和备份
5. 高级查询和分析功能
6. 与现有代码的集成

特性：
- 内存模式运行，支持本地持久化存储
- 动态payload结构，根据CSV文件列名自动构建
- 支持三级分类结构（Cat1, Cat2, Cat3）
- 增强的查询功能和结果分析
- 完整的CRUD操作
- 向后兼容现有接口

作者：AI Assistant
日期：2025-01-28
版本：2.0
"""

import os
import sys
import pandas as pd
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.enhanced_qdrant_vector_db import EnhancedQdrantVectorDB

def example_1_basic_usage():
    """
    示例1：基本使用方法
    """
    print("=== 示例1：增强版基本使用方法 ===")
    
    # 初始化增强版向量数据库
    vector_db = EnhancedQdrantVectorDB(
        collection_name="enhanced_example_basic",
        use_memory=True,
        storage_path="./enhanced_vector_storage",
        text_field="text",
        auto_persist=True,
        embedding_config={
            "model_name": "text-embedding-ada-002",  # 使用OpenAI标准嵌入模型
            "batch_size": 50,
            "max_retries": 3
        }
    )
    
    # 添加单个文本
    result = vector_db.add_text(
        text="人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
        metadata={
            "category": "technology",
            "subcategory": "artificial_intelligence",
            "language": "chinese",
            "level": "Category1"
        }
    )
    print(f"添加文本结果: {result}")
    
    # 查询相似文本（使用新的增强接口）
    query_result = vector_db.query(
        query_text="机器学习和深度学习",
        n_results=5,
        score_threshold=0.5  # 设置相似度阈值
    )
    
    print(f"查询结果数量: {len(query_result)}")
    for i, (doc, metadata, distance, score) in enumerate(zip(
        query_result.documents,
        query_result.metadatas, 
        query_result.distances,
        query_result.scores
    )):
        print(f"  {i+1}. 相似度: {score:.3f} (距离: {distance:.3f})")
        print(f"     文本: {doc[:50]}...")
        print(f"     元数据: {metadata}")
    
    # 获取详细统计信息
    stats = vector_db.get_statistics()
    print(f"数据库统计: {stats}")

def example_2_csv_loading_with_hierarchy():
    """
    示例2：从CSV文件加载数据（支持层级标签）
    """
    print("\n=== 示例2：从CSV文件加载数据（支持层级标签） ===")
    
    # 创建示例CSV数据（包含三级分类）
    sample_data = {
        'Title': [
            'NASA火星探测任务',
            '深度学习在医疗诊断中的应用',
            '气候变化对生态系统的影响',
            '量子计算的最新进展',
            '可再生能源技术发展',
            '自然语言处理技术突破',
            '海洋生物多样性研究',
            '区块链在金融领域的应用'
        ],
        'Text': [
            'NASA的火星探测任务旨在寻找火星上生命存在的证据，通过先进的探测器和科学仪器收集数据。',
            '深度学习技术在医疗影像诊断中展现出巨大潜力，能够帮助医生更准确地识别疾病。',
            '全球气候变化正在对各种生态系统产生深远影响，包括物种迁移和栖息地变化。',
            '量子计算技术的发展为解决复杂计算问题提供了新的可能性，在密码学和优化领域有重要应用。',
            '太阳能、风能等可再生能源技术的快速发展为实现碳中和目标提供了重要支撑。',
            '自然语言处理技术在机器翻译、文本生成和语义理解方面取得了重大突破。',
            '海洋生物多样性研究揭示了深海生态系统的复杂性和脆弱性。',
            '区块链技术在金融服务、供应链管理和数字身份验证方面展现出巨大潜力。'
        ],
        'Cat1': ['earth_science', 'computer_science', 'earth_science', 'computer_science', 
                'earth_science', 'computer_science', 'life_science', 'computer_science'],
        'Cat2': ['space_science', 'artificial_intelligence', 'climate_science', 'quantum_computing', 
                'energy', 'natural_language_processing', 'marine_biology', 'blockchain'],
        'Cat3': ['mars_exploration', 'medical_ai', 'ecology', 'quantum_algorithms', 
                'renewable_energy', 'text_processing', 'biodiversity', 'fintech']
    }
    
    # 保存为CSV文件
    df = pd.DataFrame(sample_data)
    csv_path = "enhanced_example_data.csv"
    df.to_csv(csv_path, index=False, encoding='utf-8')
    print(f"创建示例CSV文件: {csv_path}")
    
    # 初始化向量数据库
    vector_db = EnhancedQdrantVectorDB(
        collection_name="enhanced_example_csv",
        use_memory=True,
        auto_persist=True
    )
    
    # 加载CSV数据（支持层级标签）
    load_result = vector_db.load_csv_data(
        csv_path=csv_path,
        text_column="Text",
        encoding='utf-8',
        hierarchical_columns={
            "Category1": "Cat1",
            "Category2": "Cat2", 
            "Category3": "Cat3"
        }
    )
    
    print(f"CSV加载结果: {load_result}")
    
    # 查询不同层级的数据
    print("\n查询不同层级的数据:")
    
    # 查询一级分类
    l1_result = vector_db.query_hierarchical("人工智能和机器学习", "Category1", n_results=3)
    print(f"一级分类查询结果: {len(l1_result)} 个")
    
    # 查询二级分类
    l2_result = vector_db.query_hierarchical("深度学习算法", "Category2", n_results=3)
    print(f"二级分类查询结果: {len(l2_result)} 个")
    
    # 查询三级分类
    l3_result = vector_db.query_hierarchical("医疗诊断系统", "Category3", n_results=3)
    print(f"三级分类查询结果: {len(l3_result)} 个")
    
    # 使用复杂过滤条件查询
    complex_result = vector_db.query(
        query_text="科学研究和技术发展",
        n_results=5,
        where={"Cat1": "computer_science"},
        score_threshold=0.3
    )
    print(f"复杂过滤查询结果: {len(complex_result)} 个")
    
    # 清理临时文件
    if os.path.exists(csv_path):
        os.remove(csv_path)

def example_3_advanced_features():
    """
    示例3：高级功能演示
    """
    print("\n=== 示例3：高级功能演示 ===")
    
    # 初始化向量数据库
    vector_db = EnhancedQdrantVectorDB(
        collection_name="enhanced_advanced",
        use_memory=True,
        auto_persist=True
    )
    
    # 批量添加数据
    texts = [
        "深度学习是机器学习的一个子领域，使用多层神经网络来学习数据的表示。",
        "自然语言处理是人工智能的一个分支，专注于计算机与人类语言之间的交互。",
        "计算机视觉是一个跨学科领域，研究如何使计算机获得对数字图像或视频的高层次理解。",
        "强化学习是机器学习的一个领域，关注智能体如何在环境中采取行动以最大化累积奖励。"
    ]
    
    metadatas = [
        {"topic": "deep_learning", "level": "Category2", "difficulty": "intermediate"},
        {"topic": "nlp", "level": "Category2", "difficulty": "beginner"},
        {"topic": "computer_vision", "level": "Category2", "difficulty": "advanced"},
        {"topic": "reinforcement_learning", "level": "Category2", "difficulty": "advanced"}
    ]
    
    batch_result = vector_db.batch_add_texts(texts, metadatas)
    print(f"批量添加结果: {batch_result}")
    
    # 文档更新功能
    if batch_result.get('success') and batch_result.get('point_ids'):
        first_id = batch_result['point_ids'][0]
        update_success = vector_db.update_document(
            point_id=first_id,
            metadata={"difficulty": "expert", "updated": True}
        )
        print(f"文档更新结果: {update_success}")
    
    # 相似文档搜索
    if batch_result.get('success') and batch_result.get('point_ids'):
        similar_docs = vector_db.search_similar_documents(
            document_id=batch_result['point_ids'][0],
            n_results=3,
            exclude_self=True
        )
        print(f"相似文档搜索结果: {len(similar_docs)} 个")
        for i, (doc, score) in enumerate(zip(similar_docs.documents, similar_docs.scores)):
            print(f"  {i+1}. 相似度: {score:.3f}")
            print(f"     文本: {doc[:50]}...")
    
    # 数据备份
    backup_path = vector_db.backup_data()
    print(f"数据备份路径: {backup_path}")
    
    # 导出到CSV
    export_path = vector_db.export_to_csv()
    print(f"数据导出路径: {export_path}")
    
    # 获取详细统计信息
    detailed_stats = vector_db.get_statistics()
    print(f"详细统计信息: {detailed_stats}")

def example_4_backward_compatibility():
    """
    示例4：向后兼容性测试
    """
    print("\n=== 示例4：向后兼容性测试 ===")
    
    # 使用旧的接口
    vector_db = EnhancedQdrantVectorDB(
        collection_name="enhanced_compat",
        use_memory=True
    )
    
    # 使用旧的batch_add接口
    titles = [
        "深度学习基础",
        "自然语言处理",
        "计算机视觉"
    ]
    
    texts = [
        "深度学习是机器学习的一个子领域，使用多层神经网络来学习数据的表示。",
        "自然语言处理是人工智能的一个分支，专注于计算机与人类语言之间的交互。",
        "计算机视觉是一个跨学科领域，研究如何使计算机获得对数字图像或视频的高层次理解。"
    ]
    
    metadatas = [
        {"level": "Category1", "topic": "machine_learning"},
        {"level": "Category2", "topic": "nlp"},
        {"level": "Category3", "topic": "computer_vision"}
    ]
    
    # 批量添加数据
    vector_db.batch_add(titles, texts, metadatas)
    print("使用旧接口批量添加数据完成")
    
    # 使用旧的查询接口
    print("\n使用旧的查询接口:")
    
    # 分级查询
    l1_result = vector_db.query_l1("机器学习")
    l2_result = vector_db.query_l2("自然语言")
    l3_result = vector_db.query_l3("图像识别")
    
    print(f"L1查询结果: {len(l1_result['documents'][0])} 个")
    print(f"L2查询结果: {len(l2_result['documents'][0])} 个")
    print(f"L3查询结果: {len(l3_result['documents'][0])} 个")
    
    # 查询所有级别
    all_result = vector_db.query_all_levels("人工智能")
    print(f"所有级别查询结果: {len(all_result['documents'][0])} 个")

def main():
    """
    运行所有示例
    """
    print("🚀 增强版 QdrantVectorDB 使用示例")
    print("=" * 60)
    
    try:
        example_1_basic_usage()
        example_2_csv_loading_with_hierarchy()
        example_3_advanced_features()
        example_4_backward_compatibility()
        
        print("\n🎉 所有示例运行完成！")
        print("\n📊 增强版功能总结:")
        print("✓ 支持OpenAI标准嵌入模型")
        print("✓ 三级分类层级结构")
        print("✓ 增强的查询和过滤功能")
        print("✓ 完整的CRUD操作")
        print("✓ 数据备份和恢复")
        print("✓ 向后兼容现有接口")
        print("✓ 详细的统计和分析功能")
        
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
