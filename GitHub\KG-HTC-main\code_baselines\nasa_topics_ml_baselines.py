import pandas as pd
import numpy as np
from pathlib import Path
import sys
import json
import pickle
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.multioutput import MultiOutputClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.naive_bayes import MultinomialNB
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, f1_score, classification_report
from sklearn.preprocessing import LabelEncoder
import time

# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

def run_ml_baselines():
    """
    运行传统机器学习基线方法
    """
    
    print("=== NASA Topics传统机器学习基线实验 ===")
    
    # 读取数据
    train_path = "dataset/nasa_topics/nasa_topics_train.csv"
    test_path = "dataset/nasa_topics/nasa_topics_val.csv"
    
    try:
        train_df = pd.read_csv(train_path)
        test_df = pd.read_csv(test_path)
        print(f"训练集: {len(train_df)} 条记录")
        print(f"测试集: {len(test_df)} 条记录")
    except Exception as e:
        print(f"读取数据失败: {e}")
        return
    
    # 数据预处理
    def preprocess_data(df):
        df = df.dropna(subset=['Text', 'Cat1', 'Cat2', 'Cat3'])
        df = df[df['Cat1'] != "unknown"]
        df = df[df['Cat2'] != "unknown"]
        df = df[df['Cat3'] != "unknown"]
        return df
    
    train_df = preprocess_data(train_df)
    test_df = preprocess_data(test_df)
    
    # 限制数据量进行快速实验
    if len(train_df) > 2000:
        train_df = train_df.sample(n=2000, random_state=42)
    if len(test_df) > 500:
        test_df = test_df.sample(n=500, random_state=42)
    
    print(f"实验数据 - 训练集: {len(train_df)}, 测试集: {len(test_df)}")
    
    # 文本特征提取
    print("\n=== 文本特征提取 ===")
    vectorizer = TfidfVectorizer(
        max_features=5000,
        stop_words='english',
        ngram_range=(1, 2),
        min_df=2,
        max_df=0.95
    )
    
    X_train = vectorizer.fit_transform(train_df['Text'])
    X_test = vectorizer.transform(test_df['Text'])
    
    print(f"特征维度: {X_train.shape[1]}")
    
    # 标签编码
    le_l1 = LabelEncoder()
    le_l2 = LabelEncoder()
    le_l3 = LabelEncoder()
    
    y_train_l1 = le_l1.fit_transform(train_df['Cat1'])
    y_train_l2 = le_l2.fit_transform(train_df['Cat2'])
    y_train_l3 = le_l3.fit_transform(train_df['Cat3'])
    
    y_test_l1 = le_l1.transform(test_df['Cat1'])
    y_test_l2 = le_l2.transform(test_df['Cat2'])
    y_test_l3 = le_l3.transform(test_df['Cat3'])
    
    # 定义基线模型
    models = {
        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1),
        'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
        'Naive Bayes': MultinomialNB(),
        'SVM': SVC(random_state=42, probability=True)
    }
    
    results = {}
    
    # 训练和评估每个模型
    for model_name, model in models.items():
        print(f"\n=== 训练 {model_name} ===")
        start_time = time.time()
        
        try:
            # 分别训练三个分类器
            model_l1 = model.__class__(**model.get_params())
            model_l2 = model.__class__(**model.get_params())
            model_l3 = model.__class__(**model.get_params())
            
            # 训练
            model_l1.fit(X_train, y_train_l1)
            model_l2.fit(X_train, y_train_l2)
            model_l3.fit(X_train, y_train_l3)
            
            # 预测
            pred_l1 = model_l1.predict(X_test)
            pred_l2 = model_l2.predict(X_test)
            pred_l3 = model_l3.predict(X_test)
            
            # 计算指标
            acc_l1 = accuracy_score(y_test_l1, pred_l1)
            acc_l2 = accuracy_score(y_test_l2, pred_l2)
            acc_l3 = accuracy_score(y_test_l3, pred_l3)
            
            f1_l1 = f1_score(y_test_l1, pred_l1, average='macro', zero_division=0)
            f1_l2 = f1_score(y_test_l2, pred_l2, average='macro', zero_division=0)
            f1_l3 = f1_score(y_test_l3, pred_l3, average='macro', zero_division=0)
            
            # 层次化准确率
            hierarchical_acc = ((pred_l1 == y_test_l1) & 
                               (pred_l2 == y_test_l2) & 
                               (pred_l3 == y_test_l3)).mean()
            
            training_time = time.time() - start_time
            
            results[model_name] = {
                'accuracy': {
                    'L1': acc_l1,
                    'L2': acc_l2,
                    'L3': acc_l3,
                    'Hierarchical': hierarchical_acc
                },
                'f1_macro': {
                    'L1': f1_l1,
                    'L2': f1_l2,
                    'L3': f1_l3
                },
                'training_time': training_time
            }
            
            print(f"✓ {model_name} 训练完成")
            print(f"  L1准确率: {acc_l1:.4f}")
            print(f"  L2准确率: {acc_l2:.4f}")
            print(f"  L3准确率: {acc_l3:.4f}")
            print(f"  层次化准确率: {hierarchical_acc:.4f}")
            print(f"  训练时间: {training_time:.2f}秒")
            
            # 保存预测结果
            test_results = test_df.copy()
            test_results[f'{model_name}_pred_l1'] = le_l1.inverse_transform(pred_l1)
            test_results[f'{model_name}_pred_l2'] = le_l2.inverse_transform(pred_l2)
            test_results[f'{model_name}_pred_l3'] = le_l3.inverse_transform(pred_l3)
            test_results['method'] = model_name.lower().replace(' ', '_')
            
            # 保存到JSON
            output_path = f"dataset/nasa_topics/{model_name.lower().replace(' ', '_')}_results.json"
            test_results_dict = test_results.to_dict(orient='records')
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(test_results_dict, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            print(f"✗ {model_name} 训练失败: {e}")
            results[model_name] = {'error': str(e)}
    
    # 保存所有结果
    print(f"\n=== 基线方法性能对比 ===")
    comparison_results = {}
    
    for model_name, result in results.items():
        if 'error' not in result:
            print(f"\n{model_name}:")
            print(f"  L1: {result['accuracy']['L1']:.4f} (F1: {result['f1_macro']['L1']:.4f})")
            print(f"  L2: {result['accuracy']['L2']:.4f} (F1: {result['f1_macro']['L2']:.4f})")
            print(f"  L3: {result['accuracy']['L3']:.4f} (F1: {result['f1_macro']['L3']:.4f})")
            print(f"  层次化: {result['accuracy']['Hierarchical']:.4f}")
            print(f"  训练时间: {result['training_time']:.2f}秒")
            
            comparison_results[model_name] = result
    
    # 保存对比结果
    with open("dataset/nasa_topics/ml_baselines_comparison.json", 'w', encoding='utf-8') as f:
        json.dump(comparison_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n✓ 基线方法实验完成")
    print(f"结果已保存到: dataset/nasa_topics/ml_baselines_comparison.json")

if __name__ == "__main__":
    run_ml_baselines()
