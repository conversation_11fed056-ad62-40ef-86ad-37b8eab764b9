import pandas as pd
import os
from pathlib import Path

def analyze_4labels_datasets():
    """分析4-labels文件夹下的数据集，统计四层标签并生成架构"""
    
    # 设置文件夹路径
    data_folder = Path("4-labels")
    output_folder = Path("4-labels分析结果")
    output_folder.mkdir(exist_ok=True)
    
    # 获取所有数据文件
    data_files = list(data_folder.glob("*.csv")) + list(data_folder.glob("*.xlsx"))
    
    if not data_files:
        print("未找到数据文件，请检查4-labels文件夹")
        return
    
    all_datasets_stats = []
    all_hierarchy = []
    
    for file_path in data_files:
        print(f"\n正在处理: {file_path.name}")
        
        # 读取数据
        if file_path.suffix == '.csv':
            df = pd.read_csv(file_path, encoding='utf-8-sig')
        else:
            df = pd.read_excel(file_path)
        
        dataset_name = file_path.stem
        
        # 统计每层标签数据量
        cluster_columns = ['CLUSTER1', 'CLUSTER2', 'CLUSTER3', 'CLUSTER4']
        
        for cluster in cluster_columns:
            if cluster in df.columns:
                # 统计该层标签
                cluster_stats = df[cluster].value_counts().reset_index()
                cluster_stats.columns = ['标签值', '数据量']
                cluster_stats['数据集'] = dataset_name
                cluster_stats['标签层级'] = cluster
                
                all_datasets_stats.append(cluster_stats)
                
                # 构建层级架构
                for _, row in cluster_stats.iterrows():
                    all_hierarchy.append({
                        '数据集': dataset_name,
                        '标签层级': cluster,
                        '标签值': row['标签值'],
                        '数据量': row['数据量']
                    })
    
    # 合并所有统计结果
    if all_datasets_stats:
        combined_stats = pd.concat(all_datasets_stats, ignore_index=True)
        combined_stats.to_csv(output_folder / "所有数据集标签统计.csv", 
                            index=False, encoding='utf-8-sig')
    
    # 生成四层标签架构
    if all_hierarchy:
        hierarchy_df = pd.DataFrame(all_hierarchy)
        
        # 按数据集和层级排序
        hierarchy_df = hierarchy_df.sort_values(['数据集', '标签层级', '数据量'], 
                                              ascending=[True, True, False])
        
        # 保存完整架构
        hierarchy_df.to_csv(output_folder / "四层标签架构.csv", 
                          index=False, encoding='utf-8-sig')
        
        # 生成架构汇总
        summary = hierarchy_df.groupby(['数据集', '标签层级']).agg({
            '标签值': 'count',
            '数据量': 'sum'
        }).reset_index()
        summary.columns = ['数据集', '标签层级', '唯一标签数', '总数据量']
        
        summary.to_csv(output_folder / "标签架构汇总.csv", 
                      index=False, encoding='utf-8-sig')
        
        print(f"\n分析完成！结果保存在 {output_folder} 文件夹中")
        print(f"- 所有数据集标签统计.csv")
        print(f"- 四层标签架构.csv") 
        print(f"- 标签架构汇总.csv")
        
        return hierarchy_df, summary
    
    return None, None

# 执行分析
if __name__ == "__main__":
    hierarchy, summary = analyze_4labels_datasets()