# SiliconFlow 客户端包

基于原始测试代码封装的 SiliconFlow API 客户端包，提供推理模型和嵌入模型的调用功能，兼容 OpenAI 接口设计。

## 📦 包结构

```
siliconflow_client_package/
├── __init__.py                 # 包初始化文件
├── siliconflow_client.py       # 核心客户端模块
├── config.py                   # 配置管理模块
├── example_usage.py            # 详细使用示例
├── test_siliconflow.py         # 完整测试套件
├── quick_test_new_client.py    # 快速验证脚本
├── README_SiliconFlow.md       # 详细文档
├── 封装总结.md                # 项目总结
└── README.md                   # 本文档
```

## 🚀 快速开始

### 1. 设置 API 密钥

```bash
export SILICONFLOW_API_KEY="your-api-key-here"
```

### 2. 基本使用

```python
# 方式一：直接从包导入（推荐）
from siliconflow_client_package import SiliconFlowClient

client = SiliconFlowClient()
response = client.chat.simple_chat("你好")
print(response)

# 方式二：使用便捷函数
from siliconflow_client_package import create_client, quick_test

client = create_client(api_key="your-api-key")
test_results = quick_test(api_key="your-api-key")
```

### 3. 导入特定类

```python
from siliconflow_client_package import (
    SiliconFlowClient,
    SiliconFlowChatClient,
    SiliconFlowEmbeddingClient,
    SiliconFlowConfig
)

# 使用推理模型
chat_client = SiliconFlowChatClient(api_key="your-api-key")
response = chat_client.simple_chat("什么是人工智能？")

# 使用嵌入模型
embedding_client = SiliconFlowEmbeddingClient(api_key="your-api-key")
vector = embedding_client.get_embedding_vector("测试文本")

# 配置管理
config = SiliconFlowConfig()
config.set("api_key", "your-api-key")
```

## 🧪 运行测试

```bash
# 进入包目录
cd siliconflow_client_package

# 快速验证
python quick_test_new_client.py

# 完整测试
python test_siliconflow.py

# 查看使用示例
python example_usage.py
```

## 📖 详细文档

- **[完整使用文档](README_SiliconFlow.md)** - 详细的 API 使用指南
- **[项目总结](封装总结.md)** - 封装过程和特性说明

## 🎯 主要特性

- ✅ **兼容 OpenAI 接口** - 熟悉的使用方式
- ✅ **推理模型支持** - 多种 Qwen、LLaMA、DeepSeek 模型
- ✅ **嵌入模型支持** - BGE、Jina 等向量化模型
- ✅ **自动重试机制** - 提高调用成功率
- ✅ **配置管理** - 灵活的参数配置
- ✅ **批量处理** - 支持批量文本向量化
- ✅ **相似度计算** - 内置文本相似度功能
- ✅ **完整测试** - 全面的功能验证

## 💡 使用场景

### 推理模型应用
- 智能对话系统
- 文本生成和摘要
- 问答系统
- 代码生成

### 嵌入模型应用
- 文本相似度计算
- 语义搜索
- 文档聚类
- 推荐系统

## 🔧 配置选项

### 环境变量
```bash
export SILICONFLOW_API_KEY="your-api-key"
export SILICONFLOW_BASE_URL="https://api.siliconflow.cn/v1"
export SILICONFLOW_CHAT_MODEL="Qwen/Qwen2.5-7B-Instruct"
export SILICONFLOW_EMBEDDING_MODEL="BAAI/bge-m3"
```

### 代码配置
```python
from siliconflow_client_package import SiliconFlowConfig

config = SiliconFlowConfig()
config.set("api_key", "your-api-key")
config.set("default_chat_params.temperature", 0.8)
config.set("request_config.timeout", 60)

client = SiliconFlowClient(config=config)
```

## 📊 性能优化

- **连接池** - 复用 HTTP 连接
- **自动重试** - 指数退避策略
- **批量处理** - 减少 API 调用次数
- **配置缓存** - 避免重复配置加载

## 🛡️ 错误处理

```python
from siliconflow_client_package import SiliconFlowClient

client = SiliconFlowClient(api_key="your-api-key")

try:
    response = client.chat.simple_chat("测试")
    if response:
        print(f"成功: {response}")
    else:
        print("调用失败，请检查配置")
except Exception as e:
    print(f"异常: {e}")
```

## 🔄 版本信息

```python
from siliconflow_client_package import get_version, get_info

print(f"版本: {get_version()}")
print(f"包信息: {get_info()}")
```

## 📝 更新日志

### v1.0.0
- ✅ 初始版本发布
- ✅ 支持推理和嵌入模型调用
- ✅ 兼容 OpenAI 接口设计
- ✅ 完整的配置管理系统
- ✅ 自动重试和错误处理
- ✅ 详细的文档和示例

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个包。

## 📄 许可证

本项目基于原始测试代码开发，遵循相同的许可证条款。

---

**注意**: 请确保您的 SiliconFlow API 密钥有效，并且账户有足够的配额来运行测试和示例。
