#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的向量库测试函数

基于init_nasa.py，提供快速的向量数据库检索测试
"""

import sys
from pathlib import Path

# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

def simple_vector_retrieval_test():
    """
    简化的向量检索测试函数
    
    参考init_nasa.py的测试部分，快速验证向量数据库的检索功能
    """
    
    print("=== 简化向量检索测试 ===")
    
    try:
        from src.qdrant_vector_db import QdrantVectorDB
        
        # 初始化向量数据库 - 与init_nasa.py保持一致
        vector_db = QdrantVectorDB(
            collection_name="nasa",
            use_memory=True  # 使用内存模式
        )
        print("✓ 向量数据库连接成功")
        
    except Exception as e:
        print(f"✗ 向量数据库连接失败: {e}")
        print("请先运行 init_nasa.py 进行数据初始化")
        return False
    
    # 检查数据库状态 - 参考init_nasa.py的验证部分
    print("\n=== 检查数据库状态 ===")
    try:
        collection_info = vector_db.get_collection_info()
        title_count = collection_info['title_collection']['count']
        text_count = collection_info['text_collection']['count']
        print(f"向量数据库文档数量: title集合={title_count}, text集合={text_count}")
        
        if title_count == 0 and text_count == 0:
            print("⚠ 警告: 向量数据库中没有数据")
            return False
            
    except Exception as e:
        print(f"✗ 获取数据库状态失败: {e}")
        return False
    
    # 执行检索测试 - 使用与init_nasa.py相同的测试查询
    if title_count > 0 or text_count > 0:
        test_query = "artificial intelligence space technology"
        print(f"\n=== 测试向量检索: '{test_query}' ===")
        
        # 测试L2查询 - 参考init_nasa.py的测试代码
        print("\n🔍 L2级别查询:")
        try:
            l2_results = vector_db.query_l2(test_query, n_results=3)
            if l2_results and "documents" in l2_results and l2_results["documents"]:
                l2_docs = l2_results["documents"][0]
                l2_distances = l2_results.get("distances", [[]])[0]
                
                print(f"  ✓ L2查询成功，返回 {len(l2_docs)} 个结果")
                for i, doc in enumerate(l2_docs[:3]):
                    distance = l2_distances[i] if i < len(l2_distances) else "N/A"
                    print(f"    {i+1}. {doc} (距离: {distance})")
                    
                # 检查结果格式
                print(f"  结果格式检查:")
                print(f"    documents类型: {type(l2_results.get('documents'))}")
                print(f"    documents长度: {len(l2_results.get('documents', []))}")
                print(f"    第一个文档列表长度: {len(l2_results['documents'][0]) if l2_results.get('documents') else 0}")
                
            else:
                print("  ⚠ L2查询无结果")
                print(f"  返回结果结构: {l2_results}")
                
        except Exception as e:
            print(f"  ✗ L2查询失败: {e}")
            import traceback
            print(f"  详细错误: {traceback.format_exc()}")
        
        # 测试L3查询 - 参考init_nasa.py的测试代码
        print("\n🔍 L3级别查询:")
        try:
            l3_results = vector_db.query_l3(test_query, n_results=3)
            if l3_results and "documents" in l3_results and l3_results["documents"]:
                l3_docs = l3_results["documents"][0]
                l3_distances = l3_results.get("distances", [[]])[0]
                
                print(f"  ✓ L3查询成功，返回 {len(l3_docs)} 个结果")
                for i, doc in enumerate(l3_docs[:3]):
                    distance = l3_distances[i] if i < len(l3_distances) else "N/A"
                    print(f"    {i+1}. {doc} (距离: {distance})")
                    
                # 检查结果格式
                print(f"  结果格式检查:")
                print(f"    documents类型: {type(l3_results.get('documents'))}")
                print(f"    documents长度: {len(l3_results.get('documents', []))}")
                print(f"    第一个文档列表长度: {len(l3_results['documents'][0]) if l3_results.get('documents') else 0}")
                
            else:
                print("  ⚠ L3查询无结果")
                print(f"  返回结果结构: {l3_results}")
                
        except Exception as e:
            print(f"  ✗ L3查询失败: {e}")
            import traceback
            print(f"  详细错误: {traceback.format_exc()}")
    
    # 测试空查询和边界情况
    print(f"\n=== 测试边界情况 ===")
    
    # 测试空查询
    print("测试空查询:")
    try:
        empty_result = vector_db.query_l2("", n_results=3)
        print(f"  空查询结果: {empty_result}")
    except Exception as e:
        print(f"  空查询失败: {e}")
    
    # 测试不存在的查询
    print("测试不相关查询:")
    try:
        irrelevant_result = vector_db.query_l2("xyz123 nonexistent query", n_results=3)
        if irrelevant_result and "documents" in irrelevant_result:
            docs = irrelevant_result["documents"][0] if irrelevant_result["documents"] else []
            print(f"  不相关查询返回 {len(docs)} 个结果")
            if docs:
                print(f"  示例结果: {docs[:2]}")
        else:
            print(f"  不相关查询无结果")
    except Exception as e:
        print(f"  不相关查询失败: {e}")
    
    print(f"\n=== 测试总结 ===")
    print("✓ 向量数据库基本功能测试完成")
    return True

def test_pipeline_vector_integration():
    """
    测试Pipeline类与向量数据库的集成
    
    模拟gpt_nasa.py中的调用方式
    """
    print(f"\n=== 测试Pipeline向量集成 ===")
    
    try:
        from src.llm import LLM
        from src.pipeline import Pipeline
        
        # 配置参数 - 与gpt_nasa.py保持一致
        config = {
            "data_name": "nasa",
            "data_path": "dataset/nasa/nasa_val.csv",
            "output_path": "dataset/nasa/test_results.json",
            "vectdb_path": "database/nasa",
            "template": {
                "sys": "prompts/system/nasa/llm_graph.txt",
                "user": "prompts/user/nasa/llm_graph.txt"
            },
            "query_params": {
                "l2_top_k": 5,
                "l3_top_k": 5
            }
        }
        
        # 初始化Pipeline
        llm = LLM()
        pipeline = Pipeline(llm, config)
        print("✓ Pipeline初始化成功")
        
        # 测试query_related_nodes方法 - 这是gpt_nasa.py中出错的地方
        test_query = "Title: Artificial Intelligence for Earth Science\n"
        print(f"测试查询: {test_query.strip()}")
        
        retrieved_nodes = pipeline.query_related_nodes(test_query)
        print(f"✓ query_related_nodes调用成功")
        
        # 检查返回结果
        l2_nodes = retrieved_nodes.get("l2", [])
        l3_nodes = retrieved_nodes.get("l3", [])
        
        print(f"L2节点数量: {len(l2_nodes) if l2_nodes else 0}")
        print(f"L3节点数量: {len(l3_nodes) if l3_nodes else 0}")
        
        if l2_nodes:
            print(f"L2示例: {l2_nodes[:3]}")
        if l3_nodes:
            print(f"L3示例: {l3_nodes[:3]}")
        
        # 测试build_linked_labels方法
        print(f"\n测试build_linked_labels:")
        sub_graph = pipeline.build_linked_labels(l3_nodes or [], l2_nodes or [])
        print(f"子图大小: {len(sub_graph)}")
        if sub_graph:
            print(f"子图示例: {sub_graph[:2]}")
        
        print("✓ Pipeline向量集成测试成功")
        return True
        
    except Exception as e:
        print(f"✗ Pipeline向量集成测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("🔍 开始简化向量检索测试...\n")
    
    # 执行基本向量检索测试
    basic_test_ok = simple_vector_retrieval_test()
    
    # 执行Pipeline集成测试
    integration_test_ok = test_pipeline_vector_integration()
    
    print(f"\n=== 最终测试结果 ===")
    print(f"基本向量检索: {'✓ 通过' if basic_test_ok else '✗ 失败'}")
    print(f"Pipeline集成: {'✓ 通过' if integration_test_ok else '✗ 失败'}")
    
    if basic_test_ok and integration_test_ok:
        print(f"\n🎉 向量数据库功能正常，可以运行 gpt_nasa.py！")
    else:
        print(f"\n❌ 向量数据库存在问题，建议:")
        print("  1. 运行 init_nasa.py 重新初始化数据库")
        print("  2. 检查Neo4j数据库连接")
        print("  3. 验证OpenAI API配置")
