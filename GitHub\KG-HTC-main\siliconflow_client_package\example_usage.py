"""
SiliconFlow API 客户端使用示例
演示如何使用封装的推理模型和嵌入模型类
"""

from siliconflow_client import SiliconFlowClient, SiliconFlowChatClient, SiliconFlowEmbeddingClient


def main():
    # API 密钥配置 - 请替换为您的实际密钥
    API_KEY = ""  # 在这里填入您的 SiliconFlow API 密钥
    
    if not API_KEY:
        print("⚠️  请先在代码中设置您的 API_KEY")
        return
    
    print("🌟 SiliconFlow API 客户端使用示例")
    print("=" * 60)
    
    # 方式一：使用统一客户端（推荐）
    print("\n📦 方式一：使用统一客户端")
    print("-" * 40)
    
    # 初始化统一客户端
    client = SiliconFlowClient(api_key=API_KEY)
    
    # 测试连接
    connection_status = client.test_connection()
    
    if connection_status["chat"]:
        print("\n💬 推理模型调用示例:")
        
        # 单轮对话
        response = client.chat.simple_chat("请用中文介绍一下人工智能的发展历程")
        if response:
            print(f"🤖 模型回复: {response[:200]}...")
        
        # 多轮对话
        messages = [
            {"role": "user", "content": "你好，我想了解机器学习"},
            {"role": "assistant", "content": "你好！我很乐意为你介绍机器学习。机器学习是人工智能的一个重要分支..."},
            {"role": "user", "content": "能详细说说深度学习吗？"}
        ]
        
        result = client.chat.chat_completions(
            messages=messages,
            model="Qwen/Qwen2.5-7B-Instruct",
            temperature=0.8,
            max_tokens=500
        )
        
        if result and "choices" in result:
            print(f"🔄 多轮对话回复: {result['choices'][0]['message']['content'][:200]}...")
    
    if connection_status["embeddings"]:
        print("\n🔤 嵌入模型调用示例:")
        
        # 单个文本向量化
        text = "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。"
        vector = client.embeddings.get_embedding_vector(text)
        if vector:
            print(f"📊 文本向量维度: {len(vector)}")
            print(f"🔢 向量前5个值: {vector[:5]}")
        
        # 批量文本向量化
        texts = [
            "机器学习是人工智能的核心技术",
            "深度学习使用神经网络进行学习",
            "自然语言处理帮助计算机理解人类语言"
        ]
        
        batch_result = client.embeddings.create_embeddings(texts)
        if batch_result and "data" in batch_result:
            print(f"📚 批量处理文本数量: {len(batch_result['data'])}")
            for i, item in enumerate(batch_result['data']):
                print(f"   文本{i+1}向量维度: {len(item['embedding'])}")
    
    print("\n" + "=" * 60)
    
    # 方式二：使用独立客户端
    print("\n🔧 方式二：使用独立客户端")
    print("-" * 40)
    
    # 独立的推理客户端
    chat_client = SiliconFlowChatClient(api_key=API_KEY)
    
    print("\n💭 使用独立推理客户端:")
    simple_response = chat_client.simple_chat("什么是知识图谱？")
    if simple_response:
        print(f"🎯 简单回复: {simple_response[:150]}...")
    
    # 独立的嵌入客户端
    embedding_client = SiliconFlowEmbeddingClient(api_key=API_KEY)
    
    print("\n🎨 使用独立嵌入客户端:")
    knowledge_text = "知识图谱是一种结构化的语义知识库，用于描述概念及其相互关系。"
    knowledge_vector = embedding_client.get_embedding_vector(knowledge_text)
    if knowledge_vector:
        print(f"📈 知识图谱文本向量维度: {len(knowledge_vector)}")
    
    print("\n" + "=" * 60)
    
    # 高级使用示例
    print("\n🚀 高级使用示例")
    print("-" * 40)
    
    # 使用不同模型参数
    advanced_messages = [
        {"role": "system", "content": "你是一个专业的AI助手，请用简洁明了的方式回答问题。"},
        {"role": "user", "content": "请解释什么是Transformer架构"}
    ]
    
    advanced_result = client.chat.chat_completions(
        messages=advanced_messages,
        model="Qwen/Qwen2.5-7B-Instruct",
        temperature=0.3,  # 更确定性的输出
        top_p=0.9,
        max_tokens=300,
        frequency_penalty=0.1,
        presence_penalty=0.1
    )
    
    if advanced_result and "choices" in advanced_result:
        print(f"🎓 专业回答: {advanced_result['choices'][0]['message']['content'][:200]}...")
        
        # 显示使用统计
        if "usage" in advanced_result:
            usage = advanced_result["usage"]
            print(f"📊 Token使用统计:")
            print(f"   输入tokens: {usage.get('prompt_tokens', 'N/A')}")
            print(f"   输出tokens: {usage.get('completion_tokens', 'N/A')}")
            print(f"   总计tokens: {usage.get('total_tokens', 'N/A')}")
    
    # 文本相似度计算示例
    print("\n🔍 文本相似度计算示例:")
    
    def cosine_similarity(vec1, vec2):
        """计算两个向量的余弦相似度"""
        import math
        
        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        magnitude1 = math.sqrt(sum(a * a for a in vec1))
        magnitude2 = math.sqrt(sum(a * a for a in vec2))
        
        if magnitude1 == 0 or magnitude2 == 0:
            return 0
        
        return dot_product / (magnitude1 * magnitude2)
    
    text1 = "机器学习是人工智能的重要分支"
    text2 = "深度学习属于机器学习的范畴"
    text3 = "今天天气很好，适合出门散步"
    
    vec1 = client.embeddings.get_embedding_vector(text1)
    vec2 = client.embeddings.get_embedding_vector(text2)
    vec3 = client.embeddings.get_embedding_vector(text3)
    
    if vec1 and vec2 and vec3:
        sim_12 = cosine_similarity(vec1, vec2)
        sim_13 = cosine_similarity(vec1, vec3)
        
        print(f"📝 文本1: {text1}")
        print(f"📝 文本2: {text2}")
        print(f"📝 文本3: {text3}")
        print(f"🔗 文本1与文本2相似度: {sim_12:.4f}")
        print(f"🔗 文本1与文本3相似度: {sim_13:.4f}")
    
    print("\n✨ 示例运行完成！")


if __name__ == "__main__":
    main()
