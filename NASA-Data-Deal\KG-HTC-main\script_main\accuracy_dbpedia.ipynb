{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from sklearn.metrics import f1_score, accuracy_score\n", "from sklearn.metrics import classification_report\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "# Calculate confusion matrix for the most confused classes\n", "from sklearn.metrics import confusion_matrix\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>text</th>\n", "      <th>l1</th>\n", "      <th>l2</th>\n", "      <th>l3</th>\n", "      <th>gpt3_graph_l1</th>\n", "      <th>gpt3_graph_l2</th>\n", "      <th>gpt3_graph_l3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Li Curt is a station on the Bernina Railway li...</td>\n", "      <td>Place</td>\n", "      <td>Station</td>\n", "      <td>RailwayStation</td>\n", "      <td>place</td>\n", "      <td>Station</td>\n", "      <td>RailwayStation</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Grafton State Hospital was a psychiatric hospi...</td>\n", "      <td>Place</td>\n", "      <td>Building</td>\n", "      <td>Hospital</td>\n", "      <td>place</td>\n", "      <td>Building</td>\n", "      <td>Hospital</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>The Democratic Patriotic Alliance of Kurdistan...</td>\n", "      <td>Agent</td>\n", "      <td>Organisation</td>\n", "      <td>PoliticalParty</td>\n", "      <td>agent</td>\n", "      <td>Organisation</td>\n", "      <td>PoliticalParty</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON> (October 3, 1919 – March 4, 201...</td>\n", "      <td>Agent</td>\n", "      <td>Person</td>\n", "      <td>Architect</td>\n", "      <td>agent</td>\n", "      <td>Person</td>\n", "      <td>Architect</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Universitatea Reșița is a women handball club ...</td>\n", "      <td>Agent</td>\n", "      <td>SportsTeam</td>\n", "      <td>HandballTeam</td>\n", "      <td>agent</td>\n", "      <td>SportsTeam</td>\n", "      <td>HandballTeam</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                text     l1            l2  \\\n", "0  Li Curt is a station on the Bernina Railway li...  Place       Station   \n", "1  Grafton State Hospital was a psychiatric hospi...  Place      Building   \n", "2  The Democratic Patriotic Alliance of Kurdistan...  Agent  Organisation   \n", "3  <PERSON> (October 3, 1919 – March 4, 201...  Agent        Person   \n", "4  Universitatea Reșița is a women handball club ...  Agent    SportsTeam   \n", "\n", "               l3 gpt3_graph_l1 gpt3_graph_l2   gpt3_graph_l3  \n", "0  RailwayStation         place       Station  RailwayStation  \n", "1        Hospital         place      Building        Hospital  \n", "2  PoliticalParty         agent  Organisation  PoliticalParty  \n", "3       Architect         agent        Person       Architect  \n", "4    HandballTeam         agent    SportsTeam    HandballTeam  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# read the json file\n", "df = pd.read_json(\"../dataset/dbpedia/llm_graph_gpt3_l3.json\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.8837835419660213"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# change the l1\tas small letter\n", "df[\"l1\"] = df[\"l1\"].str.lower()\n", "df[\"gpt3_graph_l1\"] = df[\"gpt3_graph_l1\"].str.lower()\n", "# for each row, remove *, ', \" from gpt3_graph_l1\n", "df[\"gpt3_graph_l1\"] = df[\"gpt3_graph_l1\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "# f1-macro\n", "# if gpt3_graph_l1 is None, set it to a random label from l1\n", "df[\"gpt3_graph_l1\"] = df[\"gpt3_graph_l1\"].fillna(df[\"l1\"].sample(len(df)))\n", "f1_l1 = f1_score(df[\"gpt3_graph_l1\"], df[\"l1\"], average=\"macro\")\n", "f1_l1"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                precision    recall  f1-score  support\n", "device           0.339450  1.000000  0.506849     37.0\n", "topicalconcept   0.630952  0.905983  0.743860    117.0\n", "unitofwork       0.786145  0.992395  0.877311    263.0\n", "sportsseason     0.907388  0.992036  0.947826    879.0\n", "event            0.924934  0.980028  0.951684   2854.0\n", "work             0.982410  0.960204  0.971180   3141.0\n", "place            0.969275  0.975638  0.972446   6855.0\n", "agent            0.994349  0.972006  0.983051  18647.0\n", "species          0.999689  1.000000  0.999844   3210.0\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["report = classification_report(df[\"l1\"], df[\"gpt3_graph_l1\"], output_dict=True)\n", "\n", "# Convert to DataFrame for easier manipulation\n", "report_df = pd.DataFrame(report).transpose()\n", "report_df = report_df.drop(['accuracy', 'macro avg', 'weighted avg'])\n", "\n", "# Display the report\n", "print(report_df.sort_values(by='f1-score'))\n", "\n", "# Plot F1 scores by class\n", "plt.figure()\n", "sns.barplot(x=report_df.index, y=report_df['f1-score'])\n", "plt.xticks(rotation=90)\n", "plt.title('F1 Score of level 1')\n", "plt.xlabel('Category')\n", "plt.y<PERSON><PERSON>('F1 Score')\n", "plt.tight_layout()\n", "plt.savefig('dbpedia_level1.pdf')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.797014918843122, 0.8409299225064578)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# change the l2\tas small letter\n", "df[\"l2\"] = df[\"l2\"].str.lower()\n", "df[\"gpt3_graph_l2\"] = df[\"gpt3_graph_l2\"].str.lower()\n", "# for each row, remove *, ', \" from gpt3_graph_l2\n", "df[\"gpt3_graph_l2\"] = df[\"gpt3_graph_l2\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "# if gpt3_graph_l2 is not in l2, set it to a random label from l2\n", "df[\"gpt3_graph_l2\"] = df[\"gpt3_graph_l2\"].apply(lambda x: x if x in df[\"l2\"].values else df[\"l2\"].sample(1).values[0])\n", "f1_l2 = f1_score(df[\"gpt3_graph_l2\"], df[\"l2\"], average=\"macro\")\n", "acc_l2 = accuracy_score(df[\"gpt3_graph_l2\"], df[\"l2\"])\n", "f1_l2, acc_l2"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                         precision    recall  f1-score  support\n", "organisationmember        0.000000  0.000000  0.000000     57.0\n", "footballleagueseason      0.013514  0.003497  0.005556    286.0\n", "musicalartist             0.202703  1.000000  0.337079     30.0\n", "britishroyalty            0.226537  0.958904  0.366492     73.0\n", "scientist                 0.266458  0.977011  0.418719     87.0\n", "...                            ...       ...       ...      ...\n", "periodicalliterature      0.949495  0.990632  0.969628    854.0\n", "amusementparkattraction   0.947368  1.000000  0.972973     72.0\n", "naturalevent              0.950000  1.000000  0.974359    114.0\n", "eukaryote                 0.961131  0.992701  0.976661    274.0\n", "animal                    0.958880  0.998633  0.978353   2195.0\n", "\n", "[70 rows x 4 columns]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 2000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "report = classification_report(df[\"l2\"], df[\"gpt3_graph_l2\"], output_dict=True, zero_division=0)\n", "\n", "# Convert to DataFrame for easier manipulation\n", "report_df = pd.DataFrame(report).transpose()\n", "report_df = report_df.drop(['accuracy', 'macro avg', 'weighted avg'])\n", "\n", "# Display the report\n", "print(report_df.sort_values(by='f1-score'))\n", "\n", "# Plot F1 scores by class\n", "plt.figure(figsize=(20, 6))\n", "sns.barplot(x=report_df.index, y=report_df['f1-score'])\n", "plt.xticks(rotation=90, fontsize=7)\n", "plt.title('F Score of level 2')\n", "plt.xlabel('Category')\n", "plt.y<PERSON><PERSON>('F1 Score')\n", "plt.tight_layout()\n", "plt.savefig('dbpedia_level2.pdf')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.8946238368397165, 0.9073966058384023)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# change the l3\tas small letter\n", "df[\"l3\"] = df[\"l3\"].str.lower()\n", "df[\"gpt3_graph_l3\"] = df[\"gpt3_graph_l3\"].str.lower()\n", "# for each row, remove *, ', \" from gpt3_graph_l3\n", "df[\"gpt3_graph_l3\"] = df[\"gpt3_graph_l3\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "# if gpt3_graph_l3 is not in l3, set it to a random label from l3\n", "df[\"gpt3_graph_l3\"] = df[\"gpt3_graph_l3\"].apply(lambda x: x if x in df[\"l3\"].values else df[\"l3\"].sample(1).values[0])\n", "f1_l3 = f1_score(df[\"gpt3_graph_l3\"], df[\"l3\"], average=\"macro\")\n", "acc_l3 = accuracy_score(df[\"gpt3_graph_l3\"], df[\"l3\"])\n", "f1_l3, acc_l3"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                   precision    recall  f1-score  support\n", "sportsteammember                    0.000000  0.000000  0.000000     57.0\n", "officeholder                        0.326531  0.059259  0.100313    270.0\n", "canadianfootballteam                0.178571  0.500000  0.263158     20.0\n", "womenstennisassociationtournament   0.312500  0.288462  0.300000     52.0\n", "religious                           0.222222  0.526316  0.312500     95.0\n", "...                                      ...       ...       ...      ...\n", "earthquake                          1.000000  1.000000  1.000000     79.0\n", "solareclipse                        1.000000  1.000000  1.000000     35.0\n", "speedwayrider                       1.000000  1.000000  1.000000     67.0\n", "dartsplayer                         1.000000  1.000000  1.000000     53.0\n", "mollusca                            1.000000  1.000000  1.000000    287.0\n", "\n", "[219 rows x 4 columns]\n"]}, {"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 2000x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["report = classification_report(df[\"l3\"], df[\"gpt3_graph_l3\"], output_dict=True, zero_division=0)\n", "\n", "# Convert to DataFrame for easier manipulation\n", "report_df = pd.DataFrame(report).transpose()\n", "report_df = report_df.drop(['accuracy', 'macro avg', 'weighted avg'])\n", "\n", "# Display the report\n", "print(report_df.sort_values(by='f1-score'))\n", "\n", "# Plot F1 scores by class\n", "plt.figure(figsize=(20, 10))\n", "sns.barplot(x=report_df.index, y=report_df['f1-score'])\n", "plt.xticks(rotation=90, fontsize=5)\n", "plt.title('F1 Score of level 3')\n", "plt.xlabel('Category')\n", "plt.y<PERSON><PERSON>('F1 Score')\n", "plt.tight_layout()\n", "plt.savefig('dbpedia_level3.pdf')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ollama", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}