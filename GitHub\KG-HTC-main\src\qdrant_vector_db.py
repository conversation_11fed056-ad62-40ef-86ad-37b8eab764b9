import os
import json
import pickle
import pandas as pd
from pathlib import Path
from typing import Optional, List, Dict, Any, Union
from openai import OpenAI
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue
import httpx
import uuid
import logging
from datetime import datetime
from tqdm import tqdm

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置自定义 httpx Client，禁用 SSL 验证（仅测试用）
custom_httpx_client = httpx.Client(
    verify=False,
    timeout=httpx.Timeout(10.0)
)

# API 配置
OPENAI_API_KEY = "sk-kxqclwblxrbprnibbwxfachyqxnivhocpisdjiciniqckvll"
OPENAI_BASE_URL = "https://api.siliconflow.cn/v1/"
OPENAI_EMBEDDING_MODEL = "BAAI/bge-m3"

class QdrantEmbeddingFunction:
    """
    基于OpenAI接口的嵌入函数

    提供文本向量化功能，支持批量处理和错误重试机制
    """
    def __init__(self, api_key: str, base_url: str, model_name: str, max_retries: int = 3):
        """
        初始化嵌入函数

        Args:
            api_key: OpenAI API密钥
            base_url: API基础URL
            model_name: 嵌入模型名称
            max_retries: 最大重试次数
        """
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url,
            http_client=custom_httpx_client
        )
        self.model_name = model_name
        self.max_retries = max_retries
        logger.info(f"初始化嵌入函数: {model_name}")

    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        获取文本嵌入向量

        Args:
            texts: 文本列表

        Returns:
            嵌入向量列表

        Raises:
            Exception: 当所有重试都失败时抛出异常
        """
        if not texts:
            return []

        for attempt in range(self.max_retries):
            try:
                response = self.client.embeddings.create(
                    model=self.model_name,
                    input=texts
                )
                embeddings = [data.embedding for data in response.data]
                logger.debug(f"成功获取 {len(texts)} 个文本的嵌入向量")
                return embeddings

            except Exception as e:
                logger.warning(f"嵌入模型调用失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt == self.max_retries - 1:
                    logger.error(f"所有重试都失败，无法获取嵌入向量: {e}")
                    raise e

        return []

class QdrantVectorDB:
    """
    重新设计的基于Qdrant的向量数据库管理类

    特性：
    - 内存模式运行，支持本地持久化存储
    - 动态payload结构，根据CSV文件列名自动构建
    - 单一集合设计，使用text字段进行向量化
    - 支持CSV数据的批量导入和处理
    - 向后兼容现有接口
    """

    def __init__(self,
                 host: str = None,
                 port: int = 6333,
                 collection_name: str = "default",
                 use_memory: bool = True,
                 storage_path: str = None,
                 text_field: str = "text",
                 auto_persist: bool = True):
        """
        初始化Qdrant向量数据库

        Args:
            host: Qdrant服务器地址，如果为None且use_memory=True则使用内存模式
            port: Qdrant服务器端口
            collection_name: 集合名称
            use_memory: 是否使用内存模式（默认True，适合本地开发和测试）
            storage_path: 本地存储路径，用于数据持久化
            text_field: 用于向量化的文本字段名称
            auto_persist: 是否自动持久化数据
        """
        self.collection_name = collection_name
        self.text_field = text_field
        self.auto_persist = auto_persist
        self.use_memory = use_memory

        # 设置存储路径
        if storage_path is None:
            storage_path = f"./vector_storage/{collection_name}"
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)

        # 初始化Qdrant客户端
        if use_memory or host is None:
            # 使用内存模式，无需外部服务器
            self.client = QdrantClient(":memory:")
            logger.info("🚀 使用Qdrant内存模式，无需外部服务器")
        else:
            # 连接到外部Qdrant服务器
            self.client = QdrantClient(host=host, port=port)
            logger.info(f"🔗 连接到Qdrant服务器: {host}:{port}")

        # 为了向后兼容，保留这些属性
        self.title_collection = f"{collection_name}_title"
        self.text_collection = f"{collection_name}_text"

        # 初始化嵌入函数
        self.embedding_func = QdrantEmbeddingFunction(
            api_key=OPENAI_API_KEY,
            base_url=OPENAI_BASE_URL,
            model_name=OPENAI_EMBEDDING_MODEL
        )

        # 数据缓存
        self._data_cache = []
        self._metadata_schema = {}

        # 创建集合
        self._create_collections()

        # 尝试加载已存在的数据
        if self.auto_persist:
            self._load_persisted_data()
    
    def _create_collections(self):
        """
        创建向量集合

        使用单一集合设计，同时为了向后兼容保留title和text集合
        """
        try:
            # 获取嵌入维度（通过测试文本）
            test_embedding = self.embedding_func.get_embeddings(["test"])[0]
            vector_size = len(test_embedding)
            logger.info(f"检测到向量维度: {vector_size}")

            # 创建主集合
            try:
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(size=vector_size, distance=Distance.COSINE)
                )
                logger.info(f"✓ 创建主集合: {self.collection_name}")
            except Exception as e:
                logger.debug(f"主集合已存在或创建失败: {e}")

            # 为了向后兼容，创建title和text集合
            try:
                self.client.create_collection(
                    collection_name=self.title_collection,
                    vectors_config=VectorParams(size=vector_size, distance=Distance.COSINE)
                )
                logger.info(f"✓ 创建title集合: {self.title_collection}")
            except Exception as e:
                logger.debug(f"Title集合已存在或创建失败: {e}")

            try:
                self.client.create_collection(
                    collection_name=self.text_collection,
                    vectors_config=VectorParams(size=vector_size, distance=Distance.COSINE)
                )
                logger.info(f"✓ 创建text集合: {self.text_collection}")
            except Exception as e:
                logger.debug(f"Text集合已存在或创建失败: {e}")

        except Exception as e:
            logger.error(f"创建集合时发生错误: {e}")
            raise e

    def _save_metadata_schema(self):
        """保存元数据结构到本地文件"""
        if not self.auto_persist:
            return

        schema_file = self.storage_path / "metadata_schema.json"
        try:
            with open(schema_file, 'w', encoding='utf-8') as f:
                json.dump(self._metadata_schema, f, ensure_ascii=False, indent=2)
            logger.debug(f"保存元数据结构到: {schema_file}")
        except Exception as e:
            logger.warning(f"保存元数据结构失败: {e}")

    def _load_metadata_schema(self):
        """从本地文件加载元数据结构"""
        schema_file = self.storage_path / "metadata_schema.json"
        if schema_file.exists():
            try:
                with open(schema_file, 'r', encoding='utf-8') as f:
                    self._metadata_schema = json.load(f)
                logger.debug(f"加载元数据结构: {len(self._metadata_schema)} 个字段")
            except Exception as e:
                logger.warning(f"加载元数据结构失败: {e}")
                self._metadata_schema = {}

    def _save_data_cache(self):
        """保存数据缓存到本地文件"""
        if not self.auto_persist or not self._data_cache:
            return

        cache_file = self.storage_path / "data_cache.pkl"
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(self._data_cache, f)
            logger.debug(f"保存数据缓存: {len(self._data_cache)} 条记录")
        except Exception as e:
            logger.warning(f"保存数据缓存失败: {e}")

    def _load_data_cache(self):
        """从本地文件加载数据缓存"""
        cache_file = self.storage_path / "data_cache.pkl"
        if cache_file.exists():
            try:
                with open(cache_file, 'rb') as f:
                    self._data_cache = pickle.load(f)
                logger.debug(f"加载数据缓存: {len(self._data_cache)} 条记录")
            except Exception as e:
                logger.warning(f"加载数据缓存失败: {e}")
                self._data_cache = []

    def _load_persisted_data(self):
        """加载持久化的数据"""
        try:
            self._load_metadata_schema()
            self._load_data_cache()

            # 如果有缓存数据，重新加载到向量数据库
            if self._data_cache:
                logger.info(f"发现 {len(self._data_cache)} 条缓存数据，正在重新加载...")
                self._reload_cached_data()

        except Exception as e:
            logger.warning(f"加载持久化数据失败: {e}")

    def _reload_cached_data(self):
        """重新加载缓存的数据到向量数据库"""
        try:
            texts = []
            metadatas = []

            for item in tqdm(self._data_cache, desc="准备缓存数据"):
                if self.text_field in item:
                    texts.append(item[self.text_field])
                    # 构建metadata，排除text字段
                    metadata = {k: v for k, v in item.items() if k != self.text_field}
                    metadatas.append(metadata)

            if texts:
                self._batch_add_to_collections(texts, metadatas)
                logger.info(f"✓ 重新加载了 {len(texts)} 条数据到向量数据库")

        except Exception as e:
            logger.error(f"重新加载缓存数据失败: {e}")
    
    def batch_add(self, titles: List[str], texts: List[str], metadatas: Optional[List[dict]] = None):
        """
        批量添加文档到向量数据库
        
        Args:
            titles: 标题列表
            texts: 文本列表
            metadatas: 元数据列表，与titles和texts一一对应
        """
        
        # 循环调用单个添加方法，显示进度条
        for i, (title, text) in enumerate(tqdm(zip(titles, texts), total=len(titles), desc="添加文档")):
            metadata = metadatas[i] if metadatas and i < len(metadatas) else None
            self.add_single(title, text, metadata)
    
    def add_single(self, title: str, text: str, metadata: Optional[dict] = None):
        """添加单个文档"""
        # 生成嵌入向量
        title_embedding = self.embedding_func.get_embeddings([title])[0]
        text_embedding = self.embedding_func.get_embeddings([text])[0]
        
        # 生成唯一ID
        point_id = str(uuid.uuid4())
        
        # 构建payload
        payload = {
            "title": title,
            "text": text,
            "index": 0
        }
        if metadata:
            payload.update(metadata)
        
        # 创建title点
        title_point = PointStruct(
            id=point_id,
            vector=title_embedding,
            payload=payload
        )
        
        # 创建text点
        text_point = PointStruct(
            id=point_id,
            vector=text_embedding,
            payload=payload
        )
        
        # 插入到对应集合
        self.client.upsert(collection_name=self.title_collection, points=[title_point])
        self.client.upsert(collection_name=self.text_collection, points=[text_point])
    
    def _query_by_text(self, query_text: str, collection_name: str, n_results: int = 10, where: Optional[dict] = None) -> dict:
        """
        根据文本查询相似文档的内部方法
        
        Args:
            query_text: 查询文本
            collection_name: 集合名称
            n_results: 返回结果数量
            where: 元数据过滤条件
            
        Returns:
            查询结果字典
        """
        # 生成查询向量
        query_embedding = self.embedding_func.get_embeddings([query_text])[0]
        
        # 构建过滤条件
        query_filter = None
        if where:
            conditions = []
            for key, value in where.items():
                conditions.append(FieldCondition(key=key, match=MatchValue(value=value)))
            if conditions:
                query_filter = Filter(must=conditions)
        
        # 执行查询
        search_result = self.client.search(
            collection_name=collection_name,
            query_vector=query_embedding,
            limit=n_results,
            query_filter=query_filter,
            with_payload=True,
            with_vectors=False
        )
        
        # 格式化结果
        documents = []
        metadatas = []
        distances = []
        
        for point in search_result:
            documents.append(point.payload.get("text", ""))
            metadatas.append({k: v for k, v in point.payload.items() if k not in ["title", "text"]})
            distances.append(1 - point.score)  # Qdrant返回相似度，转换为距离
        
        return {
            "documents": [documents],
            "metadatas": [metadatas],
            "distances": [distances]
        }
    
    def query_by_title(self, query_text: str, n_results: int = 10, where: Optional[dict] = None) -> dict:
        """基于title向量查询"""
        return self._query_by_text(query_text, self.title_collection, n_results, where)
    
    def query_by_text(self, query_text: str, n_results: int = 10, where: Optional[dict] = None) -> dict:
        """基于text向量查询"""
        return self._query_by_text(query_text, self.text_collection, n_results, where)
    
    def query_l1(self, query_text: str, n_results: int = 10, search_type: str = "text") -> dict:
        """查询一级分类"""
        collection = self.text_collection if search_type == "text" else self.title_collection
        return self._query_by_text(query_text, collection, n_results, {"level": "Category1"})
    
    def query_l2(self, query_text: str, n_results: int = 10, search_type: str = "text") -> dict:
        """查询二级分类"""
        collection = self.text_collection if search_type == "text" else self.title_collection
        return self._query_by_text(query_text, collection, n_results, {"level": "Category2"})
    
    def query_l3(self, query_text: str, n_results: int = 10, search_type: str = "text") -> dict:
        """查询三级分类"""
        collection = self.text_collection if search_type == "text" else self.title_collection
        return self._query_by_text(query_text, collection, n_results, {"level": "Category3"})
    
    def query_all_levels(self, query_text: str, n_results: int = 10, search_type: str = "text") -> dict:
        """查询所有级别的分类"""
        collection = self.text_collection if search_type == "text" else self.title_collection
        return self._query_by_text(query_text, collection, n_results)
    
    def delete_by_ids(self, ids: List[str]):
        """根据ID删除文档"""
        self.client.delete(collection_name=self.title_collection, points_selector=ids)
        self.client.delete(collection_name=self.text_collection, points_selector=ids)
    
    def delete_by_metadata(self, where: dict):
        """根据元数据删除文档"""
        conditions = []
        for key, value in where.items():
            conditions.append(FieldCondition(key=key, match=MatchValue(value=value)))
        
        if conditions:
            filter_condition = Filter(must=conditions)
            self.client.delete(collection_name=self.title_collection, points_selector=filter_condition)
            self.client.delete(collection_name=self.text_collection, points_selector=filter_condition)
    
    def get_collection_info(self) -> dict:
        """获取集合信息"""
        title_info = self.client.get_collection(self.title_collection)
        text_info = self.client.get_collection(self.text_collection)
        
        return {
            "title_collection": {
                "name": self.title_collection,
                "count": title_info.points_count,
                "vector_size": title_info.config.params.vectors.size
            },
            "text_collection": {
                "name": self.text_collection,
                "count": text_info.points_count,
                "vector_size": text_info.config.params.vectors.size
            }
        }
    
    def clear_collections(self):
        """清空集合"""
        try:
            self.client.delete_collection(self.title_collection)
            self.client.delete_collection(self.text_collection)
            self._create_collections()
        except Exception as e:
            print(f"清空集合失败: {e}")




