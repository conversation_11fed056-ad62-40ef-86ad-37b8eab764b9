
from typing import List, Dict, Any
import random
from src.llm import LLM
from src.graph_db import GraphDB
from src.vector_db import VectorDB


class Pipeline:
    def __init__(self, llm: LLM, config: dict):
        self._config = config
        self._llm = llm
        self._graph_db = GraphDB()
        self._vector_db = VectorDB(
            database_path=self._config["vectdb_path"],
            collection_name=self._config["data_name"]
        )
        self._load_prompts()

    def _load_prompts(self):
        with open(self._config["template"]["sys"], "r") as f:
            self.system_template = f.read()
        with open(self._config["template"]["user"], "r") as f:
            self.user_template = f.read()

    def _format_category_text(self, categories: List[str]) -> str:
        return "**" + "**, **".join(categories) + "**"
    
    def query_related_nodes(self, text: str) -> Dict[str, Any]:
        """
        查询与输入文本相关的节点，并处理可能的空结果情况
        """
        # 查询L2节点
        l2_result = self._vector_db.query_l2(text, self._config["query_params"]["l2_top_k"])
        l2_nodes = l2_result.get("documents", [[]])[0] if l2_result else []
        
        # 查询L3节点（如果配置中包含l3_top_k参数）
        l3_nodes = None
        if "l3_top_k" in self._config["query_params"]:
            l3_result = self._vector_db.query_l3(text, self._config["query_params"]["l3_top_k"])
            l3_nodes = l3_result.get("documents", [[]])[0] if l3_result else []
        
        return {
            "l2": l2_nodes,
            "l3": l3_nodes
        }
    
    def build_linked_labels(self, l3_nodes: List[str], related_l2_nodes: List[str]) -> List[str]:
        """
        构建链接标签，处理可能的空值和None情况

        参数:
            l3_nodes: L3级别节点列表，可能为None或空列表
            related_l2_nodes: 相关的L2级别节点列表

        返回:
            构建的标签链接列表
        """
        labels = []

        # 检查输入参数
        if not l3_nodes:
            print("警告: l3_nodes为空，无法构建子图")
            return labels

        if not related_l2_nodes:
            print("警告: related_l2_nodes为空")

        for l3_node in l3_nodes:
            if not l3_node:  # 跳过空的l3_node
                continue

            try:
                # 查询L3对应的L2节点
                l2_node = self._graph_db.query_l2_from_l3(l3_node)
                #print(f"L3: {l3_node} -> L2: {l2_node}")

                if l2_node:  # 确保l2_node不为空
                    # 检查是否在相关L2节点中（如果有相关L2节点的话）
                    if not related_l2_nodes or l2_node in related_l2_nodes:
                        # 查询L1节点
                        try:
                            l1_node = self._graph_db.query_l1_from_l2(l2_node)
                            if l1_node:
                                labels.append(f"{l1_node} -> {l2_node} -> {l3_node}")
                            else:
                                labels.append(f"{l2_node} -> {l3_node}")
                        except Exception as e:
                            print(f"查询L1节点失败: {e}")
                            labels.append(f"{l2_node} -> {l3_node}")

                else:
                    print(f"L3节点 {l3_node} 没有对应的L2节点")

            except Exception as e:
                print(f"处理L3节点 {l3_node} 时出错: {e}")
                continue

        #print(f"构建的子图标签数量: {len(labels)}")
        return labels
    
    def predict_level(
            self, query_txt_vecdb: str, 
            context_nodes: List[str], 
            sub_graph: List[str]
        ) -> str:
        sys_msg = self.system_template.format(
            category_text=self._format_category_text(context_nodes),
            knowledge="\n".join(sub_graph)      
        )
        user_msg = self.user_template.format(text=query_txt_vecdb)
        messages = self._llm.construct_messages(sys_msg, user_msg)
        response = self._llm.chat(messages)
        
        if response is None:
            # random choose one from context_nodes
            response = random.choice(context_nodes)
        response = response.strip().lower()
        return response
    
    def predict_level_nasa(
            self, query_text: str, 
            context_nodes: List[str], 
            kg_context: List[str]
        ) -> str:

        uesr_prompt = f"""Classify this NASA Earth science dataset into one of these topic categories: {', '.join(context_nodes)}.

                Knowledge Graph Context:
                {kg_context}

                Dataset:
                {query_text}

                Topic Category:"""
            
        messages = self._llm.construct_messages("You are a NASA Earth science dataset classifier. Output only the exact category name.\nPlease provide the final answer directly without showing the reasoning process.",
                                                 uesr_prompt)
        response = self._llm.chat(messages)
        
        

        if response is None:
            # random choose one from context_nodes
            response = random.choice(context_nodes)
        response = response.strip().lower()
        return response
    
