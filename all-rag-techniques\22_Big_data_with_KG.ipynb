{"cells": [{"cell_type": "markdown", "id": "intro-0", "metadata": {}, "source": ["## End-to-End Pipeline: Big Data with Knowledge Graph (Book-Referenced)\n", "\n", "### Goal:\n", "Transform news articles about technology company acquisitions into a structured Knowledge Graph, using modern techniques for extraction, refinement, and reasoning — guided by foundational principles outlined in a conceptual book.\n", "\n", "### Dataset: CNN/DailyMail\n", "\n", "### Approach Overview:\n", "This notebook walks through a multi-phase process:\n", "1.  **Data Acquisition & Preparation:** Sourcing and cleaning raw news text.\n", "2.  **Information Extraction:** Identifying key entities (organizations, people, money, dates) and the relationships between them (e.g., 'acquire', 'invested_in').\n", "3.  **Knowledge Graph Construction:** Structuring the extracted information into RDF triples, forming the nodes and edges of our KG.\n", "4.  **KG Refinement (Conceptual):** Using embeddings to represent KG components and conceptually exploring link prediction.\n", "5.  **Persistence & Utilization:** Storing, querying (SPARQL), and visualizing the KG.\n", "\n", "We will leverage Large Language Models (LLMs) for complex NLP tasks like nuanced entity and relationship extraction, while also using traditional libraries like spaCy for initial exploration and `rdflib` for KG management."]}, {"cell_type": "markdown", "id": "b94b29f8", "metadata": {}, "source": ["# Table of Contents\n", "\n", "- [End-to-End Pipeline: Big Data with Knowledge Graph (Book-Referenced)](#intro-0)\n", "  - [Initial Setup: Imports and Configuration](#intro-setup)\n", "    - [Initialize LLM Client and spaCy Model](#llm-spacy-init-desc)\n", "    - [Define RDF Namespaces](#namespace-init-desc)\n", "- [Phase 1: Data Acquisition and Preparation](#phase1)\n", "  - [Step 1.1: Data Acquisition](#step1-1-desc)\n", "    - [Execute Data Acquisition](#data-acquisition-exec-desc)\n", "  - [Step 1.2: Data Cleaning & Preprocessing](#step1-2-desc)\n", "    - [Execute Data Cleaning](#data-cleaning-exec-desc)\n", "- [Phase 2: Information Extraction](#phase2)\n", "  - [Step 2.1: Entity Extraction (Named Entity Recognition - NER)](#step2-1-desc)\n", "    - [2.1.1: Entity Exploration with spaCy - Function Definition](#step2-1-1-spacy-desc)\n", "    - [2.1.1: Entity Exploration with spaCy - Plotting Function Definition](#plot_entity_distribution_func_def_desc)\n", "    - [2.1.1: Entity Exploration with spaCy - Execution](#spacy-ner-exec-desc)\n", "    - [Generic LLM Call Function Definition](#llm-call-func-def-desc)\n", "    - [2.1.2: Entity Type Selection using LLM - Execution](#step2-1-2-llm-type-selection-desc)\n", "    - [LLM JSON Output Parsing Function Definition](#parse_llm_json_func_def_desc)\n", "    - [2.1.3: Targeted Entity Extraction using LLM - Execution](#step2-1-3-llm-ner-exec-desc)\n", "  - [Step 2.2: Relationship Extraction](#step2-2-desc)\n", "- [Phase 3: Knowledge Graph Construction](#phase3)\n", "  - [Step 3.1: Entity Disambiguation & Linking (Simplified) - Normalization Function](#step3-1-normalize-entity-text-func-def-desc)\n", "    - [Execute Entity Normalization and URI Generation](#entity-normalization-exec-desc)\n", "  - [Step 3.2: Schema/Ontology Alignment - RDF Type Mapping Function](#step3-2-rdf-type-func-def-desc)\n", "    - [Schema/Ontology Alignment - RDF Predicate Mapping Function](#step3-2-rdf-predicate-func-def-desc)\n", "    - [Schema/Ontology Alignment - Examples](#schema-alignment-example-desc)\n", "  - [Step 3.3: Triple Generation](#step3-3-triple-generation-exec-desc)\n", "- [Phase 4: Knowledge Graph Refinement Using Embeddings](#phase4)\n", "  - [Step 4.1: Generate KG Embeddings - Embedding Function Definition](#step4-1-embedding-func-def-desc)\n", "    - [Generate KG Embeddings - Execution](#kg-embedding-exec-desc)\n", "  - [Step 4.2: <PERSON> Prediction (Knowledge Discovery - Conceptual) - Cosine Similarity Function](#step4-2-cosine-sim-func-def-desc)\n", "    - [Link Prediction (Conceptual) - Similarity Calculation Example](#link-prediction-exec-desc)\n", "  - [Step 4.3: Add Predicted Links (Optional & Conceptual) - Function Definition](#step4-3-add-inferred-func-def-desc)\n", "    - [Add Predicted Links (Conceptual) - Execution Example](#add-predicted-links-exec-desc)\n", "- [Phase 5: Persistence and Utilization](#phase5)\n", "  - [Step 5.1: Knowledge Graph Storage - Save Function Definition](#step5-1-save-graph-func-def-desc)\n", "    - [Knowledge Graph Storage - Execution](#kg-storage-exec-desc)\n", "  - [Step 5.2: Querying and Analysis - SPARQL Execution Function](#step5-2-sparql-func-def-desc)\n", "    - [SPARQL Querying and Analysis - Execution Examples](#sparql-querying-exec-desc)\n", "  - [Step 5.3: Visualization (Optional) - Visualization Function Definition](#step5-3-viz-func-def-desc)\n", "    - [KG Visualization - Execution](#visualization-exec-desc)\n", "- [Conclusion and Future Work](#conclusion)"]}, {"cell_type": "markdown", "id": "intro-setup", "metadata": {}, "source": ["### Initial Setup: Imports and Configuration\n", "\n", "**Theory:**\n", "Before any data processing or analysis can begin, we need to set up our environment. This involves:\n", "*   **Importing Libraries:** Bringing in the necessary Python packages. These include `datasets` for data loading, `openai` for interacting with LLMs, `spacy` for foundational NLP, `rdflib` for Knowledge Graph manipulation, `re` for text processing with regular expressions, `json` for handling LLM outputs, `matplotlib` and `pyvis` for visualization, and standard libraries like `os`, `collections`, and `tqdm`.\n", "*   **API Configuration:** Setting up credentials and endpoints for external services, specifically the Nebius LLM API. **Security Note:** In a production environment, API keys should never be hardcoded. Use environment variables or secure secret management systems.\n", "*   **Model Initialization:** Loading pre-trained models like spaCy's `en_core_web_sm` for basic NLP tasks and configuring the LLM client to use specific models deployed on Nebius for generation and embeddings.\n", "*   **Namespace Definitions:** For RDF-based Knowledge Graphs, namespaces (like `EX` for our custom terms, `SCHEMA` for schema.org) are crucial for creating unique and resolvable URIs for entities and properties. This aligns with the Linked Data principles."]}, {"cell_type": "code", "execution_count": null, "id": "setup-code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported.\n"]}], "source": ["# Import necessary libraries\n", "import os\n", "import re\n", "import json\n", "from collections import Counter\n", "import matplotlib.pyplot as plt\n", "from tqdm.auto import tqdm\n", "import pandas as pd\n", "import time\n", "\n", "# NLP and KG libraries\n", "import spacy\n", "from rdflib import Graph, Literal, Namespace, URIRef\n", "from rdflib.namespace import RDF, RDFS, XSD, SKOS # Added SKOS for altLabel\n", "\n", "# OpenAI client for LLM\n", "from openai import OpenAI\n", "\n", "# Visualization\n", "from pyvis.network import Network\n", "\n", "# Hugging Face datasets library\n", "from datasets import load_dataset\n", "\n", "# For embedding similarity\n", "import numpy as np\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "\n", "# --- API Configuration (IMPORTANT: Replace with your actual credentials and model names) ---\n", "NEBIUS_API_KEY = os.getenv(\"NEBIUS_API_KEY\", \"your_nebius_api_key_here\") # Replace with your actual API key\n", "NEBIUS_BASE_URL = \"https://api.studio.nebius.com/v1/\"\n", "\n", "# --- Model Names (IMPORTANT: Replace with your deployed model names) ---\n", "TEXT_GEN_MODEL_NAME = \"deepseek-ai/DeepSeek-V3\" # e.g., phi-4, deepseek or any other model\n", "EMBEDDING_MODEL_NAME = \"BAAI/bge-multilingual-gemma2\" # e.g., text-embedding-ada-002, BAAI/bge-multilingual-gemma2 or any other model\n", "\n", "print(\"Libraries imported.\")"]}, {"cell_type": "markdown", "id": "setup-code-output", "metadata": {}, "source": ["**Output Explanation:**\n", "This block simply confirms that the necessary libraries have been imported without error."]}, {"cell_type": "markdown", "id": "llm-spacy-init-desc", "metadata": {}, "source": ["#### Initialize LLM Client and spaCy Model\n", "\n", "**Theory:**\n", "Here, we instantiate the clients for our primary NLP tools:\n", "*   **OpenAI Client:** Configured to point to the Nebius API. This client will be used to send requests to the deployed LLM for tasks like entity extraction, relation extraction, and generating embeddings. A basic check is performed to see if the configuration parameters are set.\n", "*   **spaCy Model:** We load `en_core_web_sm`, a small English model from spaCy. This model provides efficient capabilities for tokenization, part-of-speech tagging, lemmatization, and basic Named Entity Recognition (NER). It's useful for initial text exploration and can complement LLM-based approaches."]}, {"cell_type": "code", "execution_count": 2, "id": "llm-spacy-init-code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OpenAI client initialized for base_url: https://api.studio.nebius.com/v1/ using model: deepseek-ai/DeepSeek-V3\n", "spaCy model 'en_core_web_sm' loaded.\n"]}], "source": ["client = None # Initialize client to None\n", "if NEBIUS_API_KEY != \"YOUR_NEBIUS_API_KEY\" and NEBIUS_BASE_URL != \"YOUR_NEBIUS_BASE_URL\" and TEXT_GEN_MODEL_NAME != \"YOUR_TEXT_GENERATION_MODEL_NAME\":\n", "    try:\n", "        client = OpenAI(\n", "            base_url=NEBIUS_BASE_URL,\n", "            api_key=NEBIUS_API_KEY \n", "        )\n", "        print(f\"OpenAI client initialized for base_url: {NEBIUS_BASE_URL} using model: {TEXT_GEN_MODEL_NAME}\")\n", "    except Exception as e:\n", "        print(f\"Error initializing OpenAI client: {e}\")\n", "        client = None # Ensure client is None if initialization fails\n", "else:\n", "    print(\"Warning: OpenAI client not fully configured. LLM features will be disabled. Please set NEBIUS_API_KEY, NEBIUS_BASE_URL, and TEXT_GEN_MODEL_NAME.\")\n", "\n", "nlp_spacy = None # Initialize nlp_spacy to None\n", "try:\n", "    nlp_spacy = spacy.load(\"en_core_web_sm\")\n", "    print(\"spaCy model 'en_core_web_sm' loaded.\")\n", "except OSError:\n", "    print(\"spaCy model 'en_core_web_sm' not found. Downloading... (This might take a moment)\")\n", "    try:\n", "        spacy.cli.download(\"en_core_web_sm\")\n", "        nlp_spacy = spacy.load(\"en_core_web_sm\")\n", "        print(\"spaCy model 'en_core_web_sm' downloaded and loaded successfully.\")\n", "    except Exception as e:\n", "        print(f\"Failed to download or load spaCy model: {e}\")\n", "        print(\"Please try: python -m spacy download en_core_web_sm in your terminal and restart the kernel.\")\n", "        nlp_spacy = None # Ensure nlp_spacy is None if loading fails"]}, {"cell_type": "markdown", "id": "llm-spacy-init-output", "metadata": {}, "source": ["**Output Explanation:**\n", "This block prints messages indicating the status of the OpenAI client and spaCy model initialization. Warnings are shown if configurations are missing or models can't be loaded."]}, {"cell_type": "markdown", "id": "namespace-init-desc", "metadata": {}, "source": ["#### Define RDF Namespaces\n", "\n", "**Theory:**\n", "In RDF, namespaces are used to avoid naming conflicts and to provide context for terms (URIs). \n", "*   `EX`: A custom namespace for terms specific to our project (e.g., our entities and relationships if not mapped to standard ontologies).\n", "*   `SCHEMA`: Refers to Schema.org, a widely used vocabulary for structured data on the internet. We'll try to map some of our extracted types to Schema.org terms for better interoperability.\n", "*   `RDFS`: RDF Schema, provides basic vocabulary for describing RDF vocabularies (e.g., `rdfs:label`, `rdfs:Class`).\n", "*   `RDF`: The core RDF vocabulary (e.g., `rdf:type`).\n", "*   `XSD`: XML Schema Datatypes, used for specifying literal data types (e.g., `xsd:string`, `xsd:date`).\n", "*   `SKOS`: Simple Knowledge Organization System, useful for thesauri, taxonomies, and controlled vocabularies (e.g., `skos:altLabel` for alternative names)."]}, {"cell_type": "code", "execution_count": 3, "id": "namespace-init-code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Custom namespace EX defined as: http://example.org/kg/\n", "Schema.org namespace SCHEMA defined as: http://schema.org/\n"]}], "source": ["EX = Namespace(\"http://example.org/kg/\")\n", "SCHEMA = Namespace(\"http://schema.org/\")\n", "\n", "print(f\"Custom namespace EX defined as: {EX}\")\n", "print(f\"Schema.org namespace SCHEMA defined as: {SCHEMA}\")"]}, {"cell_type": "markdown", "id": "namespace-init-output", "metadata": {}, "source": ["**Output Explanation:**\n", "This confirms the definition of our primary custom namespace (`EX`) and the `SCHEMA` namespace from Schema.org."]}, {"cell_type": "markdown", "id": "phase1", "metadata": {}, "source": ["## Phase 1: Data Acquisition and Preparation\n", "**(Ref: Ch. 1 – Big Data Ecosystem; Ch. 3 – Value Chain of Big Data Processing)**\n", "\n", "**Theory (Phase Overview):**\n", "This initial phase is critical in any data-driven project. It corresponds to the early stages of the Big Data value chain: \"Data Acquisition\" and parts of \"Data Preparation/Preprocessing\". The goal is to obtain the raw data and transform it into a state suitable for further processing and information extraction. Poor quality input data (the \"Garbage In, Garbage Out\" principle) will inevitably lead to a poor quality Knowledge Graph."]}, {"cell_type": "markdown", "id": "step1-1-desc", "metadata": {}, "source": ["### Step 1.1: Data Acquisition\n", "**Task:** Gather a collection of news articles.\n", "\n", "**Book Concept:** (Ch. 1, Figures 1 & 2; Ch. 3 - Data Acquisition stage)\n", "This step represents the \"Data Sources\" and \"Ingestion\" components of a Big Data ecosystem. We're tapping into an existing dataset (CNN/DailyMail via Hugging Face `datasets`) rather than scraping live news, but the principle is the same: bringing external data into our processing pipeline.\n", "\n", "**Methodology:**\n", "We will define a function `acquire_articles` to load the CNN/DailyMail dataset. To manage processing time and costs for this demonstration, and to focus on potentially relevant articles, this function will:\n", "1.  Load a specified split (e.g., 'train') of the dataset.\n", "2.  Optionally filter articles based on a list of keywords. For our goal of technology company acquisitions, keywords like \"acquire\", \"merger\", \"technology\", \"startup\" would be relevant. This is a simple heuristic; more advanced topic modeling or classification could be used for better filtering on larger datasets.\n", "3.  Take a small sample of the (filtered) articles.\n", "\n", "**Output:** A list of raw article data structures (typically dictionaries containing 'id', 'article' text, etc.)."]}, {"cell_type": "code", "execution_count": 4, "id": "acquire_articles_func_def", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Function 'acquire_articles' defined.\n"]}], "source": ["def acquire_articles(dataset_name=\"cnn_dailymail\", version=\"3.0.0\", split='train', sample_size=1000, keyword_filter=None):\n", "    \"\"\"Loads articles from the specified Hugging Face dataset, optionally filters them, and takes a sample.\"\"\"\n", "    print(f\"Attempting to load dataset: {dataset_name} (version: {version}, split: '{split}')...\")\n", "    try:\n", "        full_dataset = load_dataset(dataset_name, version, split=split, streaming=False) # Use streaming=False for easier slicing on smaller datasets\n", "        print(f\"Successfully loaded dataset. Total records in split: {len(full_dataset)}\")\n", "    except Exception as e:\n", "        print(f\"Error loading dataset {dataset_name}: {e}\")\n", "        print(\"Please ensure the dataset is available or you have internet connectivity.\")\n", "        return [] # Return empty list on failure\n", "    \n", "    raw_articles_list = []\n", "    if keyword_filter:\n", "        print(f\"Filtering articles containing any of keywords: {keyword_filter}...\")\n", "        # This is a simple keyword search. For very large datasets, this can be slow.\n", "        # Consider .filter() method of Hugging Face datasets for more efficiency if not streaming.\n", "        count = 0\n", "        # To avoid iterating the whole dataset if it's huge and we only need a small sample after filtering:\n", "        # We'll iterate up to a certain point or until we have enough filtered articles.\n", "        # This is a heuristic for balancing filtering with performance on potentially large datasets.\n", "        iteration_limit = min(len(full_dataset), sample_size * 20) # Look through at most 20x sample_size articles\n", "        for i in tqdm(range(iteration_limit), desc=\"Filtering articles\"):\n", "            record = full_dataset[i]\n", "            if any(keyword.lower() in record['article'].lower() for keyword in keyword_filter):\n", "                raw_articles_list.append(record)\n", "                count += 1\n", "            if count >= sample_size:\n", "                print(f\"Found {sample_size} articles matching filter criteria within {i+1} records checked.\")\n", "                break\n", "        if not raw_articles_list:\n", "            print(f\"Warning: No articles found with keywords {keyword_filter} within the first {iteration_limit} records. Returning an empty list.\")\n", "            return []\n", "        # If we found articles but less than sample_size, we take what we found.\n", "        # If we found more, we still only take sample_size.\n", "        raw_articles_list = raw_articles_list[:sample_size]\n", "    else:\n", "        print(f\"Taking the first {sample_size} articles without keyword filtering.\")\n", "        # Ensure sample_size does not exceed dataset length\n", "        actual_sample_size = min(sample_size, len(full_dataset))\n", "        raw_articles_list = list(full_dataset.select(range(actual_sample_size)))\n", "        \n", "    print(f\"Acquired {len(raw_articles_list)} articles.\")\n", "    return raw_articles_list\n", "\n", "print(\"Function 'acquire_articles' defined.\")"]}, {"cell_type": "markdown", "id": "acquire_articles_func_def_output", "metadata": {}, "source": ["**Output Explanation:**\n", "This cell defines the `acquire_articles` function. It will print a confirmation once the function is defined in the Python interpreter's memory."]}, {"cell_type": "markdown", "id": "data-acquisition-exec-desc", "metadata": {}, "source": ["#### Execute Data Acquisition\n", "\n", "**Theory:**\n", "Now we call the `acquire_articles` function. We define keywords relevant to our goal (technology company acquisitions) to guide the filtering process. A `SAMPLE_SIZE` is set to keep the amount of data manageable for this demonstration. Smaller samples allow for faster iteration, especially when using LLMs which can have associated costs and latency."]}, {"cell_type": "code", "execution_count": 5, "id": "data-acquisition-exec-code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Attempting to load dataset: cnn_dailymail (version: 3.0.0, split: 'train')...\n", "Successfully loaded dataset. Total records in split: 287113\n", "Filtering articles containing any of keywords: ['acquire', 'acquisition', 'merger', 'buyout', 'purchased by', 'acquired by', 'takeover']...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a09b9f3b82bc4649af79f8216c3b5034", "version_major": 2, "version_minor": 0}, "text/plain": ["Filtering articles:   0%|          | 0/200 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Acquired 3 articles.\n", "\n", "Example of a raw acquired article (ID: 56d7d67bb0fc32ee71cc006b915244776d883661):\n", "SAN DIEGO, California (CNN) -- You must know what's really driving the immigration debate. It's the culture, stupid. <PERSON><PERSON>.: Some opponents of immigration, even the legal kind, fear changes in local culture. Immigration restrictionists -- and by that, I mean those who want to limit all immigration, even the legal kind -- like to pretend they're so high-minded. Yet they can't help themselves. They always take the low road and harken back to the nativism that greeted earlier waves of...\n", "\n", "Number of fields in a record: 3\n", "Fields: ['article', 'highlights', 'id']\n"]}], "source": ["# Define keywords relevant to technology company acquisitions\n", "ACQUISITION_KEYWORDS = [\"acquire\", \"acquisition\", \"merger\", \"buyout\", \"purchased by\", \"acquired by\", \"takeover\"]\n", "TECH_KEYWORDS = [\"technology\", \"software\", \"startup\", \"app\", \"platform\", \"digital\", \"AI\", \"cloud\"]\n", "\n", "# For this demo, we will primarily filter by acquisition-related terms.\n", "# The 'technology' aspect will be reinforced through prompts to the LLM during entity/relation extraction.\n", "FILTER_KEYWORDS = ACQUISITION_KEYWORDS\n", "\n", "SAMPLE_SIZE = 10 # Keep very small for quick LLM processing in this demo notebook\n", "\n", "# Initialize raw_data_sample as an empty list\n", "raw_data_sample = [] \n", "raw_data_sample = acquire_articles(sample_size=SAMPLE_SIZE, keyword_filter=FILTER_KEYWORDS)\n", "\n", "if raw_data_sample: # Check if the list is not empty\n", "    print(f\"\\nExample of a raw acquired article (ID: {raw_data_sample[0]['id']}):\")\n", "    print(raw_data_sample[0]['article'][:500] + \"...\")\n", "    print(f\"\\nNumber of fields in a record: {len(raw_data_sample[0].keys())}\")\n", "    print(f\"Fields: {list(raw_data_sample[0].keys())}\")\n", "else:\n", "    print(\"No articles were acquired. Subsequent steps involving article processing might be skipped or produce no output.\")"]}, {"cell_type": "markdown", "id": "data-acquisition-exec-output", "metadata": {}, "source": ["**Output Explanation:**\n", "This block executes the data acquisition. It will print:\n", "*   Messages about the data loading and filtering process.\n", "*   The number of articles acquired.\n", "*   A snippet of the first acquired article and its available fields, to verify the process and understand the data structure."]}, {"cell_type": "markdown", "id": "step1-2-desc", "metadata": {}, "source": ["### Step 1.2: Data Cleaning & Preprocessing\n", "**Task:** Perform basic text normalization.\n", "\n", "**Book Concept:** (Ch. 3 - Variety challenge of Big Data)\n", "Raw text data from sources like news articles is often messy. It can contain HTML tags, boilerplate content (like bylines, copyright notices), special characters, and inconsistent formatting. This step parallels addressing the \"Variety\" (and to some extent, \"Veracity\") challenge of Big Data. Clean, normalized input is crucial for effective downstream NLP tasks, as noise can significantly degrade the performance of entity recognizers and relation extractors.\n", "\n", "**Methodology:**\n", "We'll define a function `clean_article_text` that uses regular expressions (`re` module) to:\n", "*   Remove common news boilerplate (e.g., \"(CNN) --\", specific byline patterns).\n", "*   Remove HTML tags and URLs.\n", "*   Normalize whitespace (e.g., replace multiple spaces/newlines with a single space).\n", "*   Optionally, handle quotes or other special characters that might interfere with LLM processing or JSON formatting if not handled carefully.\n", "\n", "**Output:** A list of dictionaries, where each dictionary contains the article ID and its cleaned text."]}, {"cell_type": "code", "execution_count": 6, "id": "clean_article_text_func_def", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Function 'clean_article_text' defined.\n"]}], "source": ["def clean_article_text(raw_text):\n", "    \"\"\"Cleans the raw text of a news article using regular expressions.\"\"\"\n", "    text = raw_text\n", "    \n", "    # Remove (CNN) style prefixes\n", "    text = re.sub(r'^\\(CNN\\)\\s*(--)?\\s*', '', text)\n", "    # Remove common bylines and publication/update lines (patterns may need adjustment for specific dataset nuances)\n", "    text = re.sub(r'By .*? for Dailymail\\.com.*?Published:.*?Updated:.*', '', text, flags=re.IGNORECASE | re.DOTALL)\n", "    text = re.sub(r'PUBLISHED:.*?BST,.*?UPDATED:.*?BST,.*', '', text, flags=re.IGNORECASE | re.DOTALL)\n", "    text = re.sub(r'Last updated at.*on.*', '', text, flags=re.IGNORECASE)\n", "    # Remove URLs\n", "    text = re.sub(r'https?://\\S+|www\\.\\S+', '[URL]', text)\n", "    # Remove HTML tags\n", "    text = re.sub(r'<.*?>', '', text)\n", "    # Remove email addresses\n", "    text = re.sub(r'\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b', '[EMAIL]', text)\n", "    # Normalize whitespace: replace newlines, tabs with a single space, then multiple spaces with a single space\n", "    text = text.replace('\\n', ' ').replace('\\r', ' ').replace('\\t', ' ')\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "    # Optional: escape quotes if LLM has issues, but usually not needed for good models\n", "    # text = text.replace('\"', \"\\\\\\\"\").replace(\"'\", \"\\\\'\") \n", "    return text\n", "\n", "print(\"Function 'clean_article_text' defined.\")"]}, {"cell_type": "markdown", "id": "clean_article_text_func_def_output", "metadata": {}, "source": ["**Output Explanation:**\n", "Confirms that the `clean_article_text` function, which will be used to preprocess article content, has been defined."]}, {"cell_type": "markdown", "id": "data-cleaning-exec-desc", "metadata": {}, "source": ["#### Execute Data Cleaning\n", "\n", "**Theory:**\n", "This block iterates through the `raw_data_sample` (acquired in the previous step). For each article, it calls the `clean_article_text` function. The cleaned text, along with the original article ID and potentially other useful fields like 'summary' (if available from the dataset as 'highlights'), is stored in a new list called `cleaned_articles`. This new list will be the primary input for the subsequent Information Extraction phase."]}, {"cell_type": "code", "execution_count": 7, "id": "data-cleaning-exec-code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cleaning 3 acquired articles...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9aeade12fca241dfbf636992cddfdc44", "version_major": 2, "version_minor": 0}, "text/plain": ["Cleaning articles:   0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Finished cleaning. Total cleaned articles: 3.\n", "\n", "Example of a cleaned article (ID: 56d7d67bb0fc32ee71cc006b915244776d883661):\n", "SAN DIEGO, California (CNN) -- You must know what's really driving the immigration debate. It's the culture, stupid. <PERSON><PERSON>.: Some opponents of immigration, even the legal kind, fear changes in local culture. Immigration restrictionists -- and by that, I mean those who want to limit all immigration, even the legal kind -- like to pretend they're so high-minded. Yet they can't help themselves. They always take the low road and harken back to the nativism that greeted earlier waves of...\n"]}], "source": ["cleaned_articles = [] # Initialize as an empty list\n", "\n", "if raw_data_sample: # Proceed only if raw_data_sample is not empty\n", "    print(f\"Cleaning {len(raw_data_sample)} acquired articles...\")\n", "    for record in tqdm(raw_data_sample, desc=\"Cleaning articles\"):\n", "        cleaned_text_content = clean_article_text(record['article'])\n", "        cleaned_articles.append({\n", "            \"id\": record['id'],\n", "            \"original_text\": record['article'], # Keep original for reference\n", "            \"cleaned_text\": cleaned_text_content,\n", "            \"summary\": record.get('highlights', '') # CNN/DM has 'highlights' which are summaries\n", "        })\n", "    print(f\"Finished cleaning. Total cleaned articles: {len(cleaned_articles)}.\")\n", "    if cleaned_articles: # Check if list is not empty after processing\n", "        print(f\"\\nExample of a cleaned article (ID: {cleaned_articles[0]['id']}):\")\n", "        print(cleaned_articles[0]['cleaned_text'][:500] + \"...\")\n", "else:\n", "    print(\"No raw articles were acquired in the previous step, so skipping cleaning.\")\n", "\n", "# This ensures cleaned_articles is always defined, even if empty.\n", "if 'cleaned_articles' not in globals():\n", "    cleaned_articles = []\n", "    print(\"Initialized 'cleaned_articles' as an empty list because it was not created prior.\")"]}, {"cell_type": "markdown", "id": "data-cleaning-exec-output", "metadata": {}, "source": ["**Output Explanation:**\n", "This block will:\n", "*   Indicate the start and end of the cleaning process.\n", "*   Show the number of articles cleaned.\n", "*   Display a snippet of the first cleaned article's text, allowing for a visual check of the cleaning effectiveness."]}, {"cell_type": "markdown", "id": "phase2", "metadata": {}, "source": ["## Phase 2: Information Extraction\n", "**(Ref: Ch. 2 – Basics of Knowledge Graphs; Ch. 4 – KG Creation from Structured Data)**\n", "\n", "**Theory (Phase Overview):**\n", "Information Extraction (IE) is the process of automatically extracting structured information from unstructured or semi-structured sources (like our news articles). In the context of Knowledge Graph creation, IE is paramount as it identifies the fundamental building blocks: entities (nodes) and the relationships (edges) that connect them. This phase directly addresses how to transform raw text into a more structured format, a key step before KG materialization (Ch. 4). It involves tasks like Named Entity Recognition (NER) and Relationship Extraction (RE)."]}, {"cell_type": "markdown", "id": "step2-1-desc", "metadata": {}, "source": ["### Step 2.1: Entity Extraction (Named Entity Recognition - NER)\n", "**Task:** Identify named entities like organizations, people, products, monetary figures, and dates.\n", "\n", "**Book Concept:** (Ch. 2 - Entities as nodes)\n", "Named Entities are real-world objects, such as persons, locations, organizations, products, etc., that can be denoted with a proper name. In a KG, these entities become the *nodes*. Accurate NER is foundational for building a meaningful graph.\n", "\n", "**Methodology:**\n", "We'll employ a two-pronged approach:\n", "1.  **Exploratory NER with spaCy:** Use spaCy's pre-trained model to get a quick overview of common entity types present in our cleaned articles. This helps in understanding the general landscape of entities.\n", "2.  **LLM-driven Entity Type Selection:** Based on spaCy's output and our specific goal (technology acquisitions), we'll prompt an LLM to suggest a focused set of entity types that are most relevant.\n", "3.  **Targeted NER with LLM:** Use the LLM with the refined list of entity types to perform NER on the articles, aiming for higher accuracy and relevance for our specific domain. LLMs can be powerful here due to their contextual understanding, especially when guided by well-crafted prompts."]}, {"cell_type": "markdown", "id": "step2-1-1-spacy-desc", "metadata": {}, "source": ["#### 2.1.1: Entity Exploration with spaCy - Function Definition\n", "\n", "**Theory:**\n", "This function, `get_spacy_entity_counts`, takes a list of articles, processes a sample of their text using spaCy's NER capabilities, and returns a counter object tallying the frequencies of different entity labels (e.g., `PERSON`, `ORG`, `GPE`). This gives us an empirical basis for understanding what kinds of entities are prevalent in our dataset before we engage the more resource-intensive LLM."]}, {"cell_type": "code", "execution_count": 8, "id": "get_spacy_entity_counts_func_def", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Function 'get_spacy_entity_counts' defined.\n"]}], "source": ["def get_spacy_entity_counts(articles_data, text_field='cleaned_text', sample_size_spacy=50):\n", "    \"\"\"Processes a sample of articles with spaCy and counts entity labels.\"\"\"\n", "    if not nlp_spacy:\n", "        print(\"spaCy model not loaded. Skipping spaCy entity counting.\")\n", "        return Counter()\n", "    if not articles_data:\n", "        print(\"No articles data provided to spaCy for entity counting. Skipping.\")\n", "        return Counter()\n", "    \n", "    label_counter = Counter()\n", "    # Process a smaller sample for quick spaCy analysis\n", "    sample_to_process = articles_data[:min(len(articles_data), sample_size_spacy)]\n", "    \n", "    print(f\"Processing {len(sample_to_process)} articles with spaCy for entity counts...\")\n", "    for article in tqdm(sample_to_process, desc=\"spaCy NER for counts\"):\n", "        doc = nlp_spacy(article[text_field])\n", "        for ent in doc.ents:\n", "            label_counter[ent.label_] += 1\n", "    return label_counter\n", "\n", "print(\"Function 'get_spacy_entity_counts' defined.\")"]}, {"cell_type": "markdown", "id": "get_spacy_entity_counts_func_def_output", "metadata": {}, "source": ["**Output Explanation:**\n", "Confirms the definition of the `get_spacy_entity_counts` function."]}, {"cell_type": "markdown", "id": "plot_entity_distribution_func_def_desc", "metadata": {}, "source": ["#### 2.1.1: Entity Exploration with spaCy - Plotting Function Definition\n", "\n", "**Theory:**\n", "The `plot_entity_distribution` function takes the entity counts (from `get_spacy_entity_counts`) and uses `matplotlib` to generate a bar chart. Visualizing this distribution helps in quickly identifying the most frequent entity types, which can inform subsequent decisions about which types to prioritize for the KG."]}, {"cell_type": "code", "execution_count": 9, "id": "plot_entity_distribution_func_def", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Function 'plot_entity_distribution' defined.\n"]}], "source": ["def plot_entity_distribution(label_counter_to_plot):\n", "    \"\"\"Plots the distribution of entity labels from a Counter object.\"\"\"\n", "    if not label_counter_to_plot:\n", "        print(\"No entity counts to plot.\")\n", "        return\n", "    \n", "    # Get the most common 15, or all if fewer than 15\n", "    top_items = label_counter_to_plot.most_common(min(15, len(label_counter_to_plot)))\n", "    if not top_items: # Handle case where counter is not empty but most_common(0) or similar edge case\n", "        print(\"No items to plot from entity counts.\")\n", "        return\n", "        \n", "    labels, counts = zip(*top_items)\n", "    \n", "    plt.figure(figsize=(12, 7))\n", "    plt.bar(labels, counts, color='skyblue')\n", "    plt.title(\"Top Entity Type Distribution (via spaCy)\")\n", "    plt.ylabel(\"Frequency\")\n", "    plt.xlabel(\"Entity Label\")\n", "    plt.xticks(rotation=45, ha=\"right\")\n", "    plt.tight_layout() # Adjust layout to make sure everything fits\n", "    plt.show()\n", "\n", "print(\"Function 'plot_entity_distribution' defined.\")"]}, {"cell_type": "markdown", "id": "plot_entity_distribution_func_def_output", "metadata": {}, "source": ["**Output Explanation:**\n", "Confirms the definition of the `plot_entity_distribution` function."]}, {"cell_type": "markdown", "id": "spacy-ner-exec-desc", "metadata": {}, "source": ["#### 2.1.1: Entity Exploration with spaCy - Execution\n", "\n", "**Theory:**\n", "This block executes the spaCy-based entity exploration. It calls `get_spacy_entity_counts` on the `cleaned_articles`. The resulting counts are then printed and passed to `plot_entity_distribution` to visualize the findings. This step is skipped if no cleaned articles are available or if the spaCy model failed to load."]}, {"cell_type": "code", "execution_count": 10, "id": "spacy-ner-exec-code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running spaCy NER on a sample of 3 cleaned articles...\n", "Processing 3 articles with spaCy for entity counts...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0697c026abbd4932bca897921dae59fc", "version_major": 2, "version_minor": 0}, "text/plain": ["spaCy NER for counts:   0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "spaCy Entity Counts (from sample):\n", "  ORG: 57\n", "  GPE: 47\n", "  PERSON: 39\n", "  NORP: 28\n", "  CARDINAL: 27\n", "  DATE: 22\n", "  LOC: 7\n", "  LANGUAGE: 5\n", "  PRODUCT: 4\n", "  ORDINAL: 4\n", "  FAC: 3\n", "  TIME: 3\n", "  WORK_OF_ART: 2\n", "  LAW: 1\n", "  MONEY: 1\n", "  QUANTITY: 1\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["spacy_entity_counts = Counter() # Initialize an empty counter\n", "\n", "if cleaned_articles and nlp_spacy:\n", "    # Use a small, fixed sample size for spaCy analysis to keep it quick\n", "    spacy_analysis_sample_size = min(len(cleaned_articles), 20) \n", "    print(f\"Running spaCy NER on a sample of {spacy_analysis_sample_size} cleaned articles...\")\n", "    spacy_entity_counts = get_spacy_entity_counts(cleaned_articles, sample_size_spacy=spacy_analysis_sample_size)\n", "    \n", "    if spacy_entity_counts:\n", "        print(\"\\nspaCy Entity Counts (from sample):\")\n", "        for label, count in spacy_entity_counts.most_common():\n", "            print(f\"  {label}: {count}\")\n", "        plot_entity_distribution(spacy_entity_counts)\n", "    else:\n", "        print(\"spaCy NER did not return any entity counts from the sample.\")\n", "else:\n", "    print(\"Skipping spaCy entity analysis: No cleaned articles available or spaCy model not loaded.\")"]}, {"cell_type": "markdown", "id": "spacy-ner-exec-output", "metadata": {}, "source": ["**Output Explanation:**\n", "This block will print:\n", "*   The frequency of different entity types found by spaCy in the sample.\n", "*   A bar chart visualizing this distribution.\n", "If prerequisites are not met, it will print a message indicating why this step was skipped."]}, {"cell_type": "markdown", "id": "llm-call-func-def-desc", "metadata": {}, "source": ["#### Generic LLM Call Function Definition\n", "\n", "**Theory:**\n", "To interact with the LLM for various tasks (entity type selection, NER, relation extraction), we define a reusable helper function `call_llm_for_response`. This function encapsulates the logic for:\n", "*   Taking a system prompt (instructions for the LLM) and a user prompt (the specific input/query).\n", "*   Making the API call to the configured LLM endpoint.\n", "*   Extracting the textual content from the LLM's response.\n", "*   Basic error handling if the LLM client is not initialized or if the API call fails.\n", "Using a helper function promotes code reusability and makes the main logic cleaner."]}, {"cell_type": "code", "execution_count": 11, "id": "llm-call-func-def", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Function 'call_llm_for_response' defined.\n"]}], "source": ["def call_llm_for_response(system_prompt, user_prompt, model_to_use=TEXT_GEN_MODEL_NAME, temperature=0.2):\n", "    \"\"\"Generic function to call the LLM and get a response, with basic error handling.\"\"\"\n", "    if not client:\n", "        print(\"LLM client not initialized. Skipping LLM call.\")\n", "        return \"LLM_CLIENT_NOT_INITIALIZED\"\n", "    try:\n", "        print(f\"\\nCalling LLM (model: {model_to_use}, temperature: {temperature})...\")\n", "        # For debugging, uncomment to see prompts (can be very long)\n", "        # print(f\"System Prompt (first 200 chars): {system_prompt[:200]}...\")\n", "        # print(f\"User Prompt (first 200 chars): {user_prompt[:200]}...\")\n", "        \n", "        response = client.chat.completions.create(\n", "            model=model_to_use,\n", "            messages=[\n", "                {\"role\": \"system\", \"content\": system_prompt},\n", "                {\"role\": \"user\", \"content\": user_prompt}\n", "            ],\n", "            temperature=temperature # Lower temperature for more focused/deterministic output\n", "        )\n", "        content = response.choices[0].message.content.strip()\n", "        print(\"LLM response received.\")\n", "        return content\n", "    except Exception as e:\n", "        print(f\"Error calling LLM: {e}\")\n", "        return f\"LLM_ERROR: {str(e)}\"\n", "\n", "print(\"Function 'call_llm_for_response' defined.\")"]}, {"cell_type": "markdown", "id": "llm-call-func-def-output", "metadata": {}, "source": ["**Output Explanation:**\n", "Confirms the definition of the `call_llm_for_response` helper function."]}, {"cell_type": "markdown", "id": "step2-1-2-llm-type-selection-desc", "metadata": {}, "source": ["#### 2.1.2: Entity Type Selection using LLM - Execution\n", "\n", "**Theory:**\n", "While spaCy provides a general set of entity types, not all may be relevant for our specific goal of building a KG about technology company acquisitions. For instance, `WORK_OF_ART` might be less important than `ORG` (organization) or `MONEY`. \n", "In this step, we leverage the LLM's understanding to refine this list. \n", "1.  **System Prompt:** We craft a detailed system prompt instructing the LLM to act as an expert in KG construction for technology news. It's asked to select the *most relevant* entity labels from the spaCy-derived list, focusing on our domain, and to provide an explanation for each chosen type.\n", "2.  **User Prompt:** The user prompt contains the actual list of entity labels and their frequencies obtained from spaCy.\n", "3.  **LLM Call:** We use our `call_llm_for_response` function.\n", "The LLM's output should be a comma-separated string of chosen entity types with their descriptions (e.g., `ORG (Organizations involved in acquisitions, e.g., Google, Microsoft)`). This curated list forms a more targeted schema for our subsequent LLM-based NER."]}, {"cell_type": "code", "execution_count": 12, "id": "llm-entity-type-selection-exec-code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Calling LLM (model: deepseek-ai/DeepSeek-V3, temperature: 0.2)...\n", "LLM response received.\n", "\n", "LLM Suggested Entity Types for Tech Acquisitions KG:\n", "\n", "Final list of Entity Types to be used for NER: ORG (Company involved in acquisition, e.g., Google, Microsoft), PERSON (Key individuals like CEOs or founders, e.g., <PERSON><PERSON><PERSON>), DATE (Date of acquisition announcement or closing, e.g., July 26, 2023), MONEY (Transaction value or investment, e.g., $1 billion), PRODUCT (Technology products or services involved, e.g., cloud computing platform), GPE (Geopolitical entities related to the acquisition, e.g., United States, California), CARDINAL (Numerical values relevant to the deal, e.g., number of employees transferred)\n"]}], "source": ["ENTITY_TYPE_SELECTION_SYSTEM_PROMPT = (\n", "    \"You are an expert assistant specializing in Knowledge Graph construction for technology news analysis. \"\n", "    \"You will be provided with a list of named entity labels and their frequencies, derived from news articles. \"\n", "    \"Your task is to select and return a comma-separated list of the MOST RELEVANT entity labels for building a Knowledge Graph focused on **technology company acquisitions**. \"\n", "    \"Prioritize labels like organizations (acquirer, acquired), financial amounts (deal value), dates (announcement/closing), key persons (CEOs, founders), and relevant technology products/services or sectors. \"\n", "    \"For EACH entity label you include in your output list, provide a concise parenthetical explanation or a clear, illustrative example. \"\n", "    \"Example: ORG (Company involved in acquisition, e.g., Google, Microsoft), MONEY (Transaction value or investment, e.g., $1 billion), DATE (Date of acquisition announcement or closing, e.g., July 26, 2023). \"\n", "    \"The output MUST be ONLY the comma-separated list of labels and their parenthetical explanations. \"\n", "    \"Do not include any introductory phrases, greetings, summaries, or any other text whatsoever outside of this formatted list.\"\n", ")\n", "\n", "llm_selected_entity_types_str = \"\" # Initialize\n", "DEFAULT_ENTITY_TYPES_STR = \"ORG (Acquiring or acquired company, e.g., TechCorp), PERSON (Key executives, e.g., CEO), MONEY (Acquisition price, e.g., $500 million), DATE (Date of acquisition announcement), PRODUCT (Key product/service involved), GPE (Location of companies, e.g., Silicon Valley)\"\n", "\n", "if spacy_entity_counts and client: # Proceed if we have spaCy counts and LLM client is available\n", "    # Create a string from spaCy entity counts for the prompt\n", "    spacy_labels_for_prompt = \", \".join([f\"{label} (frequency: {count})\" for label, count in spacy_entity_counts.most_common()])\n", "    user_prompt_for_types = f\"From the following entity labels and their frequencies found in news articles: [{spacy_labels_for_prompt}]. Please select and format the most relevant entity types for a knowledge graph about technology company acquisitions, as per the instructions.\"\n", "    \n", "    llm_selected_entity_types_str = call_llm_for_response(ENTITY_TYPE_SELECTION_SYSTEM_PROMPT, user_prompt_for_types)\n", "    \n", "    if \"LLM_CLIENT_NOT_INITIALIZED\" in llm_selected_entity_types_str or \"LLM_ERROR\" in llm_selected_entity_types_str or not llm_selected_entity_types_str.strip():\n", "        print(\"\\nLLM entity type selection failed or returned empty. Using default entity types.\")\n", "        llm_selected_entity_types_str = DEFAULT_ENTITY_TYPES_STR\n", "    else:\n", "        print(\"\\nLLM Suggested Entity Types for Tech Acquisitions KG:\")\n", "        # Post-process to ensure it's a clean list if LLM adds extra verbiage despite instructions\n", "        # This is a simple heuristic, more robust parsing might be needed for less compliant LLMs\n", "        if not re.match(r\"^([A-Z_]+ \\(.*?\\))(, [A-Z_]+ \\(.*?\\))*$\", llm_selected_entity_types_str.strip()):\n", "             print(f\"Warning: LLM output for entity types might not be in the expected strict format. Raw: '{llm_selected_entity_types_str}'\")\n", "             # Attempt a simple cleanup: take the longest line that looks like a list of terms\n", "             lines = llm_selected_entity_types_str.strip().split('\\n')\n", "             best_line = \"\"\n", "             for line in lines:\n", "                 if '(' in line and ')' in line and len(line) > len(best_line):\n", "                     best_line = line\n", "             if best_line:\n", "                 llm_selected_entity_types_str = best_line\n", "                 print(f\"Attempted cleanup: '{llm_selected_entity_types_str}'\")\n", "             else:\n", "                 print(\"Cleanup failed, falling back to default entity types.\")\n", "                 llm_selected_entity_types_str = DEFAULT_ENTITY_TYPES_STR\n", "else:\n", "    print(\"\\nSkipping LLM entity type selection (spaCy counts unavailable or LLM client not initialized). Using default entity types.\")\n", "    llm_selected_entity_types_str = DEFAULT_ENTITY_TYPES_STR\n", "\n", "print(f\"\\nFinal list of Entity Types to be used for NER: {llm_selected_entity_types_str}\")"]}, {"cell_type": "markdown", "id": "llm-entity-type-selection-exec-output", "metadata": {}, "source": ["**Output Explanation:**\n", "This block will print:\n", "*   The comma-separated list of entity types and their descriptions as suggested by the LLM (or the default list if the LLM call fails/is skipped).\n", "*   This list will guide the next step: targeted Named Entity Recognition."]}, {"cell_type": "markdown", "id": "parse_llm_json_func_def_desc", "metadata": {}, "source": ["#### LLM JSON Output Parsing Function Definition\n", "\n", "**Theory:**\n", "LLMs, even when prompted for specific formats like JSON, can sometimes produce output that includes extra text, markdown formatting (like ` ```json ... ``` `), or slight deviations from perfect JSON. The `parse_llm_json_output` function is a utility to robustly parse the LLM's string output into a Python list of dictionaries (representing entities or relations).\n", "It attempts to:\n", "1.  Handle common markdown code block syntax.\n", "2.  Use `json.loads()` for parsing.\n", "3.  Include error handling for `JSONDecodeError` and provide fallback mechanisms like regex-based extraction if simple parsing fails.\n", "This function is crucial for reliably converting LLM responses into usable structured data."]}, {"cell_type": "code", "execution_count": 13, "id": "parse_llm_json_func_def", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Function 'parse_llm_json_output' defined.\n"]}], "source": ["def parse_llm_json_output(llm_output_str):\n", "    \"\"\"Parses JSON output from LLM, handling potential markdown code blocks and common issues.\"\"\"\n", "    if not llm_output_str or \"LLM_CLIENT_NOT_INITIALIZED\" in llm_output_str or \"LLM_ERROR\" in llm_output_str:\n", "        print(\"Cannot parse LLM output: LLM did not run, errored, or output was empty.\")\n", "        return [] # Return empty list\n", "\n", "    # Attempt to extract JSON from within markdown code blocks\n", "    match = re.search(r'```json\\s*([\\s\\S]*?)\\s*```', llm_output_str, re.IGNORECASE)\n", "    if match:\n", "        json_str = match.group(1).strip()\n", "    else:\n", "        # If no markdown block, assume the whole string is the JSON (or needs cleaning)\n", "        # LLMs sometimes add introductory text before the JSON list. Try to find the start of the list.\n", "        list_start_index = llm_output_str.find('[')\n", "        list_end_index = llm_output_str.rfind(']')\n", "        if list_start_index != -1 and list_end_index != -1 and list_start_index < list_end_index:\n", "            json_str = llm_output_str[list_start_index : list_end_index+1].strip()\n", "        else:\n", "            json_str = llm_output_str.strip() # Fallback to the whole string\n", "\n", "    try:\n", "        parsed_data = json.loads(json_str)\n", "        if isinstance(parsed_data, list):\n", "            return parsed_data\n", "        else:\n", "            print(f\"Warning: LLM output was valid JSON but not a list (type: {type(parsed_data)}). Returning empty list.\")\n", "            print(f\"Problematic JSON string (or part of it): {json_str[:200]}...\")\n", "            return []\n", "    except json.JSONDecodeError as e:\n", "        print(f\"Error decoding JSON from LLM output: {e}\")\n", "        print(f\"Problematic JSON string (or part of it): {json_str[:500]}...\")\n", "        # Optional: A more aggressive regex fallback if standard parsing fails badly\n", "        # This is risky as it might grab partial JSONs. Use with caution.\n", "        # entities_found = []\n", "        # for match_obj in re.finditer(r'\\{\\s*\"text\":\\s*\".*?\",\\s*\"type\":\\s*\".*?\"\\s*\\}', json_str):\n", "        #     try:\n", "        #        entities_found.append(json.loads(match_obj.group(0)))\n", "        #     except json.JSONDecodeError: continue # Skip malformed individual objects\n", "        # if entities_found:\n", "        #    print(f\"Warning: Recovered {len(entities_found)} entities using aggressive regex due to JSON error.\")\n", "        #    return entities_found\n", "        return []\n", "    except Exception as e:\n", "        print(f\"An unexpected error occurred during LLM JSON output parsing: {e}\")\n", "        return []\n", "\n", "print(\"Function 'parse_llm_json_output' defined.\")"]}, {"cell_type": "markdown", "id": "parse_llm_json_func_def_output", "metadata": {}, "source": ["**Output Explanation:**\n", "Confirms the definition of the `parse_llm_json_output` utility function."]}, {"cell_type": "markdown", "id": "step2-1-3-llm-ner-exec-desc", "metadata": {}, "source": ["#### 2.1.3: Targeted Entity Extraction using LLM - Execution\n", "\n", "**Theory:**\n", "Now, equipped with our curated list of entity types (`llm_selected_entity_types_str`), we instruct the LLM to perform NER on each (cleaned) article. \n", "1.  **System Prompt:** The system prompt for NER is carefully constructed. It tells the LLM:\n", "    *   Its role (expert NER system for tech acquisitions).\n", "    *   The specific entity types to focus on (from `llm_selected_entity_types_str`).\n", "    *   The required output format: a JSON list of objects, where each object has `\"text\"` (the exact extracted entity span) and `\"type\"` (one of the specified entity types).\n", "    *   An example of the desired JSON output.\n", "    *   To output an empty JSON list `[]` if no relevant entities are found.\n", "2.  **User Prompt:** For each article, the user prompt is simply its `cleaned_text`.\n", "3.  **Processing Loop:** We iterate through a small sample of `cleaned_articles` (defined by `MAX_ARTICLES_FOR_LLM_NER` to manage time/cost). For each:\n", "    *   The article text is (optionally truncated if too long for LLM context window).\n", "    *   `call_llm_for_response` is invoked.\n", "    *   `parse_llm_json_output` processes the LLM's response.\n", "    *   The extracted entities are stored alongside the article data in a new list, `articles_with_entities`.\n", "A small delay (`time.sleep`) is added between API calls to be polite to the API endpoint and avoid potential rate limiting."]}, {"cell_type": "code", "execution_count": 14, "id": "llm-ner-exec-code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting LLM NER for 3 articles...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "84d4cca6263341858b79d2b43219c17a", "version_major": 2, "version_minor": 0}, "text/plain": ["LLM NER Processing:   0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Processing article ID: 56d7d67bb0fc32ee71cc006b915244776d883661 for NER with LLM (1/3)...\n", "\n", "Calling LLM (model: deepseek-ai/DeepSeek-V3, temperature: 0.2)...\n", "LLM response received.\n", "  Extracted 0 entities for article ID 56d7d67bb0fc32ee71cc006b915244776d883661.\n", "\n", "Processing article ID: 4cf51ce9372dff8ff7f44f098eab1c1d7569af7a for NER with LLM (2/3)...\n", "\n", "Calling LLM (model: deepseek-ai/DeepSeek-V3, temperature: 0.2)...\n", "LLM response received.\n", "  Extracted 23 entities for article ID 4cf51ce9372dff8ff7f44f098eab1c1d7569af7a.\n", "  Sample entities: [\n", "  {\n", "    \"text\": \"United Nations\",\n", "    \"type\": \"ORG\"\n", "  },\n", "  {\n", "    \"text\": \"Algiers\",\n", "    \"type\": \"GPE\"\n", "  },\n", "  {\n", "    \"text\": \"CNN\",\n", "    \"type\": \"ORG\"\n", "  }\n", "]\n", "\n", "Processing article ID: 82a0e1f034174079179821b052f33df76c781b47 for NER with LLM (3/3)...\n", "\n", "Calling LLM (model: deepseek-ai/DeepSeek-V3, temperature: 0.2)...\n", "LLM response received.\n", "  Extracted 0 entities for article ID 82a0e1f034174079179821b052f33df76c781b47.\n", "\n", "Finished LLM NER. Processed 3 articles and stored entities.\n"]}], "source": ["LLM_NER_SYSTEM_PROMPT_TEMPLATE = (\n", "    \"You are an expert Named Entity Recognition system specialized in identifying information about **technology company acquisitions**. \"\n", "    \"From the provided news article text, identify and extract entities. \"\n", "    \"The entity types to focus on are: {entity_types_list_str}. \"\n", "    \"Ensure that the extracted 'text' for each entity is an EXACT span from the article. \"\n", "    \"Output ONLY a valid JSON list of objects, where each object has 'text' (the exact extracted entity string) and 'type' (one of the entity type main labels, e.g., ORG, PERSON, MONEY from your list) keys. \"\n", "    \"Example: [{{'text': 'United Nations', 'type': 'ORG'}}, {{'text': 'Barack Obama', 'type': 'PERSON'}}, {{'text': 'iPhone 15', 'type': 'PRODUCT'}}]\"\n", ")\n", "\n", "articles_with_entities = [] # Initialize\n", "MAX_ARTICLES_FOR_LLM_NER = 3 # Process a very small number for this demo\n", "\n", "if cleaned_articles and client and llm_selected_entity_types_str and \"LLM_\" not in llm_selected_entity_types_str:\n", "    # Prepare the system prompt with the dynamically selected entity types\n", "    ner_system_prompt = LLM_NER_SYSTEM_PROMPT_TEMPLATE.format(entity_types_list_str=llm_selected_entity_types_str)\n", "    \n", "    # Determine the number of articles to process for NER\n", "    num_articles_to_process_ner = min(len(cleaned_articles), MAX_ARTICLES_FOR_LLM_NER)\n", "    print(f\"Starting LLM NER for {num_articles_to_process_ner} articles...\")\n", "    \n", "    for i, article_dict in enumerate(tqdm(cleaned_articles[:num_articles_to_process_ner], desc=\"LLM NER Processing\")):\n", "        print(f\"\\nProcessing article ID: {article_dict['id']} for NER with LLM ({i+1}/{num_articles_to_process_ner})...\")\n", "        \n", "        # Truncate article text if it's too long (e.g., > 3000 words for some models)\n", "        # Character count is a rough proxy for token count. Adjust as needed based on model limits.\n", "        max_text_chars = 12000 # Approx 3000 words. Should be safe for many models.\n", "        article_text_for_llm = article_dict['cleaned_text'][:max_text_chars]\n", "        if len(article_dict['cleaned_text']) > max_text_chars:\n", "            print(f\"  Warning: Article text truncated from {len(article_dict['cleaned_text'])} to {max_text_chars} characters for LLM NER.\")\n", "\n", "        llm_ner_raw_output = call_llm_for_response(ner_system_prompt, article_text_for_llm)\n", "        extracted_entities_list = parse_llm_json_output(llm_ner_raw_output)\n", "        \n", "        # Store results\n", "        current_article_data = article_dict.copy() # Make a copy to avoid modifying original list items directly\n", "        current_article_data['llm_entities'] = extracted_entities_list\n", "        articles_with_entities.append(current_article_data)\n", "        \n", "        print(f\"  Extracted {len(extracted_entities_list)} entities for article ID {article_dict['id']}.\")\n", "        if extracted_entities_list:\n", "            # Print a sample of entities, max 3\n", "            print(f\"  Sample entities: {json.dumps(extracted_entities_list[:min(3, len(extracted_entities_list))], indent=2)}\")\n", "        \n", "        if i < num_articles_to_process_ner - 1: # Avoid sleeping after the last article\n", "            time.sleep(1) # Small delay to be polite to <PERSON>\n", "            \n", "    if articles_with_entities:\n", "        print(f\"\\nFinished LLM NER. Processed {len(articles_with_entities)} articles and stored entities.\")\n", "else:\n", "    print(\"Skipping LLM NER: Prerequisites (cleaned articles, LLM client, or valid entity types string) not met.\")\n", "    # If NER is skipped, ensure articles_with_entities is populated with empty entity lists for subsequent steps\n", "    if cleaned_articles: # only if we have cleaned articles to begin with\n", "        num_articles_to_fallback = min(len(cleaned_articles), MAX_ARTICLES_FOR_LLM_NER)\n", "        for article_dict_fallback in cleaned_articles[:num_articles_to_fallback]:\n", "            fallback_data = article_dict_fallback.copy()\n", "            fallback_data['llm_entities'] = []\n", "            articles_with_entities.append(fallback_data)\n", "        print(f\"Populated 'articles_with_entities' with {len(articles_with_entities)} entries having empty 'llm_entities' lists.\")\n", "\n", "# Ensure articles_with_entities is defined\n", "if 'articles_with_entities' not in globals():\n", "    articles_with_entities = []\n", "    print(\"Initialized 'articles_with_entities' as an empty list.\")"]}, {"cell_type": "markdown", "id": "llm-ner-exec-output", "metadata": {}, "source": ["**Output Explanation:**\n", "This block will show the progress of LLM-based NER:\n", "*   For each processed article: its ID, a message about truncation (if any), the number of entities extracted, and a sample of the extracted entities in JSON format.\n", "*   A final message indicating completion or why the step was skipped.\n", "The `articles_with_entities` list now contains the original article data plus a new key `llm_entities` holding the list of entities extracted by the LLM for that article."]}, {"cell_type": "markdown", "id": "step2-2-desc", "metadata": {}, "source": ["### Step 2.2: Relationship Extraction\n", "**Task:** Identify semantic relationships between extracted entities, such as acquisition events or affiliations.\n", "\n", "**Book Concept:** (Ch. 2 - Relationships as edges)\n", "Relationships define how entities are connected, forming the *edges* in our Knowledge Graph. Extracting these relationships is crucial for capturing the actual knowledge (e.g., \"Company A *acquired* Company B\", \"Acquisition *has_price* $X Million\").\n", "\n", "**Methodology:**\n", "Similar to NER, we'll use the LLM for Relationship Extraction (RE). For each article:\n", "1.  **System Prompt:** We design a system prompt that instructs the LLM to act as a relationship extraction expert for technology acquisitions. It specifies:\n", "    *   The desired relationship types (predicates) like `ACQUIRED`, `HAS_PRICE`, `ANNOUNCED_ON`, `ACQUIRER_IS`, `ACQUIRED_COMPANY_IS`, `INVOLVES_PRODUCT`. These are chosen to capture key aspects of an acquisition event.\n", "    *   The requirement that the subject and object of a relationship must be exact text spans from the list of entities provided for that article.\n", "    *   The desired output format: a JSON list of objects, each with `subject_text`, `subject_type`, `predicate`, `object_text`, and `object_type`.\n", "    *   An example of the output format.\n", "2.  **User Prompt:** The user prompt for each article will contain:\n", "    *   The article's `cleaned_text`.\n", "    *   The list of `llm_entities` extracted in the previous step for that specific article (serialized as a JSON string within the prompt).\n", "3.  **Processing Loop:** Iterate through `articles_with_entities`. If an article has entities:\n", "    *   Construct the user prompt.\n", "    *   Call the LLM.\n", "    *   Parse the JSON output.\n", "    *   Optionally, validate that the subject/object texts in the extracted relations indeed come from the provided entity list to maintain consistency.\n", "    *   Store the extracted relations in a new list, `articles_with_relations` (each item will be the article data augmented with `llm_relations`)."]}, {"cell_type": "code", "execution_count": 15, "id": "relationship-extraction-exec-code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting LLM Relationship Extraction for 3 articles (that have entities)...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4a06030691d84109b131e4b83754c510", "version_major": 2, "version_minor": 0}, "text/plain": ["LLM Relationship Extraction:   0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Processing article ID: 56d7d67bb0fc32ee71cc006b915244776d883661 for relationships (1/3)...\n", "  No entities found for article ID 56d7d67bb0fc32ee71cc006b915244776d883661, skipping relationship extraction for this article.\n", "\n", "Processing article ID: 4cf51ce9372dff8ff7f44f098eab1c1d7569af7a for relationships (2/3)...\n", "\n", "Calling LLM (model: deepseek-ai/DeepSeek-V3, temperature: 0.2)...\n", "LLM response received.\n", "  Extracted 0 valid relationships for article ID 4cf51ce9372dff8ff7f44f098eab1c1d7569af7a.\n", "\n", "Processing article ID: 82a0e1f034174079179821b052f33df76c781b47 for relationships (3/3)...\n", "  No entities found for article ID 82a0e1f034174079179821b052f33df76c781b47, skipping relationship extraction for this article.\n", "\n", "Finished LLM Relationship Extraction. Processed 3 articles and stored relations.\n"]}], "source": ["RELATIONSHIP_EXTRACTION_SYSTEM_PROMPT_TEMPLATE = (\n", "    \"You are an expert system for extracting relationships between entities from text, specifically focusing on **technology company acquisitions**. \"\n", "    \"Example: [{{'subject_text': 'Innovatech Ltd.', 'subject_type': 'ORG', 'predicate': 'ACQUIRED', 'object_text': 'Global Solutions Inc.', 'object_type': 'ORG'}}, {{'subject_text': 'Global Solutions Inc.', 'subject_type': 'ORG', 'predicate': 'HAS_PRICE', 'object_text': '$250M', 'object_type': 'MONEY'}}] \"\n", "    \"If no relevant relationships of the specified types are found between the provided entities, output an empty JSON list []. Do not output any other text or explanation.\"\n", ")\n", "\n", "articles_with_relations = [] # Initialize\n", "\n", "if articles_with_entities and client: # Proceed if we have entities and LLM client\n", "    relation_system_prompt = RELATIONSHIP_EXTRACTION_SYSTEM_PROMPT_TEMPLATE # System prompt is constant for this task\n", "    \n", "    print(f\"Starting LLM Relationship Extraction for {len(articles_with_entities)} articles (that have entities)...\")\n", "    for i, article_data_with_ents in enumerate(tqdm(articles_with_entities, desc=\"LLM Relationship Extraction\")):\n", "        print(f\"\\nProcessing article ID: {article_data_with_ents['id']} for relationships ({i+1}/{len(articles_with_entities)})...\")\n", "        article_text_content = article_data_with_ents['cleaned_text']\n", "        entities_for_article = article_data_with_ents['llm_entities']\n", "        \n", "        if not entities_for_article: # If an article had no entities extracted\n", "            print(f\"  No entities found for article ID {article_data_with_ents['id']}, skipping relationship extraction for this article.\")\n", "            current_article_output = article_data_with_ents.copy()\n", "            current_article_output['llm_relations'] = []\n", "            articles_with_relations.append(current_article_output)\n", "            continue\n", "\n", "        # Truncate article text for LLM context, similar to NER step\n", "        max_text_chars_re = 10000 # Slightly less than NER to accommodate entity list in prompt\n", "        article_text_for_llm_re = article_text_content[:max_text_chars_re]\n", "        if len(article_text_content) > max_text_chars_re:\n", "            print(f\"  Warning: Article text truncated from {len(article_text_content)} to {max_text_chars_re} characters for LLM RE.\")\n", "            \n", "        # Serialize entities list to a JSON string for the prompt\n", "        # Ensure quotes within entity text are escaped for valid JSON string in prompt\n", "        entities_for_prompt_str = json.dumps([{'text': e['text'], 'type': e['type']} for e in entities_for_article])\n", "        \n", "        user_prompt_for_relations = (\n", "            f\"Article Text:\\n```\\n{article_text_for_llm_re}\\n```\\n\\n\" \n", "            f\"Extracted Entities (use these exact texts for subjects/objects):\\n```json\\n{entities_for_prompt_str}\\n```\\n\\n\" \n", "            f\"Identify and extract relationships between these entities based on the system instructions.\"\n", "        )\n", "        \n", "        llm_relations_raw_output = call_llm_for_response(relation_system_prompt, user_prompt_for_relations)\n", "        extracted_relations_list = parse_llm_json_output(llm_relations_raw_output)\n", "        \n", "        # Optional: Validate that subject/object texts in relations are from the provided entities\n", "        valid_relations_list = []\n", "        entity_texts_in_article = {e['text'] for e in entities_for_article}\n", "        for rel in extracted_relations_list:\n", "            # Check structure and presence of texts\n", "            if isinstance(rel, dict) and rel.get('subject_text') in entity_texts_in_article and rel.get('object_text') in entity_texts_in_article:\n", "                valid_relations_list.append(rel)\n", "            else:\n", "                print(f\"  Warning: Discarding relation due to missing fields or mismatched entity text: {str(rel)[:100]}...\")\n", "        \n", "        current_article_output = article_data_with_ents.copy()\n", "        current_article_output['llm_relations'] = valid_relations_list\n", "        articles_with_relations.append(current_article_output)\n", "        \n", "        print(f\"  Extracted {len(valid_relations_list)} valid relationships for article ID {article_data_with_ents['id']}.\")\n", "        if valid_relations_list:\n", "            print(f\"  Sample relations: {json.dumps(valid_relations_list[:min(2, len(valid_relations_list))], indent=2)}\")\n", "        \n", "        if i < len(articles_with_entities) - 1:\n", "             time.sleep(1) # Small delay\n", "            \n", "    if articles_with_relations:\n", "        print(f\"\\nFinished LLM Relationship Extraction. Processed {len(articles_with_relations)} articles and stored relations.\")\n", "else:\n", "    print(\"Skipping LLM Relationship Extraction: Prerequisites (articles with entities, LLM client) not met.\")\n", "    # If RE is skipped, populate articles_with_relations with empty relation lists\n", "    if articles_with_entities: # only if we have articles from NER step\n", "        for article_data_fallback_re in articles_with_entities:\n", "            fallback_data_re = article_data_fallback_re.copy()\n", "            fallback_data_re['llm_relations'] = []\n", "            articles_with_relations.append(fallback_data_re)\n", "        print(f\"Populated 'articles_with_relations' with {len(articles_with_relations)} entries having empty 'llm_relations' lists.\")\n", "\n", "# Ensure articles_with_relations is defined\n", "if 'articles_with_relations' not in globals():\n", "    articles_with_relations = []\n", "    print(\"Initialized 'articles_with_relations' as an empty list.\")"]}, {"cell_type": "markdown", "id": "relationship-extraction-exec-output", "metadata": {}, "source": ["**Output Explanation:**\n", "This block will show the progress of LLM-based Relationship Extraction:\n", "*   For each processed article: its ID, number of relationships extracted, and a sample of these relations in JSON format.\n", "*   Warnings if any extracted relations are discarded due to validation failures.\n", "*   A final message indicating completion or why the step was skipped.\n", "The `articles_with_relations` list now contains items that have `llm_entities` and `llm_relations`."]}, {"cell_type": "markdown", "id": "phase3", "metadata": {}, "source": ["## Phase 3: Knowledge Graph Construction\n", "**(Ref: Ch. 2 – KG Layers; Ch. 4 – Mapping and Materialization)**\n", "\n", "**Theory (Phase Overview):**\n", "Having extracted entities and relationships, this phase focuses on formally constructing the Knowledge Graph. This involves several key sub-tasks:\n", "*   **Entity Disambiguation & Linking:** Ensuring that different textual mentions of the same real-world entity are resolved to a single, canonical identifier (URI). This is crucial for graph coherence and data integration (Ch. 6, Ch. 8 concepts like COMET).\n", "*   **Schema/Ontology Alignment:** Mapping the extracted entity types and relationship predicates to a predefined schema or ontology (Ch. 2 - Ontology Layer; Ch. 4 - R2RML-like mapping). This provides semantic structure and enables interoperability and reasoning.\n", "*   **Triple Generation:** Converting the structured entity and relation data into Subject-Predicate-Object (S-P-O) triples, the fundamental data model of RDF-based KGs (Ch. 2, Ch. 4 - RML output).\n", "The output of this phase is a populated `rdflib.Graph` object."]}, {"cell_type": "markdown", "id": "step3-1-normalize-entity-text-func-def-desc", "metadata": {}, "source": ["### Step 3.1: Entity Disambiguation & Linking (Simplified) - Normalization Function\n", "**Task:** Resolve different mentions of the same real-world entity.\n", "\n", "**Book Concept:** (Ch. 6 - Entity Resolution; Ch. 8 - Context-aware linking)\n", "True entity disambiguation and linking (EDL) is a complex NLP task, often involving linking entities to large external KGs like Wikidata or DBpedia, or using sophisticated clustering and coreference resolution. \n", "\n", "**Methodology (Simplified):**\n", "For this demonstration, we'll perform a simplified version: **text normalization**. The `normalize_entity_text` function will:\n", "*   Trim whitespace.\n", "*   For `ORG` entities, attempt to remove common corporate suffixes (e.g., \"Inc.\", \"Ltd.\", \"Corp.\") to group variations like \"Example Corp\" and \"Example Corporation\" under a common normalized form like \"Example\".\n", "*   (Optionally, one might consider lowercasing, but this can sometimes lose important distinctions, e.g., between \"IT\" the pronoun and \"IT\" the sector).\n", "This normalized text will then be used to create a unique URI for each distinct entity."]}, {"cell_type": "code", "execution_count": 16, "id": "normalize_entity_text_func_def", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Function 'normalize_entity_text' defined.\n"]}], "source": ["def normalize_entity_text(text_to_normalize, entity_type_str):\n", "    \"\"\"Normalizes entity text for better linking (simplified version).\"\"\"\n", "    normalized_t = text_to_normalize.strip()\n", "    \n", "    if entity_type_str == 'ORG':\n", "        # Common suffixes to remove for ORG names. Order can matter for nested suffixes.\n", "        # More comprehensive list would be needed for production.\n", "        suffixes = [\n", "            'Inc.', 'Incorporated', 'Ltd.', 'Limited', 'LLC', 'L.L.C.', \n", "            'Corp.', 'Corporation', 'PLC', 'Public Limited Company', \n", "            'GmbH', 'AG', 'S.A.', 'S.A.S.', 'B.V.', 'Pty Ltd', 'Co.', 'Company',\n", "            'Solutions', 'Technologies', 'Systems', 'Group', 'Holdings'\n", "        ]\n", "        # Sort suffixes by length descending to remove longer matches first (e.g., 'Pty Ltd' before 'Ltd')\n", "        suffixes.sort(key=len, reverse=True)\n", "        for suffix in suffixes:\n", "            # Case-insensitive suffix removal from the end of the string\n", "            if normalized_t.lower().endswith(suffix.lower()):\n", "                # Find the actual start of the suffix to preserve casing of the main name\n", "                suffix_start_index = normalized_t.lower().rfind(suffix.lower())\n", "                normalized_t = normalized_t[:suffix_start_index].strip()\n", "                break # Remove one suffix type, then re-evaluate if more complex logic is needed\n", "        \n", "        # Remove trailing commas or periods that might be left after suffix removal\n", "        normalized_t = re.sub(r'[-,.]*$', '', normalized_t).strip()\n", "        \n", "    # General cleaning: remove possessives like 's sometimes caught by NER\n", "    if normalized_t.endswith(\"'s\") or normalized_t.endswith(\"s'\"):\n", "        normalized_t = normalized_t[:-2].strip()\n", "        \n", "    # Consider lowercasing carefully. For ORGs it might be okay, for PERSONs less so.\n", "    # For this demo, we'll keep original casing for the most part after suffix stripping.\n", "    # normalized_t = normalized_t.lower() # Uncomment if aggressive normalization is desired\n", "    \n", "    return normalized_t.strip() if normalized_t else text_to_normalize # Return original if normalization results in empty\n", "\n", "print(\"Function 'normalize_entity_text' defined.\")"]}, {"cell_type": "markdown", "id": "normalize_entity_text_func_def_output", "metadata": {}, "source": ["**Output Explanation:**\n", "Confirms the definition of the `normalize_entity_text` function."]}, {"cell_type": "markdown", "id": "entity-normalization-exec-desc", "metadata": {}, "source": ["#### Execute Entity Normalization and URI Generation\n", "\n", "**Theory:**\n", "This block processes the `articles_with_relations` list. For each entity extracted by the LLM:\n", "1.  Its text is normalized using `normalize_entity_text`.\n", "2.  A unique URI (Uniform Resource Identifier) is generated for each distinct normalized entity. We use a simple scheme: `EX:<NormalizedText>_<EntityType>`. The `EX` is our custom namespace. This creates a canonical identifier for each unique real-world concept (as per our normalization). A `unique_entities_map` dictionary stores the mapping from `(normalized_text, entity_type)` to its URI, ensuring that the same normalized entity always gets the same URI across all articles.\n", "3.  The normalized text and the URI are added to the entity's dictionary.\n", "The results are stored in `articles_with_normalized_entities`."]}, {"cell_type": "code", "execution_count": 17, "id": "entity-normalization-exec-code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Normalizing entities and preparing for triple generation...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7e3112e4d60f4ffea53ceea5e7230a73", "version_major": 2, "version_minor": 0}, "text/plain": ["Normalizing Entities & URI Gen:   0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Processed 3 articles for entity normalization and URI generation.\n", "Total unique canonical entity URIs created: 23\n"]}], "source": ["articles_with_normalized_entities = [] # Initialize\n", "unique_entities_map = {} # Maps (normalized_text, type) -> URI, to ensure URI consistency\n", "\n", "if articles_with_relations: # Process only if we have articles with relations (which implies entities)\n", "    print(\"Normalizing entities and preparing for triple generation...\")\n", "    for article_data_rel in tqdm(articles_with_relations, desc=\"Normalizing Entities & URI Gen\"):\n", "        current_article_normalized_ents = []\n", "        # Ensure 'llm_entities' key exists and is a list\n", "        if 'llm_entities' in article_data_rel and isinstance(article_data_rel['llm_entities'], list):\n", "            for entity_dict in article_data_rel['llm_entities']:\n", "                # Ensure entity_dict is a dictionary with 'text' and 'type'\n", "                if not (isinstance(entity_dict, dict) and 'text' in entity_dict and 'type' in entity_dict):\n", "                    print(f\"  Skipping malformed entity object: {str(entity_dict)[:100]} in article {article_data_rel['id']}\")\n", "                    continue\n", "\n", "                original_entity_text = entity_dict['text']\n", "                entity_type_val = entity_dict['type'] \n", "                # LLM might return type with description, e.g. \"ORG (Company)\". We need just \"ORG\".\n", "                # We'll take the first word as the type for simplicity, assuming it's the label like ORG, PERSON.\n", "                simple_entity_type = entity_type_val.split(' ')[0].upper()\n", "                entity_dict['simple_type'] = simple_entity_type # Store the simplified type\n", "                \n", "                normalized_entity_text = normalize_entity_text(original_entity_text, simple_entity_type)\n", "                if not normalized_entity_text: # if normalization resulted in empty string, use original\n", "                    normalized_entity_text = original_entity_text \n", "                \n", "                # Create a unique key for the map using normalized text and simple type\n", "                entity_map_key = (normalized_entity_text, simple_entity_type)\n", "                \n", "                if entity_map_key not in unique_entities_map:\n", "                    # Sanitize text for URI: replace spaces and special characters not allowed in URIs\n", "                    # A more robust IRI->URI conversion might be needed for non-ASCII characters.\n", "                    safe_uri_text_part = re.sub(r'[^a-zA-Z0-9_\\-]', '_', normalized_entity_text.replace(' ', '_'))\n", "                    # Prevent extremely long URIs from very long entity texts (rare but possible)\n", "                    safe_uri_text_part = safe_uri_text_part[:80] \n", "                    if not safe_uri_text_part: # If sanitization results in empty string, use a hash or generic id\n", "                        import hashlib\n", "                        safe_uri_text_part = f\"entity_{hashlib.md5(normalized_entity_text.encode()).hexdigest()[:8]}\"\n", "                    unique_entities_map[entity_map_key] = EX[f\"{safe_uri_text_part}_{simple_entity_type}\"]\n", "                \n", "                # Update the entity dictionary\n", "                entity_dict_copy = entity_dict.copy()\n", "                entity_dict_copy['normalized_text'] = normalized_entity_text\n", "                entity_dict_copy['uri'] = unique_entities_map[entity_map_key]\n", "                current_article_normalized_ents.append(entity_dict_copy)\n", "        \n", "        # Store results for the current article\n", "        article_data_output_norm = article_data_rel.copy()\n", "        article_data_output_norm['normalized_entities'] = current_article_normalized_ents\n", "        articles_with_normalized_entities.append(article_data_output_norm)\n", "    \n", "    if articles_with_normalized_entities and articles_with_normalized_entities[0].get('normalized_entities'):\n", "        print(\"\\nExample of first article's normalized entities (first 3):\")\n", "        for ent_example in articles_with_normalized_entities[0]['normalized_entities'][:3]:\n", "            print(f\"  Original: '{ent_example['text']}', Type: {ent_example['type']} (Simple: {ent_example['simple_type']}), Normalized: '{ent_example['normalized_text']}', URI: <{ent_example['uri']}>\")\n", "    print(f\"\\nProcessed {len(articles_with_normalized_entities)} articles for entity normalization and URI generation.\")\n", "    print(f\"Total unique canonical entity URIs created: {len(unique_entities_map)}\")\n", "else:\n", "    print(\"Skipping entity normalization and URI generation: No articles with relations available.\")\n", "\n", "# Ensure articles_with_normalized_entities is defined\n", "if 'articles_with_normalized_entities' not in globals():\n", "    articles_with_normalized_entities = []\n", "    print(\"Initialized 'articles_with_normalized_entities' as an empty list.\")"]}, {"cell_type": "markdown", "id": "entity-normalization-exec-output", "metadata": {}, "source": ["**Output Explanation:**\n", "This block will show:\n", "*   Progress of the normalization and URI generation process.\n", "*   Examples of original entity text vs. their normalized versions and the generated URIs for the first few entities in the first processed article.\n", "*   The total count of unique entity URIs created across all processed articles."]}, {"cell_type": "markdown", "id": "step3-2-rdf-type-func-def-desc", "metadata": {}, "source": ["### Step 3.2: Schema/Ontology Alignment - RDF Type Mapping Function\n", "**Task:** Map extracted entities and relationships to a consistent schema or ontology.\n", "\n", "**Book Concept:** (Ch. 2 - Ontology Layer; Ch. 4 - Mapping)\n", "Schema/Ontology alignment involves mapping our locally defined entity types (e.g., \"ORG\", \"PERSON\" from the LLM NER step) and relationship predicates to standard vocabularies (like Schema.org) or custom-defined RDF classes and properties. This adds semantic rigor and enables interoperability.\n", "\n", "**Methodology:**\n", "The `get_rdf_type_for_entity` function takes our simple entity type string (e.g., \"ORG\") and maps it to an RDF Class. \n", "*   It uses a predefined dictionary (`type_mapping`) to link common types to `SCHEMA` (Schema.org) classes (e.g., `ORG` -> `SCHEMA.Organization`).\n", "*   If a type is not in the map, it defaults to creating a class within our custom `EX` namespace (e.g., `EX.CUSTOM_TYPE`).\n", "This function ensures that each entity in our KG will be assigned a formal RDF type."]}, {"cell_type": "code", "execution_count": 18, "id": "get_rdf_type_for_entity_func_def", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Function 'get_rdf_type_for_entity' defined.\n"]}], "source": ["def get_rdf_type_for_entity(simple_entity_type_str):\n", "    \"\"\"Maps our simple entity type string (e.g., 'ORG') to an RDF Class.\"\"\"\n", "    type_mapping = {\n", "        'ORG': SCHEMA.Organization,\n", "        'PERSON': SCHEMA.Person,\n", "        'MONEY': SCHEMA.PriceSpecification, # Or a custom EX.MonetaryValue\n", "        'DATE': SCHEMA.Date, # Note: schema.org/Date is a datatype, consider schema.org/Event for events with dates\n", "        'PRODUCT': SCHEMA.Product,\n", "        'GPE': SCHEMA.Place,    # Geopolitical Entity\n", "        'LOC': SCHEMA.Place,    # General Location\n", "        'EVENT': SCHEMA.Event,\n", "        # Add other mappings as derived from llm_selected_entity_types_str if needed\n", "        'CARDINAL': RDF.Statement, # Or more specific if context known, often just a literal value\n", "        'FAC': SCHEMA.Place # Facility\n", "    }\n", "    return type_mapping.get(simple_entity_type_str.upper(), EX[simple_entity_type_str.upper()]) # Fallback to custom type\n", "\n", "print(\"Function 'get_rdf_type_for_entity' defined.\")"]}, {"cell_type": "markdown", "id": "get_rdf_type_for_entity_func_def_output", "metadata": {}, "source": ["**Output Explanation:**\n", "Confirms the definition of the `get_rdf_type_for_entity` mapping function."]}, {"cell_type": "markdown", "id": "step3-2-rdf-predicate-func-def-desc", "metadata": {}, "source": ["#### Schema/Ontology Alignment - RDF Predicate Mapping Function\n", "\n", "**Theory:**\n", "The `get_rdf_predicate` function maps our string-based relationship predicates (e.g., \"ACQUIRED\", \"HAS_PRICE\" from the LLM RE step) to RDF Properties. For simplicity and custom control, these are typically mapped to properties within our `EX` namespace. The function ensures that predicate strings are converted into valid URI components (e.g., by replacing spaces with underscores)."]}, {"cell_type": "code", "execution_count": 19, "id": "get_rdf_predicate_func_def", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Function 'get_rdf_predicate' defined.\n"]}], "source": ["def get_rdf_predicate(predicate_str_from_llm):\n", "    \"\"\"Maps our predicate string (from LLM relation extraction) to an RDF Property in our EX namespace.\"\"\"\n", "    # Predicates are already somewhat like properties (e.g., 'ACQUIRED')\n", "    # We'll ensure they are valid URI components for our custom namespace EX\n", "    # Replace spaces with underscores and ensure it's a simple, clean string\n", "    sanitized_predicate = predicate_str_from_llm.strip().replace(\" \", \"_\").upper()\n", "    return EX[sanitized_predicate]\n", "\n", "print(\"Function 'get_rdf_predicate' defined.\")"]}, {"cell_type": "markdown", "id": "get_rdf_predicate_func_def_output", "metadata": {}, "source": ["**Output Explanation:**\n", "Confirms the definition of the `get_rdf_predicate` mapping function."]}, {"cell_type": "markdown", "id": "schema-alignment-example-desc", "metadata": {}, "source": ["#### Schema/Ontology Alignment - Examples\n", "\n", "**Theory:**\n", "This block simply prints out a few examples of how our entity types and relationship predicates would be mapped to RDF terms using the functions defined above. This serves as a quick check and illustration of the mapping logic."]}, {"cell_type": "code", "execution_count": 20, "id": "schema-alignment-example-code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Schema alignment functions ready. Example mappings:\n", "  Entity Type 'ORG' maps to RDF Class: <http://schema.org/Organization>\n", "  Predicate 'ACQUIRED' maps to RDF Property: <http://example.org/kg/ACQUIRED>\n", "  Entity Type 'MONEY' maps to RDF Class: <http://schema.org/PriceSpecification>\n", "  Predicate 'HAS_PRICE' maps to RDF Property: <http://example.org/kg/HAS_PRICE>\n"]}], "source": ["print(\"Schema alignment functions ready. Example mappings:\")\n", "example_entity_type = 'ORG'\n", "example_predicate_str = 'ACQUIRED'\n", "print(f\"  Entity Type '{example_entity_type}' maps to RDF Class: <{get_rdf_type_for_entity(example_entity_type)}>\")\n", "print(f\"  Predicate '{example_predicate_str}' maps to RDF Property: <{get_rdf_predicate(example_predicate_str)}>\")\n", "\n", "example_entity_type_2 = 'MONEY'\n", "example_predicate_str_2 = 'HAS_PRICE'\n", "print(f\"  Entity Type '{example_entity_type_2}' maps to RDF Class: <{get_rdf_type_for_entity(example_entity_type_2)}>\")\n", "print(f\"  Predicate '{example_predicate_str_2}' maps to RDF Property: <{get_rdf_predicate(example_predicate_str_2)}>\")"]}, {"cell_type": "markdown", "id": "schema-alignment-example-output", "metadata": {}, "source": ["**Output Explanation:**\n", "Shows example RDF URIs that would be generated for sample entity types and predicate strings, illustrating the mapping functions."]}, {"cell_type": "markdown", "id": "step3-3-triple-generation-exec-desc", "metadata": {}, "source": ["### Step 3.3: Triple Generation\n", "**Task:** Convert the structured entity and relation data into subject–predicate–object triples.\n", "\n", "**Book Concept:** (Ch. 2 - KG structure; Ch. 4 - RML output)\n", "This is where the Knowledge Graph materializes. We iterate through our processed articles (`articles_with_normalized_entities`) and convert the extracted information into RDF triples using `rdflib`.\n", "\n", "**Methodology:**\n", "1.  **Initialize Graph:** An `rdflib.Graph` object (`kg`) is created.\n", "2.  **Bind Namespaces:** Namespaces (EX, SCHEMA, RDFS, etc.) are bound to prefixes for cleaner serialization of the RDF (e.g., `ex:AcmeCorp` instead of the full URI).\n", "3.  **Iterate Articles:** For each article:\n", "    *   An RDF resource is created for the article itself (e.g., `ex:article_123`), typed as `schema:Article`.\n", "    *   Its summary (or ID) can be added as a `schema:headline` or `rdfs:label`.\n", "4.  **Iterate Entities:** For each `normalized_entity` within an article:\n", "    *   The entity's URI (from `entity['uri']`) is used as the subject.\n", "    *   A triple `(entity_uri, rdf:type, rdf_entity_type)` is added, where `rdf_entity_type` comes from `get_rdf_type_for_entity()`.\n", "    *   A triple `(entity_uri, rdfs:label, Literal(normalized_text))` is added to provide a human-readable label.\n", "    *   If the original text differs from the normalized text, `(entity_uri, skos:altLabel, Literal(original_text))` can be added for the original mention.\n", "    *   A triple `(article_uri, schema:mentions, entity_uri)` links the article to the entities it mentions.\n", "    *   A local map (`entity_uri_map_for_article`) is built for the current article, mapping original entity texts to their canonical URIs. This is crucial for resolving relations in the next step, as relations were extracted based on original text spans.\n", "5.  **Iterate Relations:** For each `llm_relation` within an article:\n", "    *   The URIs for the subject and object entities are looked up from `entity_uri_map_for_article` using their original text spans.\n", "    *   The predicate string is converted to an RDF property using `get_rdf_predicate()`.\n", "    *   If both subject and object URIs are found, the triple `(subject_uri, predicate_rdf, object_uri)` is added to the graph.\n", "\n", "**Output:** A populated `rdflib.Graph` (`kg`) containing all the extracted knowledge as RDF triples."]}, {"cell_type": "code", "execution_count": 21, "id": "triple-generation-exec-code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generating RDF triples for 3 processed articles...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "818e44fbf2b24002a0c9ff572e0266ed", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating Triples:   0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Finished generating triples. Approximately 75 triples were candidates for addition.\n", "Total actual triples in the graph: 75\n", "\n", "Sample of first 5 triples in N3 format:\n", "  ex:article_4cf51ce9372dff8ff7f44f098eab1c1d7569af7a schema1:mentions ex:Tunisia_GPE.\n", "  ex:<PERSON><PERSON><PERSON>_PERSON rdf:type schema1:Person.\n", "  ex:<PERSON><PERSON>_PERSON rdfs:label \"<PERSON>\"@en.\n", "  ex:article_56d7d67bb0fc32ee71cc006b915244776d883661 rdf:type schema1:Article.\n", "  ex:article_4cf51ce9372dff8ff7f44f098eab1c1d7569af7a schema1:mentions ex:<PERSON>_<PERSON><PERSON>_PERSON.\n"]}], "source": ["kg = Graph() # Initialize an empty RDF graph\n", "# Bind namespaces for prettier RDF serialization (e.g., in Turtle files)\n", "kg.bind(\"ex\", EX)\n", "kg.bind(\"schema\", SCHEMA)\n", "kg.bind(\"rdf\", RDF)\n", "kg.bind(\"rdfs\", RDFS)\n", "kg.bind(\"xsd\", XSD)\n", "kg.bind(\"skos\", SKOS)\n", "\n", "triples_generated_count = 0\n", "\n", "if articles_with_normalized_entities:\n", "    print(f\"Generating RDF triples for {len(articles_with_normalized_entities)} processed articles...\")\n", "    for article_data_final in tqdm(articles_with_normalized_entities, desc=\"Generating Triples\"):\n", "        # Create a URI for the article itself\n", "        article_uri = EX[f\"article_{article_data_final['id'].replace('-', '_')}\"] # Sanitize ID for URI\n", "        kg.add((article_uri, RDF.type, SCHEMA.Article))\n", "        kg.add((article_uri, SCHEMA.headline, Literal(article_data_final.get('summary', article_data_final['id']))))\n", "        # Optionally add a link to the original text source if available/desired\n", "        # kg.add((article_uri, SCHEMA.mainEntityOfPage, URIRef(f\"http://datasource.example.com/articles/{article_data_final['id']}\")))\n", "        triples_generated_count += 2\n", "\n", "        # Create a local map for this article: original entity text -> canonical URI\n", "        # This is important because relations were extracted based on original text spans.\n", "        entity_text_to_uri_map_current_article = {}\n", "\n", "        # Add entity triples\n", "        for entity_obj in article_data_final.get('normalized_entities', []):\n", "            entity_uri_val = entity_obj['uri'] # This is the canonical URI from unique_entities_map\n", "            rdf_entity_type_val = get_rdf_type_for_entity(entity_obj['simple_type'])\n", "            normalized_label = entity_obj['normalized_text']\n", "            original_label = entity_obj['text']\n", "            \n", "            kg.add((entity_uri_val, RDF.type, rdf_entity_type_val))\n", "            kg.add((entity_uri_val, RDFS.label, Literal(normalized_label, lang='en')))\n", "            triples_generated_count += 2\n", "            if normalized_label != original_label:\n", "                 kg.add((entity_uri_val, SKOS.altLabel, Literal(original_label, lang='en')))\n", "                 triples_generated_count += 1\n", "            \n", "            # Link article to mentioned entities\n", "            kg.add((article_uri, SCHEMA.mentions, entity_uri_val))\n", "            triples_generated_count += 1\n", "            \n", "            # Populate the local map for resolving relations within this article\n", "            entity_text_to_uri_map_current_article[original_label] = entity_uri_val\n", "\n", "        # Add relation triples\n", "        for relation_obj in article_data_final.get('llm_relations', []):\n", "            subject_orig_text = relation_obj.get('subject_text')\n", "            object_orig_text = relation_obj.get('object_text')\n", "            predicate_str = relation_obj.get('predicate')\n", "            \n", "            # Resolve subject and object texts to their canonical URIs using the article-specific map\n", "            subject_resolved_uri = entity_text_to_uri_map_current_article.get(subject_orig_text)\n", "            object_resolved_uri = entity_text_to_uri_map_current_article.get(object_orig_text)\n", "            \n", "            if subject_resolved_uri and object_resolved_uri and predicate_str:\n", "                predicate_rdf_prop = get_rdf_predicate(predicate_str)\n", "                kg.add((subject_resolved_uri, predicate_rdf_prop, object_resolved_uri))\n", "                triples_generated_count += 1\n", "            else:\n", "                if not subject_resolved_uri:\n", "                    print(f\"  Warning: Could not find URI for subject '{subject_orig_text}' in article {article_data_final['id']}. Relation skipped: {relation_obj}\")\n", "                if not object_resolved_uri:\n", "                    print(f\"  Warning: Could not find URI for object '{object_orig_text}' in article {article_data_final['id']}. Relation skipped: {relation_obj}\")\n", "\n", "    print(f\"\\nFinished generating triples. Approximately {triples_generated_count} triples were candidates for addition.\")\n", "    print(f\"Total actual triples in the graph: {len(kg)}\")\n", "    if len(kg) > 0:\n", "        print(\"\\nSample of first 5 triples in N3 format:\")\n", "        for i, (s, p, o) in enumerate(kg):\n", "            # Use n3() for a readable representation respecting prefixes\n", "            print(f\"  {s.n3(kg.namespace_manager)} {p.n3(kg.namespace_manager)} {o.n3(kg.namespace_manager)}.\")\n", "            if i >= 4: # Print first 5\n", "                break\n", "else:\n", "    print(\"Skipping triple generation: No processed articles with normalized entities available.\")\n", "\n", "# Ensure kg is defined\n", "if 'kg' not in globals():\n", "    kg = Graph()\n", "    print(\"Initialized 'kg' as an empty rdflib.Graph object.\")"]}, {"cell_type": "markdown", "id": "triple-generation-exec-output", "metadata": {}, "source": ["**Output Explanation:**\n", "This block will show:\n", "*   Progress of triple generation.\n", "*   The approximate number of triples considered for addition and the final total number of triples in the `kg` graph.\n", "*   Warnings if subject/object entities for a relation couldn't be resolved to URIs.\n", "*   A sample of the first few triples in N3 (Notation3) format, which is a human-readable RDF serialization."]}, {"cell_type": "markdown", "id": "phase4", "metadata": {}, "source": ["## Phase 4: Knowledge Graph Refinement Using Embeddings\n", "**(Ref: Ch. 6 – Embedding-Based Reasoning; Ch. 7 – ML on KGs with SANSA)**\n", "\n", "**Theory (Phase Overview):**\n", "Knowledge Graph embeddings (KGEs) learn low-dimensional vector representations for entities and relations in a KG. These embeddings capture the semantic properties of KG components and their interactions. This phase explores using such embeddings for KG refinement, a concept aligned with embedding-based reasoning (Ch. 6).\n", "Key tasks include:\n", "*   **Generating Embeddings:** Creating vector representations for nodes (entities).\n", "*   **Link Prediction (Knowledge Discovery):** Using embeddings to infer missing connections or predict new potential relationships (Ch. 6). This is a powerful way to discover implicit knowledge and enrich the KG.\n", "While full KGE model training (like TransE, ComplEx, DistMult mentioned in Ch. 6) is beyond this demo's scope, we'll use pre-trained text embeddings for entity names as a proxy to demonstrate semantic similarity, a foundational concept for some link prediction approaches."]}, {"cell_type": "markdown", "id": "step4-1-embedding-func-def-desc", "metadata": {}, "source": ["### Step 4.1: Generate KG Embeddings - Embedding Function Definition\n", "**Task:** Create vector representations for nodes (entities) in the graph.\n", "\n", "**Book Concept:** (Ch. 6 - Embeddings bridging symbolic & sub-symbolic)\n", "Embeddings transform symbolic entities (represented by URIs and labels) into numerical vectors in a continuous vector space. This allows us to apply machine learning techniques and measure semantic similarity.\n", "\n", "**Methodology:**\n", "The `get_embeddings_for_texts` function will:\n", "*   Take a list of unique entity texts (e.g., their normalized labels).\n", "*   Use the configured OpenAI/Nebius embedding API (with `EMBEDDING_MODEL_NAME`) to fetch pre-trained embeddings for these texts.\n", "*   Handle batching or individual requests as appropriate for the API.\n", "*   Return a dictionary mapping each input text to its embedding vector.\n", "These embeddings represent the semantic meaning of the entity names."]}, {"cell_type": "code", "execution_count": 22, "id": "get_embeddings_for_texts_func_def", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Function 'get_embeddings_for_texts' defined.\n"]}], "source": ["def get_embeddings_for_texts(texts_list, embedding_model_name=EMBEDDING_MODEL_NAME):\n", "    \"\"\"Gets embeddings for a list of texts using the specified model via the LLM client.\"\"\"\n", "    if not client:\n", "        print(\"LLM client not initialized. Skipping embedding generation.\")\n", "        return {text: [] for text in texts_list} # Return dict with empty embeddings\n", "    if not texts_list:\n", "        print(\"No texts provided for embedding generation.\")\n", "        return {}\n", "    \n", "    embeddings_map_dict = {}\n", "    print(f\"Fetching embeddings for {len(texts_list)} unique texts using model '{embedding_model_name}'...\")\n", "    \n", "    # Process texts in batches to be efficient and respect potential API limits\n", "    # Some APIs handle list inputs directly, others might require batching.\n", "    # The OpenAI client's `embeddings.create` can often take a list of inputs.\n", "    # If your specific endpoint requires single inputs or smaller batches, adjust this loop.\n", "    # For this example, assuming the client can handle a list, but will iterate if not or for safety.\n", "\n", "    # Check if the input texts_list itself is a list of strings\n", "    if not all(isinstance(text, str) for text in texts_list):\n", "        print(\"Error: Input 'texts_list' must be a list of strings.\")\n", "        return {text: [] for text in texts_list if isinstance(text, str)} # Try to salvage what we can\n", "    \n", "    # Remove empty strings to avoid API errors\n", "    valid_texts_list = [text for text in texts_list if text.strip()]\n", "    if not valid_texts_list:\n", "        print(\"No valid (non-empty) texts to embed.\")\n", "        return {}\n", "\n", "    try:\n", "        # Assuming the client.embeddings.create can take a list of inputs.\n", "        # If it can only take one string at a time, you'd loop here.\n", "        response = client.embeddings.create(\n", "            model=embedding_model_name,\n", "            input=valid_texts_list # Pass the list of valid texts\n", "        )\n", "        # The response.data should be a list of embedding objects, in the same order as input\n", "        for i, data_item in enumerate(response.data):\n", "            embeddings_map_dict[valid_texts_list[i]] = data_item.embedding\n", "        \n", "        print(f\"Embeddings received for {len(embeddings_map_dict)} texts.\")\n", "        # For texts that were empty or failed, add them with empty embeddings if needed by caller\n", "        for text in texts_list:\n", "            if text not in embeddings_map_dict:\n", "                embeddings_map_dict[text] = []\n", "        return embeddings_map_dict\n", "    \n", "    except Exception as e:\n", "        print(f\"Error getting embeddings (batch attempt): {e}\")\n", "        print(\"Falling back to individual embedding requests if batch failed...\")\n", "        # Fallback to individual requests if batching failed or is not supported by the specific client/endpoint setup\n", "        embeddings_map_dict_fallback = {}\n", "        for text_input_item in tqdm(valid_texts_list, desc=\"Generating Embeddings (Fallback Mode)\"):\n", "            try:\n", "                response_item = client.embeddings.create(\n", "                    model=embedding_model_name,\n", "                    input=text_input_item\n", "                )\n", "                embeddings_map_dict_fallback[text_input_item] = response_item.data[0].embedding\n", "                if len(valid_texts_list) > 10: # Only sleep if processing many items\n", "                    time.sleep(0.1) # Small delay per request in fallback\n", "            except Exception as e_item:\n", "                print(f\"  Error getting embedding for text '{text_input_item[:50]}...': {e_item}\")\n", "                embeddings_map_dict_fallback[text_input_item] = [] # Store empty list on error for this item\n", "        \n", "        # For texts that were empty or failed, add them with empty embeddings if needed by caller\n", "        for text in texts_list:\n", "            if text not in embeddings_map_dict_fallback:\n", "                embeddings_map_dict_fallback[text] = []\n", "        return embeddings_map_dict_fallback\n", "\n", "print(\"Function 'get_embeddings_for_texts' defined.\")"]}, {"cell_type": "markdown", "id": "get_embeddings_for_texts_func_def_output", "metadata": {}, "source": ["**Output Explanation:**\n", "Confirms the definition of the `get_embeddings_for_texts` function."]}, {"cell_type": "markdown", "id": "kg-embedding-exec-desc", "metadata": {}, "source": ["#### Generate KG Embeddings - Execution\n", "\n", "**Theory:**\n", "This block orchestrates the generation of embeddings for our KG entities:\n", "1.  It extracts the set of unique, normalized entity texts from our `unique_entities_map` (which maps `(normalized_text, type)` to URIs). We are interested in embedding the textual representation of entities.\n", "2.  It calls `get_embeddings_for_texts` with this list of unique texts.\n", "3.  The returned embeddings (which are mapped to texts) are then re-mapped to our canonical entity URIs, creating the `entity_embeddings` dictionary: `{entity_uri: embedding_vector}`.\n", "This dictionary will store the vector representation for each unique entity in our graph."]}, {"cell_type": "code", "execution_count": 23, "id": "kg-embedding-exec-code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Preparing to fetch embeddings for 23 unique normalized entity texts.\n", "Fetching embeddings for 23 unique texts using model 'BAAI/bge-multilingual-gemma2'...\n", "Embeddings received for 23 texts.\n", "\n", "Successfully generated and mapped embeddings for 23 entity URIs.\n", "  Example embedding for URI <http://example.org/kg/United_Nations_ORG> (Label: 'United Nations'):\n", "    Vector (first 5 dims): [-0.00027251243591308594, 0.0006837844848632812, -0.01216888427734375, -0.01558685302734375, 0.0038166046142578125]...\n", "    Vector dimension: 3584\n"]}], "source": ["entity_embeddings = {} # Initialize: maps entity URI -> embedding vector\n", "\n", "if unique_entities_map and client: # Proceed if we have unique entities and LLM client\n", "    # Extract unique normalized entity texts for which we need embeddings.\n", "    # unique_entities_map maps (normalized_text, type) -> URI\n", "    # We need just the normalized_text part for embedding.\n", "    entity_normalized_texts_to_embed = list(set([key[0] for key in unique_entities_map.keys() if key[0].strip()]))\n", "    \n", "    if entity_normalized_texts_to_embed:\n", "        print(f\"Preparing to fetch embeddings for {len(entity_normalized_texts_to_embed)} unique normalized entity texts.\")\n", "        \n", "        # Get embeddings for these unique texts\n", "        text_to_embedding_map = get_embeddings_for_texts(entity_normalized_texts_to_embed)\n", "        \n", "        # Map these embeddings back to our entity URIs\n", "        for (normalized_text_key, entity_type_key), entity_uri_val_emb in unique_entities_map.items():\n", "            if normalized_text_key in text_to_embedding_map and text_to_embedding_map[normalized_text_key]:\n", "                entity_embeddings[entity_uri_val_emb] = text_to_embedding_map[normalized_text_key]\n", "            # else: # This case should be handled by get_embeddings_for_texts returning empty list for failed/empty texts\n", "                # print(f\"  Warning: No embedding found or generated for text '{normalized_text_key}' (URI: {entity_uri_val_emb})\")\n", "                \n", "        if entity_embeddings:\n", "            print(f\"\\nSuccessfully generated and mapped embeddings for {len(entity_embeddings)} entity URIs.\")\n", "            # Show an example\n", "            first_uri_with_embedding = next(iter(entity_embeddings.keys()), None)\n", "            if first_uri_with_embedding:\n", "                emb_example = entity_embeddings[first_uri_with_embedding]\n", "                # Get label for this URI from KG\n", "                label_for_uri = kg.value(subject=first_uri_with_embedding, predicate=RDFS.label, default=str(first_uri_with_embedding))\n", "                print(f\"  Example embedding for URI <{first_uri_with_embedding}> (Label: '{label_for_uri}'):\")\n", "                print(f\"    Vector (first 5 dims): {str(emb_example[:5])}...\")\n", "                print(f\"    Vector dimension: {len(emb_example)}\")\n", "        else:\n", "            print(\"No embeddings were successfully mapped to entity URIs.\")\n", "    else:\n", "        print(\"No unique entity texts found to generate embeddings for.\")\n", "else:\n", "    print(\"Skipping embedding generation: No unique entities identified, or LLM client not available.\")\n", "\n", "# Ensure entity_embeddings is defined\n", "if 'entity_embeddings' not in globals():\n", "    entity_embeddings = {}\n", "    print(\"Initialized 'entity_embeddings' as an empty dictionary.\")"]}, {"cell_type": "markdown", "id": "kg-embedding-exec-output", "metadata": {}, "source": ["**Output Explanation:**\n", "This block will show:\n", "*   Progress of fetching embeddings.\n", "*   The number of unique entity texts for which embeddings are requested.\n", "*   The number of entity URIs for which embeddings were successfully generated and mapped.\n", "*   An example of an embedding vector (first few dimensions and total length) for one of the entities, along with its URI and label."]}, {"cell_type": "markdown", "id": "step4-2-cosine-sim-func-def-desc", "metadata": {}, "source": ["### Step 4.2: <PERSON> Prediction (Knowledge Discovery - Conceptual) - Cosine Similarity Function\n", "**Task:** Use embeddings to infer new or missing connections.\n", "\n", "**Book Concept:** (Ch. 6 - Link prediction as reasoning)\n", "Link prediction aims to identify missing edges (triples) in a KG. KGE models are trained to score potential triples (s, p, o), and high-scoring triples not already in the KG are candidate new links. \n", "\n", "**Methodology (Simplified):**\n", "A full link prediction model is complex. Here, we'll demonstrate a simpler, related concept: **semantic similarity** between entities based on their name embeddings. The `get_cosine_similarity` function calculates the cosine similarity between two embedding vectors. High cosine similarity (close to 1) between entity name embeddings suggests that the entities are semantically related in terms of their textual description. This *could* hint at potential relationships (e.g., two similarly named software products might be competitors or complementary), but it's not direct link prediction for specific predicates."]}, {"cell_type": "code", "execution_count": 24, "id": "get_cosine_similarity_func_def", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Function 'get_cosine_similarity' defined.\n"]}], "source": ["def get_cosine_similarity(embedding1, embedding2):\n", "    \"\"\"Calculates cosine similarity between two embedding vectors using sklearn.\"\"\"\n", "    if not isinstance(embedding1, (list, np.ndarray)) or not isinstance(embedding2, (list, np.ndarray)):\n", "        # print(\"Warning: One or both embeddings are not lists/arrays.\")\n", "        return 0.0\n", "    if not embedding1 or not embedding2:\n", "        # print(\"Warning: One or both embeddings are empty.\")\n", "        return 0.0\n", "    \n", "    # Ensure they are numpy arrays and 2D for cosine_similarity function\n", "    vec1 = np.array(embedding1).reshape(1, -1)\n", "    vec2 = np.array(embedding2).reshape(1, -1)\n", "    \n", "    # Check if dimensions match, though reshape should handle 1D to 2D conversion.\n", "    # Cosine similarity doesn't strictly require same length for this usage, but it's implied for valid comparison.\n", "    if vec1.shape[1] != vec2.shape[1]:\n", "        print(f\"Warning: Embedding dimensions do not match for cosine similarity: {vec1.shape[1]} vs {vec2.shape[1]}\")\n", "        return 0.0 # Or handle as an error\n", "        \n", "    return cosine_similarity(vec1, vec2)[0][0]\n", "\n", "print(\"Function 'get_cosine_similarity' defined.\")"]}, {"cell_type": "markdown", "id": "get_cosine_similarity_func_def_output", "metadata": {}, "source": ["**Output Explanation:**\n", "Confirms the definition of the `get_cosine_similarity` function."]}, {"cell_type": "markdown", "id": "link-prediction-exec-desc", "metadata": {}, "source": ["#### Link Prediction (Conceptual) - Similarity Calculation Example\n", "\n", "**Theory:**\n", "This block demonstrates the use of `get_cosine_similarity`. It selects a couple of entities (preferably organizations, if available with embeddings) from our `entity_embeddings` map and calculates the similarity between their name embeddings. A high similarity might suggest they operate in similar domains or have related roles, which could be a starting point for investigating potential (unobserved) connections if we had a more sophisticated link prediction model."]}, {"cell_type": "code", "execution_count": 25, "id": "link-prediction-exec-code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Conceptual Link Prediction: Calculating semantic similarity between a sample of entities using their name embeddings.\n", "Found at least two ORG entities with embeddings for similarity comparison.\n", "\n", "  Similarity between 'United Nations' (<http://example.org/kg/United_Nations_ORG>) and 'CNN' (<http://example.org/kg/CNN_ORG>): 0.7679\n", "  Interpretation: These entities show moderate similarity based on their name embeddings.\n", "\n", "Note: This is a conceptual demonstration of semantic similarity. True link prediction involves training specialized KGE models (e.g., TransE, ComplEx) on existing graph triples to predict missing (subject, predicate, object) facts with specific predicates, not just general entity similarity.\n"]}], "source": ["if len(entity_embeddings) >= 2:\n", "    print(\"\\nConceptual Link Prediction: Calculating semantic similarity between a sample of entities using their name embeddings.\")\n", "    \n", "    # Get all URIs that have embeddings\n", "    uris_with_embeddings = [uri for uri, emb in entity_embeddings.items() if emb] # Check if emb is not empty\n", "    \n", "    # Try to find two ORG entities for a more meaningful comparison\n", "    org_entity_uris_with_embeddings = []\n", "    for uri_cand in uris_with_embeddings:\n", "        # Check the type from the KG\n", "        rdf_types_for_uri = list(kg.objects(subject=uri_cand, predicate=RDF.type))\n", "        if SCHEMA.Organization in rdf_types_for_uri or EX.ORG in rdf_types_for_uri:\n", "            org_entity_uris_with_embeddings.append(uri_cand)\n", "    \n", "    entity1_uri_sim = None\n", "    entity2_uri_sim = None\n", "\n", "    if len(org_entity_uris_with_embeddings) >= 2:\n", "        entity1_uri_sim = org_entity_uris_with_embeddings[0]\n", "        entity2_uri_sim = org_entity_uris_with_embeddings[1]\n", "        print(f\"Found at least two ORG entities with embeddings for similarity comparison.\")\n", "    elif len(uris_with_embeddings) >= 2: # Fallback to any two entities if not enough ORGs\n", "        entity1_uri_sim = uris_with_embeddings[0]\n", "        entity2_uri_sim = uris_with_embeddings[1]\n", "        print(f\"Could not find two ORGs with embeddings. Using two generic entities for similarity comparison.\")\n", "    else:\n", "        print(\"Not enough entities (less than 2) with valid embeddings to demonstrate similarity.\")\n", "\n", "    if entity1_uri_sim and entity2_uri_sim:\n", "        embedding1_val = entity_embeddings.get(entity1_uri_sim)\n", "        embedding2_val = entity_embeddings.get(entity2_uri_sim)\n", "        \n", "        # Retrieve labels for these URIs from the graph for context\n", "        label1_val = kg.value(subject=entity1_uri_sim, predicate=RDFS.label, default=str(entity1_uri_sim))\n", "        label2_val = kg.value(subject=entity2_uri_sim, predicate=RDFS.label, default=str(entity2_uri_sim))\n", "\n", "        calculated_similarity = get_cosine_similarity(embedding1_val, embedding2_val)\n", "        print(f\"\\n  Similarity between '{label1_val}' (<{entity1_uri_sim}>) and '{label2_val}' (<{entity2_uri_sim}>): {calculated_similarity:.4f}\")\n", "        \n", "        # Interpret similarity (example thresholds)\n", "        if calculated_similarity > 0.8:\n", "            print(f\"  Interpretation: These entities are highly similar based on their name embeddings.\")\n", "        elif calculated_similarity > 0.6:\n", "            print(f\"  Interpretation: These entities show moderate similarity based on their name embeddings.\")\n", "        else:\n", "            print(f\"  Interpretation: These entities show low similarity based on their name embeddings.\")\n", "            \n", "    print(\"\\nNote: This is a conceptual demonstration of semantic similarity. True link prediction involves training specialized KGE models (e.g., TransE, ComplEx) on existing graph triples to predict missing (subject, predicate, object) facts with specific predicates, not just general entity similarity.\")\n", "else:\n", "    print(\"Skipping conceptual link prediction: Not enough entity embeddings available (need at least 2).\")"]}, {"cell_type": "markdown", "id": "link-prediction-exec-output", "metadata": {}, "source": ["**Output Explanation:**\n", "This block will:\n", "*   Select two entities that have embeddings.\n", "    *   Calculate and print the cosine similarity score between their embeddings.\n", "    *   Provide a simple interpretation of the similarity score.\n", "    *   Include a disclaimer that this is a simplified concept, not full link prediction."]}, {"cell_type": "markdown", "id": "step4-3-add-inferred-func-def-desc", "metadata": {}, "source": ["### Step 4.3: Add Predicted Links (Optional & Conceptual) - Function Definition\n", "**Task:** Integrate high-confidence predicted links into the main graph.\n", "\n", "**Book Concept:** (Ch. 6 - KG enrichment and lifecycle)\n", "If a link prediction model were to identify new, high-confidence relationships, these could be added to the KG, enriching it with inferred knowledge. This is part of the KG lifecycle, where the graph evolves and grows.\n", "\n", "**Methodology:**\n", "The function `add_inferred_triples_to_graph` is a placeholder to illustrate this. It would take a list of (subject_uri, predicate_uri, object_uri) triples (presumably from a link prediction model) and add them to our main `rdflib.Graph`."]}, {"cell_type": "code", "execution_count": 26, "id": "add_inferred_triples_to_graph_func_def", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Function 'add_inferred_triples_to_graph' defined.\n"]}], "source": ["def add_inferred_triples_to_graph(target_graph, list_of_inferred_triples):\n", "    \"\"\"Adds a list of inferred (subject_uri, predicate_uri, object_uri) triples to the graph.\"\"\"\n", "    if not list_of_inferred_triples:\n", "        print(\"No inferred triples provided to add.\")\n", "        return target_graph, 0\n", "    \n", "    added_count = 0\n", "    for s_uri, p_uri, o_uri in list_of_inferred_triples:\n", "        # Basic validation: ensure they are URIRefs or Literals as appropriate\n", "        if isinstance(s_uri, URIRef) and isinstance(p_uri, URIRef) and (isinstance(o_uri, URIRef) or isinstance(o_uri, Literal)):\n", "            target_graph.add((s_uri, p_uri, o_uri))\n", "            added_count +=1\n", "        else:\n", "            print(f\"  Warning: Skipping malformed conceptual inferred triple: ({s_uri}, {p_uri}, {o_uri})\")\n", "            \n", "    print(f\"Added {added_count} conceptually inferred triples to the graph.\")\n", "    return target_graph, added_count\n", "\n", "print(\"Function 'add_inferred_triples_to_graph' defined.\")"]}, {"cell_type": "markdown", "id": "add_inferred_triples_to_graph_func_def_output", "metadata": {}, "source": ["**Output Explanation:**\n", "Confirms the definition of the `add_inferred_triples_to_graph` function."]}, {"cell_type": "markdown", "id": "add-predicted-links-exec-desc", "metadata": {}, "source": ["#### Add Predicted Links (Conceptual) - Execution Example\n", "\n", "**Theory:**\n", "This block provides a conceptual example. Since we haven't trained a full link prediction model, we create a dummy `conceptual_inferred_triples` list. If this list contained actual high-confidence predictions (e.g., from a TransE model scoring `(CompanyX, ex:potentialAcquirerOf, CompanyY)` highly), the `add_inferred_triples_to_graph` function would integrate them into our `kg`. In this demo, it will likely state that no triples were added unless you manually populate the dummy list."]}, {"cell_type": "code", "execution_count": 27, "id": "add-predicted-links-exec-code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "No conceptual inferred triples generated to add in this demonstration.\n"]}], "source": ["# Conceptual: Assume we have some high-confidence predicted links from a separate model.\n", "# For example, if entity1_uri_sim and entity2_uri_sim from the similarity check showed very high similarity,\n", "# and we had a predicate like ex:isSemanticallySimilarTo, we might add it.\n", "conceptual_inferred_triples_list = [] \n", "\n", "# Example: If we had variables entity1_uri_sim, entity2_uri_sim, and calculated_similarity from the previous step\n", "# This is just to illustrate, these variables might not be in scope if that cell wasn't run or had no valid entities\n", "SIMILARITY_THRESHOLD_FOR_INFERENCE = 0.85 # Example threshold\n", "if 'entity1_uri_sim' in locals() and 'entity2_uri_sim' in locals() and 'calculated_similarity' in locals():\n", "    if entity1_uri_sim and entity2_uri_sim and calculated_similarity > SIMILARITY_THRESHOLD_FOR_INFERENCE:\n", "        print(f\"Conceptual inference: Entities '{kg.label(entity1_uri_sim)}' and '{kg.label(entity2_uri_sim)}' are highly similar ({calculated_similarity:.2f}).\")\n", "        # Let's define a conceptual predicate for this\n", "        EX.isHighlySimilarTo = EX[\"isHighlySimilarTo\"] # Define if not already\n", "        conceptual_inferred_triples_list.append((entity1_uri_sim, EX.isHighly<PERSON>imilar<PERSON>o, entity2_uri_sim))\n", "        # Symmetrical relationship (optional, depends on predicate definition)\n", "        # conceptual_inferred_triples_list.append((entity2_uri_sim, EX.isHighly<PERSON>imilar<PERSON>o, entity1_uri_sim))\n", "\n", "if conceptual_inferred_triples_list:\n", "    print(f\"\\nAttempting to add {len(conceptual_inferred_triples_list)} conceptual inferred triples...\")\n", "    kg, num_added = add_inferred_triples_to_graph(kg, conceptual_inferred_triples_list)\n", "    if num_added > 0:\n", "        print(f\"Total triples in graph after adding conceptual inferences: {len(kg)}\")\n", "else:\n", "    print(\"\\nNo conceptual inferred triples generated to add in this demonstration.\")"]}, {"cell_type": "markdown", "id": "add-predicted-links-exec-output", "metadata": {}, "source": ["**Output Explanation:**\n", "This block will indicate if any conceptual inferred triples were added to the graph. If the dummy example for high similarity was triggered, it will show that these triples were added and the new total triple count."]}, {"cell_type": "markdown", "id": "phase5", "metadata": {}, "source": ["## Phase 5: Persistence and Utilization\n", "**(Ref: Ch. 3 – Data Storage; Ch. 5 – Querying and Access)**\n", "\n", "**Theory (Phase Overview):**\n", "Once the Knowledge Graph is constructed (and potentially refined), it needs to be stored for long-term access and utilized to derive insights. This phase covers:\n", "*   **KG Storage:** Persisting the graph. Options include RDF serialization formats (like Turtle, RDF/XML), native triple stores (e.g., Fuseki, GraphDB), or graph databases (e.g., Neo4j, if modeled appropriately) (Ch. 3).\n", "*   **Querying and Analysis:** Using query languages like SPARQL (for RDF KGs) to retrieve specific information, answer complex questions, and perform analytical tasks (Ch. 5).\n", "*   **Visualization:** Presenting parts of the KG or query results graphically for better human interpretation and understanding (Ch. 1 & 3 - Value of Visualization in Big Data)."]}, {"cell_type": "markdown", "id": "step5-1-save-graph-func-def-desc", "metadata": {}, "source": ["### Step 5.1: Knowledge Graph Storage - Save Function Definition\n", "**Task:** Persist the KG in a suitable format (e.g., RDF/Turtle).\n", "\n", "**Book Concept:** (Ch. 3 - Data Storage options)\n", "Serializing the KG to a file allows for persistence, sharing, and loading into other RDF-compliant tools or triple stores.\n", "\n", "**Methodology:**\n", "The `save_graph_to_turtle` function uses `rdflib.Graph.serialize()` method to save the `kg` object into a file. Turtle (`.ttl`) is chosen as it's a human-readable and common RDF serialization format."]}, {"cell_type": "code", "execution_count": 28, "id": "save_graph_to_turtle_func_def", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Function 'save_graph_to_turtle' defined.\n"]}], "source": ["def save_graph_to_turtle(graph_to_save, output_filepath=\"knowledge_graph.ttl\"):\n", "    \"\"\"Saves the RDF graph to a Turtle file.\"\"\"\n", "    if not len(graph_to_save):\n", "        print(\"Graph is empty. Nothing to save.\")\n", "        return False\n", "    try:\n", "        # Ensure the format string is correct, e.g., 'turtle', 'xml', 'n3', 'nt'\n", "        graph_to_save.serialize(destination=output_filepath, format='turtle')\n", "        print(f\"Knowledge Graph with {len(graph_to_save)} triples successfully saved to: {output_filepath}\")\n", "        return True\n", "    except Exception as e:\n", "        print(f\"Error saving graph to {output_filepath}: {e}\")\n", "        return False\n", "\n", "print(\"Function 'save_graph_to_turtle' defined.\")"]}, {"cell_type": "markdown", "id": "save_graph_to_turtle_func_def_output", "metadata": {}, "source": ["**Output Explanation:**\n", "Confirms the definition of the `save_graph_to_turtle` function."]}, {"cell_type": "markdown", "id": "kg-storage-exec-desc", "metadata": {}, "source": ["#### Knowledge Graph Storage - Execution\n", "\n", "**Theory:**\n", "This block calls the `save_graph_to_turtle` function to persist our constructed `kg` to a file named `tech_acquisitions_kg.ttl`. If the graph contains triples, it will be saved; otherwise, a message indicating an empty graph will be shown."]}, {"cell_type": "code", "execution_count": 29, "id": "kg-storage-exec-code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Attempting to save the graph with 75 triples...\n", "Knowledge Graph with 75 triples successfully saved to: tech_acquisitions_kg.ttl\n"]}], "source": ["KG_OUTPUT_FILENAME = \"tech_acquisitions_kg.ttl\"\n", "if len(kg) > 0:\n", "    print(f\"Attempting to save the graph with {len(kg)} triples...\")\n", "    save_graph_to_turtle(kg, KG_OUTPUT_FILENAME)\n", "else:\n", "    print(f\"Knowledge Graph ('kg') is empty. Skipping save to '{KG_OUTPUT_FILENAME}'.\")"]}, {"cell_type": "markdown", "id": "kg-storage-exec-output", "metadata": {}, "source": ["**Output Explanation:**\n", "This block will print a confirmation message if the graph is successfully saved, including the file path and the number of triples saved. If the graph is empty, it will state that."]}, {"cell_type": "markdown", "id": "step5-2-sparql-func-def-desc", "metadata": {}, "source": ["### Step 5.2: Querying and Analysis - SPARQL Execution Function\n", "**Task:** Execute SPARQL queries to extract insights.\n", "\n", "**Book Concept:** (Ch. 5 - Querying and Access, SPARQL)\n", "SPARQL (SPARQL Protocol and RDF Query Language) is the standard query language for RDF Knowledge Graphs. It allows for pattern matching against the graph structure to retrieve data, infer new information (through more complex queries), and answer analytical questions.\n", "\n", "**Methodology:**\n", "The `execute_sparql_query` function takes our `rdflib.Graph` and a SPARQL query string. It uses `graph.query()` to execute the query and then iterates through the results, printing them in a readable format. This function will be used to run several example queries."]}, {"cell_type": "code", "execution_count": 30, "id": "execute_sparql_query_func_def", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Function 'execute_sparql_query' defined.\n"]}], "source": ["def execute_sparql_query(graph_to_query, query_string_sparql):\n", "    \"\"\"Executes a SPARQL query on the graph and prints results, returning them as a list of dicts.\"\"\"\n", "    if not len(graph_to_query):\n", "        print(\"Cannot execute SPARQL query: The graph is empty.\")\n", "        return []\n", "        \n", "    print(f\"\\nExecuting SPARQL Query:\\n{query_string_sparql}\")\n", "    try:\n", "        query_results = graph_to_query.query(query_string_sparql)\n", "    except Exception as e:\n", "        print(f\"Error executing SPARQL query: {e}\")\n", "        return []\n", "\n", "    if not query_results:\n", "        print(\"Query executed successfully but returned no results.\")\n", "        return []\n", "    \n", "    results_list_of_dicts = []\n", "    print(f\"Query Results ({len(query_results)} found): \")\n", "    for row_idx, row_data in enumerate(query_results):\n", "        # Convert row to a dictionary for easier access and printing\n", "        # row_data is a ResultRow object, access by variable name from SELECT clause\n", "        result_item_dict = {}\n", "        if hasattr(row_data, 'labels'): # rdflib 6.x+ provides .labels and .asdict()\n", "            result_item_dict = {str(label): str(value) for label, value in row_data.asdict().items()}\n", "        else: # Fallback for older rdflib or if .asdict() is not available\n", "            # This part might need adjustment based on the actual structure of row_data in older versions\n", "            # For now, we'll assume it's an iterable of values corresponding to SELECT variables\n", "            # This requires knowing the SELECT variables' order, which is less robust\n", "            # For simplicity, if .labels is not present, we just make a list of string values\n", "            result_item_dict = {f\"col_{j}\": str(item_val) for j, item_val in enumerate(row_data)}\n", "            \n", "        results_list_of_dicts.append(result_item_dict)\n", "        \n", "        # Print a sample of results\n", "        if row_idx < 10: # Print up to 10 results\n", "            print(f\"  Row {row_idx+1}: {result_item_dict}\")\n", "        elif row_idx == 10:\n", "            print(f\"  ... ( da<PERSON><PERSON><PERSON><PERSON> {len(query_results) - 10} vý<PERSON>d<PERSON>ů )\") # and more results in Czech, should be English\n", "            print(f\"  ... (and {len(query_results) - 10} more results)\")\n", "            \n", "    return results_list_of_dicts\n", "\n", "print(\"Function 'execute_sparql_query' defined.\")"]}, {"cell_type": "markdown", "id": "execute_sparql_query_func_def_output", "metadata": {}, "source": ["**Output Explanation:**\n", "Confirms the definition of the `execute_sparql_query` function."]}, {"cell_type": "markdown", "id": "sparql-querying-exec-desc", "metadata": {}, "source": ["#### SPARQL Querying and Analysis - Execution Examples\n", "\n", "**Theory:**\n", "This block demonstrates the power of SPARQL by executing several example queries against our constructed KG (`kg`). Each query targets different aspects of the acquisition data:\n", "*   **Query 1: List Organizations:** Retrieves all entities explicitly typed as `schema:Organization` (or `ex:ORG`) and their labels. This is a basic check to see what organizations are in our KG.\n", "*   **Query 2: Find Acquisition Relationships:** Identifies pairs of companies where one acquired the other, based on our `ex:ACQUIRED` predicate. This directly extracts acquisition events.\n", "*   **Query 3: Find Acquisitions with Price:** Retrieves companies (or acquisition events) that have an associated `ex:HAS_PRICE` relationship pointing to a monetary value (`schema:PriceSpecification` or `ex:MONEY`).\n", "The results of each query are printed, showcasing how structured queries can extract specific insights from the graph."]}, {"cell_type": "code", "execution_count": 31, "id": "sparql-querying-exec-code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Executing Sample SPARQL Queries ---\n", "\n", "Executing SPARQL Query:\n", "\n", "    PREFIX ex: <http://example.org/kg/>\n", "    PREFIX schema: <http://schema.org/>\n", "    PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>\n", "    \n", "    SELECT DISTINCT ?org_uri ?org_label\n", "    WHERE {\n", "        ?org_uri a schema:Organization ;\n", "                 rdfs:label ?org_label .\n", "    }\n", "    ORDER BY ?org_label\n", "    LIMIT 10\n", "    \n", "Query Results (7 found): \n", "  Row 1: {'org_uri': 'http://example.org/kg/Algeria_Press_Agency_ORG', 'org_label': 'Algeria Press Agency'}\n", "  Row 2: {'org_uri': 'http://example.org/kg/CNN_ORG', 'org_label': 'CNN'}\n", "  Row 3: {'org_uri': 'http://example.org/kg/GSPC_ORG', 'org_label': 'GSPC'}\n", "  Row 4: {'org_uri': 'http://example.org/kg/Salafist_Group_for_Preaching_and_Combat_ORG', 'org_label': 'Salafist Group for Preaching and Combat'}\n", "  Row 5: {'org_uri': 'http://example.org/kg/UN_High_Commissioner_for_Refugees_ORG', 'org_label': 'UN High Commissioner for Refugees'}\n", "  Row 6: {'org_uri': 'http://example.org/kg/United_Nations_ORG', 'org_label': 'United Nations'}\n", "  Row 7: {'org_uri': 'http://example.org/kg/al_Qaeda_ORG', 'org_label': 'al Qaeda'}\n", "\n", "Executing SPARQL Query:\n", "\n", "    PREFIX ex: <http://example.org/kg/>\n", "    PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>\n", "    PREFIX schema: <http://schema.org/>\n", "    \n", "    SELECT ?acquiredCompanyLabel ?acquiringCompanyLabel\n", "    WHERE {\n", "        ?acquiredCompany ex:ACQUIRED ?acquiringCompany .\n", "        ?acquiredCompany rdfs:label ?acquiredCompanyLabel .\n", "        ?acquiringCompany rdfs:label ?acquiringCompanyLabel .\n", "        # Ensure both are organizations\n", "        ?acquiredCompany a schema:Organization .\n", "        ?acquiringCompany a schema:Organization .\n", "    }\n", "    LIMIT 10\n", "    \n", "Query executed successfully but returned no results.\n", "\n", "Executing SPARQL Query:\n", "\n", "    PREFIX ex: <http://example.org/kg/>\n", "    PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>\n", "    PREFIX schema: <http://schema.org/>\n", "    \n", "    SELECT ?companyLabel ?priceLabel ?dateLabel\n", "    WHERE {\n", "        ?company ex:HAS_PRICE ?priceEntity .\n", "        ?company rdfs:label ?companyLabel .\n", "        ?priceEntity rdfs:label ?priceLabel .\n", "        # Ensure ?company is an ORG and ?priceEntity is a PriceSpecification (MONEY)\n", "        ?company a schema:Organization .\n", "        ?priceEntity a schema:PriceSpecification .\n", "        \n", "        # Optionally, try to find a date associated with this acquisition event/company\n", "        OPTIONAL { \n", "            ?company ex:ANNOUNCED_ON ?dateEntity .\n", "            ?dateEntity rdfs:label ?dateLabelRaw .\n", "            # If dateEntity is schema:Date, its label might be the date string directly\n", "            # If dateEntity is an Event, it might have a schema:startDate or similar\n", "            BIND(COALESCE(?dateLabelRaw, STR(?dateEntity)) As ?dateLabel)            \n", "        }\n", "    }\n", "    LIMIT 10\n", "    \n", "Query executed successfully but returned no results.\n", "\n", "Executing SPARQL Query:\n", "\n", "    PREFIX ex: <http://example.org/kg/>\n", "    PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>\n", "    PREFIX schema: <http://schema.org/>\n", "    \n", "    SELECT ?acquiringCompanyLabel (COUNT(?acquiredCompany) AS ?numberOfAcquisitions)\n", "    WHERE {\n", "        ?acquiredCompany ex:ACQUIRED ?acquiringCompany .\n", "        ?acquiringCompany rdfs:label ?acquiringCompanyLabel .\n", "        ?acquiringCompany a schema:Organization .\n", "        ?acquiredCompany a schema:Organization .\n", "    }\n", "    GROUP BY ?acquiringCompanyLabel\n", "    ORDER BY DESC(?numberOfAcquisitions)\n", "    LIMIT 10\n", "    \n", "Query Results (1 found): \n"]}], "source": ["if len(kg) > 0:\n", "    print(\"\\n--- Executing Sample SPARQL Queries ---\")\n", "    # Query 1: Find all organizations mentioned in the KG and their labels\n", "    sparql_query_1 = \"\"\"\n", "    PREFIX ex: <http://example.org/kg/>\n", "    PREFIX schema: <http://schema.org/>\n", "    PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>\n", "    \n", "    SELECT DISTINCT ?org_uri ?org_label\n", "    WHERE {\n", "        ?org_uri a schema:Organization ;\n", "                 rdfs:label ?org_label .\n", "    }\n", "    ORDER BY ?org_label\n", "    LIMIT 10\n", "    \"\"\"\n", "    query1_results = execute_sparql_query(kg, sparql_query_1)\n", "\n", "    # Query 2: Find companies that acquired another company, and the acquired company\n", "    # Assumes: ?acquiredCompany ex:ACQUIRED ?acquiringCompany.\n", "    sparql_query_2 = \"\"\"\n", "    PREFIX ex: <http://example.org/kg/>\n", "    PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>\n", "    PREFIX schema: <http://schema.org/>\n", "    \n", "    SELECT ?acquiredCompanyLabel ?acquiringCompanyLabel\n", "    WHERE {\n", "        ?acquiredCompany ex:ACQUIRED ?acquiringCompany .\n", "        ?acquiredCompany rdfs:label ?acquiredCompanyLabel .\n", "        ?acquiringCompany rdfs:label ?acquiringCompanyLabel .\n", "        # Ensure both are organizations\n", "        ?acquiredCompany a schema:Organization .\n", "        ?acquiringCompany a schema:Organization .\n", "    }\n", "    LIMIT 10\n", "    \"\"\"\n", "    query2_results = execute_sparql_query(kg, sparql_query_2)\n", "\n", "    # Query 3: Find acquisitions (represented by a company involved) with a price mentioned\n", "    sparql_query_3 = \"\"\"\n", "    PREFIX ex: <http://example.org/kg/>\n", "    PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>\n", "    PREFIX schema: <http://schema.org/>\n", "    \n", "    SELECT ?companyLabel ?priceLabel ?dateLabel\n", "    WHERE {\n", "        ?company ex:HAS_PRICE ?priceEntity .\n", "        ?company rdfs:label ?companyLabel .\n", "        ?priceEntity rdfs:label ?priceLabel .\n", "        # Ensure ?company is an ORG and ?priceEntity is a PriceSpecification (MONEY)\n", "        ?company a schema:Organization .\n", "        ?priceEntity a schema:PriceSpecification .\n", "        \n", "        # Optionally, try to find a date associated with this acquisition event/company\n", "        OPTIONAL { \n", "            ?company ex:ANNOUNCED_ON ?dateEntity .\n", "            ?dateEntity rdfs:label ?dateLabelRaw .\n", "            # If dateEntity is schema:Date, its label might be the date string directly\n", "            # If dateEntity is an Event, it might have a schema:startDate or similar\n", "            BIND(COALESCE(?dateLabelRaw, STR(?dateEntity)) As ?dateLabel)            \n", "        }\n", "    }\n", "    LIMIT 10\n", "    \"\"\"\n", "    query3_results = execute_sparql_query(kg, sparql_query_3)\n", "    \n", "    # Query 4: Count number of acquisitions per acquiring company\n", "    sparql_query_4 = \"\"\"\n", "    PREFIX ex: <http://example.org/kg/>\n", "    PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>\n", "    PREFIX schema: <http://schema.org/>\n", "    \n", "    SELECT ?acquiringCompanyLabel (COUNT(?acquiredCompany) AS ?numberOfAcquisitions)\n", "    WHERE {\n", "        ?acquiredCompany ex:ACQUIRED ?acquiringCompany .\n", "        ?acquiringCompany rdfs:label ?acquiringCompanyLabel .\n", "        ?acquiringCompany a schema:Organization .\n", "        ?acquiredCompany a schema:Organization .\n", "    }\n", "    GROUP BY ?acquiringCompanyLabel\n", "    ORDER BY DESC(?numberOfAcquisitions)\n", "    LIMIT 10\n", "    \"\"\"\n", "    query4_results = execute_sparql_query(kg, sparql_query_4)\n", "\n", "else:\n", "    print(\"Knowledge Graph ('kg') is empty. Skipping SPARQL query execution.\")"]}, {"cell_type": "markdown", "id": "sparql-querying-exec-output", "metadata": {}, "source": ["**Output Explanation:**\n", "This block will print:\n", "*   Each SPARQL query string.\n", "*   The results (up to a limit) for each query, typically as a list of dictionaries where keys are the `SELECT` variables.\n", "If the KG is empty, it will indicate that queries are skipped."]}, {"cell_type": "markdown", "id": "step5-3-viz-func-def-desc", "metadata": {}, "source": ["### Step 5.3: Visualization (Optional) - Visualization Function Definition\n", "**Task:** Visualize parts of the KG or results from queries for better interpretability.\n", "\n", "**Book Concept:** (Ch. 1 & 3 - Visualization in Big Data)\n", "Visualizing graph structures can make complex relationships much easier to understand for humans. Interactive visualizations allow for exploration and discovery.\n", "\n", "**Methodology:**\n", "The `visualize_subgraph_pyvis` function uses the `pyvis` library to create an interactive HTML-based network visualization. It:\n", "*   Takes the `rdflib.Graph` and an optional filename.\n", "    *   (A more advanced version could take a central node URI and depth to explore from that node).\n", "*   For simplicity in this demo, it visualizes a sample of triples from the graph.\n", "*   Adds nodes and edges to a `pyvis.Network` object.\n", "*   Nodes are labeled with their `rdfs:label` (or a part of their URI if no label).\n", "*   Edges are labeled with the predicate name.\n", "*   Saves the visualization to an HTML file and attempts to display it inline if in a Jupyter environment."]}, {"cell_type": "code", "execution_count": 32, "id": "visualize_subgraph_pyvis_func_def", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Function 'visualize_subgraph_pyvis' defined.\n"]}], "source": ["def visualize_subgraph_pyvis(graph_to_viz, output_filename=\"kg_visualization.html\", sample_size_triples=75):\n", "    \"\"\"Visualizes a sample subgraph using pyvis and saves to HTML.\"\"\"\n", "    if not len(graph_to_viz):\n", "        print(\"Graph is empty, nothing to visualize.\")\n", "        return None\n", "\n", "    net = Network(notebook=True, height=\"800px\", width=\"100%\", cdn_resources='remote', directed=True)\n", "    net.repulsion(node_distance=150, spring_length=200)\n", "    # net.show_buttons(filter_=['physics', 'nodes', 'edges', 'interaction'])\n", "    net.set_options(\"\"\"\n", "    var options = {\n", "      \"physics\": {\n", "        \"forceAtlas2Based\": {\n", "          \"gravitationalConstant\": -50,\n", "          \"centralGravity\": 0.01,\n", "          \"springLength\": 100,\n", "          \"springConstant\": 0.08,\n", "          \"damping\": 0.4,\n", "          \"avoidOverlap\": 0.5\n", "        },\n", "        \"maxVelocity\": 50,\n", "        \"minVelocity\": 0.1,\n", "        \"solver\": \"forceAtlas2Based\",\n", "        \"timestep\": 0.5,\n", "        \"stabilization\": {\"iterations\": 150}\n", "      }\n", "    }\n", "    \"\"\")\n", "\n", "    added_nodes_set = set()\n", "    \n", "    # For a more meaningful visualization, focus on triples where subject and object are resources (URIs)\n", "    # And try to get a sample that includes some structure, not just attribute assignments to single nodes.\n", "    # Here, we'll take a sample of all triples for simplicity.\n", "    triples_for_visualization = list(graph_to_viz)[:min(sample_size_triples, len(graph_to_viz))]\n", "    \n", "    if not triples_for_visualization:\n", "        print(\"No triples selected from the sample for visualization.\")\n", "        return None\n", "        \n", "    print(f\"Preparing visualization for {len(triples_for_visualization)} sample triples...\")\n", "\n", "    for s_uri, p_uri, o_val in tqdm(triples_for_visualization, desc=\"Building Pyvis Visualization\"):\n", "        # Get labels or use URI parts\n", "        s_label_str = str(graph_to_viz.value(subject=s_uri, predicate=RDFS.label, default=s_uri.split('/')[-1].split('#')[-1]))\n", "        p_label_str = str(p_uri.split('/')[-1].split('#')[-1])\n", "        \n", "        s_node_id = str(s_uri)\n", "        s_node_title = f\"{s_label_str}\\nURI: {s_uri}\"\n", "        s_node_group_uri = graph_to_viz.value(s_uri, RDF.type)\n", "        s_node_group = str(s_node_group_uri.split('/')[-1].split('#')[-1]) if s_node_group_uri else \"UnknownType\"\n", "\n", "\n", "        if s_uri not in added_nodes_set:\n", "            net.add_node(s_node_id, label=s_label_str, title=s_node_title, group=s_node_group)\n", "            added_nodes_set.add(s_uri)\n", "        \n", "        if isinstance(o_val, URIRef): # If object is a resource, add it as a node and draw an edge\n", "            o_label_str = str(graph_to_viz.value(subject=o_val, predicate=RDFS.label, default=o_val.split('/')[-1].split('#')[-1]))\n", "            o_node_id = str(o_val)\n", "            o_node_title = f\"{o_label_str}\\nURI: {o_val}\"\n", "            o_node_group_uri = graph_to_viz.value(o_val, RDF.type)\n", "            o_node_group = str(o_node_group_uri.split('/')[-1].split('#')[-1]) if o_node_group_uri else \"UnknownType\"\n", "            \n", "            if o_val not in added_nodes_set:\n", "                net.add_node(o_node_id, label=o_label_str, title=o_node_title, group=o_node_group)\n", "                added_nodes_set.add(o_val)\n", "            net.add_edge(s_node_id, o_node_id, title=p_label_str, label=p_label_str)\n", "        else: # If object is a literal, add it as a property to the subject node's title (tooltip)\n", "            # This avoids cluttering the graph with many literal nodes.\n", "            # Update subject node's title if it's already added\n", "            for node_obj in net.nodes:\n", "                if node_obj['id'] == s_node_id:\n", "                    node_obj['title'] += f\"\\n{p_label_str}: {str(o_val)}\"\n", "                    break\n", "    \n", "    try:\n", "        net.save_graph(output_filename)\n", "        print(f\"Interactive KG visualization saved to HTML file: {output_filename}\")\n", "        # In Jupyter Lab/Notebook, the graph should render inline if notebook=True was set and environment supports it.\n", "        # Sometimes, an explicit display is needed, or opening the HTML file manually.\n", "    except Exception as e:\n", "        print(f\"Error saving or attempting to show graph visualization: {e}\")\n", "    return net # Return the network object\n", "\n", "print(\"Function 'visualize_subgraph_pyvis' defined.\")"]}, {"cell_type": "markdown", "id": "visualize_subgraph_pyvis_func_def_output", "metadata": {}, "source": ["**Output Explanation:**\n", "Confirms the definition of the `visualize_subgraph_pyvis` function."]}, {"cell_type": "markdown", "id": "visualization-exec-desc", "metadata": {}, "source": ["#### KG Visualization - Execution\n", "\n", "**Theory:**\n", "This block calls `visualize_subgraph_pyvis` with our `kg`. It will generate an HTML file (e.g., `tech_acquisitions_kg_viz.html`) containing the interactive graph. If running in a compatible Jupyter environment, the visualization might also render directly in the notebook output. This allows for a visual exploration of the connections and entities within a sample of our KG."]}, {"cell_type": "code", "execution_count": 33, "id": "visualization-exec-code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Attempting to visualize a sample of the graph with 75 triples...\n", "Preparing visualization for 75 sample triples...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "372adb1f2db1432cbc080375a0760437", "version_major": 2, "version_minor": 0}, "text/plain": ["Building Pyvis Visualization:   0%|          | 0/75 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Interactive KG visualization saved to HTML file: tech_acquisitions_kg_interactive_viz.html\n", "\n", "To view the visualization, open the file 'tech_acquisitions_kg_interactive_viz.html' in a web browser.\n", "If in a Jupyter Notebook/Lab, the graph might also be rendered above this message.\n"]}], "source": ["VIZ_OUTPUT_FILENAME = \"tech_acquisitions_kg_interactive_viz.html\"\n", "pyvis_network_object = None # Initialize\n", "\n", "if len(kg) > 0:\n", "    print(f\"Attempting to visualize a sample of the graph with {len(kg)} triples...\")\n", "    # Visualize a sample of up to 75 triples from the graph\n", "    pyvis_network_object = visualize_subgraph_pyvis(kg, output_filename=VIZ_OUTPUT_FILENAME, sample_size_triples=75)\n", "else:\n", "    print(f\"Knowledge Graph ('kg') is empty. Skipping visualization.\")\n", "\n", "# Attempt to display inline in Jupy<PERSON> (might require trusting the notebook or specific Jupyter setup)\n", "if pyvis_network_object is not None:\n", "    try:\n", "        # This should trigger inline display in classic notebook or if properly configured in Lab\n", "        from IPython.display import HTML, display\n", "        # display(HTML(VIZ_OUTPUT_FILENAME)) # This loads from file, pyvis might render directly\n", "        # pyvis_network_object.show(VIZ_OUTPUT_FILENAME) # Alternative: opens in new tab/tries inline\n", "        print(f\"\\nTo view the visualization, open the file '{VIZ_OUTPUT_FILENAME}' in a web browser.\")\n", "        print(\"If in a Jupyter Notebook/Lab, the graph might also be rendered above this message.\")\n", "        # If pyvis_network_object is returned from a cell and notebook=True, it often renders automatically.\n", "    except Exception as e_display:\n", "        print(f\"Could not automatically display visualization inline ({e_display}). Please open '{VIZ_OUTPUT_FILENAME}' manually.\")\n", "        \n", "# This cell will return the pyvis_network_object. If it's the last statement and notebook=True, \n", "# <PERSON><PERSON><PERSON> will attempt to render it.\n", "if pyvis_network_object:\n", "    pyvis_network_object # This line is crucial for auto-display in some Jupyter environments"]}, {"cell_type": "markdown", "id": "visualization-exec-output", "metadata": {}, "source": ["**Output Explanation:**\n", "This block will:\n", "*   Generate an HTML file (e.g., `tech_acquisitions_kg_interactive_viz.html`) with the interactive graph visualization.\n", "*   Print a message confirming the save and provide the filename.\n", "*   If in a compatible Jupyter environment, it might also render the graph directly below the cell. Otherwise, you'll need to open the HTML file manually in a browser."]}, {"cell_type": "markdown", "id": "conclusion", "metadata": {}, "source": ["## Conclusion and Future Work\n", "\n", "This notebook has demonstrated a comprehensive, albeit simplified, end-to-end pipeline for constructing a Knowledge Graph from unstructured news articles, with a focus on technology company acquisitions. We navigated through critical phases, referencing conceptual underpinnings from Big Data and Knowledge Graph literature:\n", "\n", "1.  **Data Acquisition and Preparation:** We loaded articles from the CNN/DailyMail dataset and performed essential cleaning to prepare the text for analysis. This underscored the importance of data quality as a foundation (Ch. 1, Ch. 3).\n", "2.  **Information Extraction:** \n", "    *   Named Entity Recognition (NER) was performed first exploratively with spaCy, then more targetedly using an LLM guided by a refined entity schema. This created the *nodes* of our graph (Ch. 2).\n", "    *   Relationship Extraction (RE) using an LLM identified semantic connections between these entities, forming the *edges* (Ch. 2).\n", "3.  **Knowledge Graph Construction:** \n", "    *   Entities were normalized for consistency, and unique URIs were generated, aiding in entity resolution (Ch. 6, Ch. 8).\n", "    *   Extracted information was mapped to a schema (mixing custom `EX:` terms and `schema.org` terms) and materialized into RDF triples using `rdflib` (Ch. 2, Ch. 4).\n", "4.  **Knowledge Graph Refinement (Conceptual):** \n", "    *   We generated text embeddings for entity names, bridging symbolic and sub-symbolic representations (Ch. 6).\n", "    *   The concept of link prediction via semantic similarity was introduced, hinting at KG enrichment capabilities (Ch. 6).\n", "5.  **Persistence and Utilization:** \n", "    *   The KG was persisted by serializing it to a Turtle file (Ch. 3).\n", "    *   SPARQL queries were executed to retrieve structured insights, demonstrating the analytical power of KGs (Ch. 5).\n", "    *   A sample subgraph was visualized, highlighting the importance of making KGs accessible (Ch. 1, Ch. 3).\n", "\n", "### Potential Future Enhancements:\n", "*   **Advanced Entity Disambiguation & Linking (EDL):** Implement robust EDL to link extracted entities to canonical entries in external KGs like Wikidata or DBpedia. This would greatly improve graph integration and consistency.\n", "*   **Richer Ontology/Schema:** Develop a more detailed custom ontology for technology acquisitions or align more comprehensively with existing financial or business ontologies (e.g., FIBO).\n", "*   **Sophisticated Relationship Extraction:** Explore more advanced RE techniques, including classifying a wider range of relation types, handling n-ary relations, and event extraction (modeling acquisitions as complex events with multiple participants and attributes).\n", "*   **Knowledge Graph Embedding Models:** Train dedicated KGE models (e.g., TransE, ComplEx, RotatE from Ch. 6) on the generated triples for more accurate link prediction and KG completion.\n", "*   **Reasoning and Inference:** Implement ontological reasoning (e.g., using RDFS/OWL reasoners) to infer new facts based on the schema and asserted triples.\n", "*   **Scalability and Performance:** For larger datasets, utilize distributed processing frameworks (like Apache Spark, conceptually linked to SANSA in Ch. 7 for ML on KGs) and deploy the KG in a scalable graph database or triple store.\n", "*   **LLM Fine-tuning:** Fine-tune smaller, open-source LLMs specifically on NER and RE tasks for the technology/financial domain to potentially achieve better performance and cost-efficiency than general-purpose models for these specific tasks.\n", "*   **Temporal Dynamics:** Incorporate the temporal aspect of news data more explicitly, tracking how information and relationships evolve over time.\n", "*   **User Interface:** Develop a user-friendly interface for exploring, querying, and visualizing the KG beyond programmatic access.\n", "\n", "This project serves as a foundational example, illustrating how modern NLP techniques, particularly LLMs, can be integrated with traditional KG methodologies to extract and structure valuable knowledge from vast amounts of unstructured text."]}], "metadata": {"kernelspec": {"display_name": ".venv-big-data", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}