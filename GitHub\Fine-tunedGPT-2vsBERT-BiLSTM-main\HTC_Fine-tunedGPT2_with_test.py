#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
层次文本分类 - 基于Fine-tuned GPT-2模型实现（包含测试集验证）

本脚本实现了使用GPT-2模型进行层次文本分类的完整流程，包括：
1. 数据预处理和标签编码
2. 模型初始化和配置
3. 分层训练（类别和超类别）
4. 模型评估和层次F1分数计算
5. 独立测试集验证

作者: eedisgpu, Bouchiha
创建时间: 2024年7月-8月
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix
from transformers import GPT2Tokenizer, GPT2ForSequenceClassification, Trainer, TrainingArguments, DataCollatorWithPadding
import torch
from torch.utils.data import Dataset
import os

# ==================== 数据加载和预处理 ====================
"""
数据预处理阶段：
1. 加载训练数据集和测试数据集
2. 构建层次化文本表示
3. 为GPT-2模型准备输入格式
"""

# 加载训练数据集（包含文本、类别、超类别信息）
print("正在加载训练数据集...")
train_data = pd.read_csv('train_40k_Adapted.csv')

# 加载测试数据集（格式与训练集相同）
print("正在加载测试数据集...")
test_data = pd.read_csv('test_dataset.csv')  # 请替换为您的测试集文件名

print(f"训练集大小: {len(train_data)}")
print(f"测试集大小: {len(test_data)}")

# 构建层次化文本表示
# 将原始文本与类别信息结合，形成"文本 [CATEGORY] 类别"的格式
train_data['text_category'] = train_data['text'] + " [CATEGORY] " + train_data['category']
test_data['text_category'] = test_data['text'] + " [CATEGORY] " + test_data['category']

# 进一步添加超类别信息，形成完整的层次结构
train_data['text_category_supercategory'] = train_data['text_category'] + " [SUPERCATEGORY] " + train_data['super_category']
test_data['text_category_supercategory'] = test_data['text_category'] + " [SUPERCATEGORY] " + test_data['super_category']

# ==================== 分词器和模型初始化 ====================
"""
GPT-2模型配置：
- 使用预训练的GPT-2分词器和模型
- 设置填充标记以处理不同长度的文本
- 配置序列分类任务
"""

# 初始化GPT-2分词器
tokenizer = GPT2Tokenizer.from_pretrained('gpt2')
# GPT-2原本没有填充标记，使用结束标记作为填充标记
tokenizer.pad_token = tokenizer.eos_token

# ==================== 自定义数据集类 ====================
class CustomDataset(Dataset):
    """
    自定义PyTorch数据集类
    
    功能：
    1. 封装编码后的文本数据和标签
    2. 提供批量数据加载接口
    3. 确保数据格式符合Trainer要求
    """
    def __init__(self, encodings, labels):
        """
        初始化数据集
        
        参数:
        - encodings: 分词器编码后的文本数据
        - labels: 对应的数字化标签
        """
        self.encodings = encodings
        self.labels = labels
    
    def __len__(self):
        """返回数据集大小"""
        return len(self.labels)
    
    def __getitem__(self, idx):
        """
        获取单个数据样本
        
        返回:
        - item: 包含input_ids, attention_mask等的字典
        - labels: 对应的标签张量
        """
        item = {key: torch.tensor(val[idx]) for key, val in self.encodings.items()}
        item['labels'] = torch.tensor(self.labels[idx])
        return item

# ==================== 标签编码 ====================
"""
标签数字化处理：
基于训练集创建标签映射，确保训练集和测试集使用相同的编码
"""

print("正在创建标签映射...")

# 基于训练集创建标签映射字典
category_labels = {label: i for i, label in enumerate(train_data['category'].unique())}
super_category_labels = {label: i for i, label in enumerate(train_data['super_category'].unique())}

print(f"类别数量: {len(category_labels)}")
print(f"超类别数量: {len(super_category_labels)}")

# 将训练集标签转换为数字
train_labels_category = train_data['category'].map(category_labels).tolist()
train_labels_super_category = train_data['super_category'].map(super_category_labels).tolist()

# 将测试集标签转换为数字（使用训练集的映射）
test_labels_category = test_data['category'].map(category_labels).tolist()
test_labels_super_category = test_data['super_category'].map(super_category_labels).tolist()

# 检查测试集是否包含训练集中未见过的标签
unknown_categories = set(test_data['category']) - set(train_data['category'])
unknown_super_categories = set(test_data['super_category']) - set(train_data['super_category'])

if unknown_categories:
    print(f"警告：测试集包含未知类别: {unknown_categories}")
if unknown_super_categories:
    print(f"警告：测试集包含未知超类别: {unknown_super_categories}")

# ==================== 数据集划分 ====================
"""
训练/验证集划分：
从训练数据中划分出验证集用于训练过程中的评估
测试集保持独立，用于最终性能验证
"""
train_texts, val_texts, train_labels_cat, val_labels_cat, train_labels_super_cat, val_labels_super_cat = train_test_split(
    train_data['text'].tolist(), train_labels_category, train_labels_super_category, 
    test_size=0.2, random_state=42)

print(f"训练集大小: {len(train_texts)}")
print(f"验证集大小: {len(val_texts)}")
print(f"测试集大小: {len(test_data)}")

# ==================== 文本编码 ====================
"""
文本预处理：
- 截断：限制最大长度为512个token
- 填充：统一序列长度便于批处理
- 生成attention_mask：标识有效token位置
"""

print("正在编码文本数据...")

# 编码训练集、验证集和测试集
train_encodings = tokenizer(train_texts, truncation=True, padding=True, max_length=512)
val_encodings = tokenizer(val_texts, truncation=True, padding=True, max_length=512)
test_encodings = tokenizer(test_data['text'].tolist(), truncation=True, padding=True, max_length=512)

# ==================== 创建数据集对象 ====================
"""
将编码后的数据封装为PyTorch数据集
分别为类别和超类别分类任务创建数据集
"""

# 类别分类数据集
train_dataset_category = CustomDataset(train_encodings, train_labels_cat)
val_dataset_category = CustomDataset(val_encodings, val_labels_cat)
test_dataset_category = CustomDataset(test_encodings, test_labels_category)

# 超类别分类数据集
train_dataset_super_category = CustomDataset(train_encodings, train_labels_super_cat)
val_dataset_super_category = CustomDataset(val_encodings, val_labels_super_cat)
test_dataset_super_category = CustomDataset(test_encodings, test_labels_super_category)

# ==================== 模型初始化 ====================
"""
GPT-2序列分类模型配置：
1. 加载预训练GPT-2模型
2. 添加分类头（线性层）
3. 调整词汇表大小以匹配分词器
4. 移至GPU加速训练
"""

print("正在初始化模型...")

# 初始化类别分类模型
model_category = GPT2ForSequenceClassification.from_pretrained(
    'gpt2', 
    num_labels=len(category_labels)  # 设置输出类别数
)
model_category.resize_token_embeddings(len(tokenizer))
model_category.to('cuda')

# 初始化超类别分类模型
model_super_category = GPT2ForSequenceClassification.from_pretrained(
    'gpt2', 
    num_labels=len(super_category_labels)  # 设置输出超类别数
)
model_super_category.resize_token_embeddings(len(tokenizer))
model_super_category.to('cuda')

# ==================== 数据整理器 ====================
data_collator = DataCollatorWithPadding(tokenizer=tokenizer)

# ==================== 训练参数配置 ====================
"""
训练超参数配置：
- 保存最佳模型检查点
- 早停机制防止过拟合
- 详细的评估和日志记录
"""

# 类别分类训练参数
training_args_category = TrainingArguments(
    output_dir='./results_category',
    num_train_epochs=5,
    per_device_train_batch_size=1,
    per_device_eval_batch_size=1,
    warmup_steps=500,
    weight_decay=0.01,
    logging_dir='./logs_category',
    logging_steps=10,
    evaluation_strategy="epoch",
    save_strategy="epoch",                     # 每个epoch保存模型
    load_best_model_at_end=True,              # 训练结束时加载最佳模型
    metric_for_best_model="eval_loss",        # 使用验证损失选择最佳模型
    greater_is_better=False,                  # 损失越小越好
)

# 超类别分类训练参数
training_args_super_category = TrainingArguments(
    output_dir='./results_super_category',
    num_train_epochs=3,
    per_device_train_batch_size=1,
    per_device_eval_batch_size=1,
    warmup_steps=500,
    weight_decay=0.01,
    logging_dir='./logs_super_category',
    logging_steps=10,
    evaluation_strategy="epoch",
    save_strategy="epoch",
    load_best_model_at_end=True,
    metric_for_best_model="eval_loss",
    greater_is_better=False,
)

# ==================== 训练器初始化 ====================
trainer_category = Trainer(
    model=model_category,
    args=training_args_category,
    train_dataset=train_dataset_category,
    eval_dataset=val_dataset_category,        # 使用验证集进行训练时评估
    data_collator=data_collator
)

trainer_super_category = Trainer(
    model=model_super_category,
    args=training_args_super_category,
    train_dataset=train_dataset_super_category,
    eval_dataset=val_dataset_super_category,
    data_collator=data_collator
)

# ==================== 模型训练 ====================
print("开始训练类别分类模型...")
trainer_category.train()

print("开始训练超类别分类模型...")
trainer_super_category.train()

# ==================== 测试集验证 ====================
"""
使用训练好的模型在独立测试集上进行最终验证
这是模型性能的真实评估，因为测试集在训练过程中从未被使用
"""

print("\n" + "="*50)
print("开始在测试集上进行最终验证...")
print("="*50)

# 在测试集上进行预测
print("正在对测试集进行预测...")
test_predictions_category = trainer_category.predict(test_dataset_category)
test_predictions_super_category = trainer_super_category.predict(test_dataset_super_category)

# 将预测概率转换为预测标签
test_pred_cat = np.argmax(test_predictions_category.predictions, axis=1)
test_true_cat = np.array(test_labels_category)

test_pred_super_cat = np.argmax(test_predictions_super_category.predictions, axis=1)
test_true_super_cat = np.array(test_labels_super_category)

# ==================== 层次化评估指标 ====================
def hierarchical_f1_score(y_true_cat, y_pred_cat, y_true_super_cat, y_pred_super_cat):
    """
    计算层次化F1分数
    
    原理：
    只有当类别和超类别都预测正确时，才认为预测正确
    这反映了层次分类的严格要求
    """
    correct_predictions = (y_true_cat == y_pred_cat) & (y_true_super_cat == y_pred_super_cat)
    hierarchical_f1 = f1_score(correct_predictions, [True] * len(correct_predictions), average='weighted')
    return hierarchical_f1

def calculate_hierarchical_accuracy(y_true_cat, y_pred_cat, y_true_super_cat, y_pred_super_cat):
    """
    计算层次化准确率
    
    返回：
    - exact_match_accuracy: 完全匹配准确率（类别和超类别都正确）
    - partial_match_accuracy: 部分匹配准确率（至少一个正确）
    """
    exact_match = (y_true_cat == y_pred_cat) & (y_true_super_cat == y_pred_super_cat)
    partial_match = (y_true_cat == y_pred_cat) | (y_true_super_cat == y_pred_super_cat)
    
    exact_match_accuracy = np.mean(exact_match)
    partial_match_accuracy = np.mean(partial_match)
    
    return exact_match_accuracy, partial_match_accuracy

# ==================== 测试集性能评估 ====================
"""
计算测试集上的各项性能指标：
1. 单独的类别和超类别性能
2. 层次化性能指标
3. 详细的分类报告
"""

print("正在计算测试集性能指标...")

# 单独性能指标
test_category_accuracy = accuracy_score(test_true_cat, test_pred_cat)
test_category_f1 = f1_score(test_true_cat, test_pred_cat, average='weighted')
test_super_category_accuracy = accuracy_score(test_true_super_cat, test_pred_super_cat)
test_super_category_f1 = f1_score(test_true_super_cat, test_pred_super_cat, average='weighted')

# 层次化性能指标
test_hierarchical_f1 = hierarchical_f1_score(test_true_cat, test_pred_cat, 
                                            test_true_super_cat, test_pred_super_cat)
test_exact_accuracy, test_partial_accuracy = calculate_hierarchical_accuracy(
    test_true_cat, test_pred_cat, test_true_super_cat, test_pred_super_cat)

# ==================== 结果输出和保存 ====================
print("\n" + "="*60)
print("           测试集最终验证结果")
print("="*60)
print(f'类别准确率 (Category Accuracy): {test_category_accuracy:.4f}')
print(f'类别F1分数 (Category F1 Score): {test_category_f1:.4f}')
print(f'超类别准确率 (Super Category Accuracy): {test_super_category_accuracy:.4f}')
print(f'超类别F1分数 (Super Category F1 Score): {test_super_category_f1:.4f}')
print("-" * 60)
print(f'层次化F1分数 (Hierarchical F1 Score): {test_hierarchical_f1:.4f}')
print(f'完全匹配准确率 (Exact Match Accuracy): {test_exact_accuracy:.4f}')
print(f'部分匹配准确率 (Partial Match Accuracy): {test_partial_accuracy:.4f}')
print("="*60)

# ==================== 详细分析报告 ====================
"""
生成详细的分类报告和混淆矩阵
保存预测结果用于进一步分析
"""

print("\n正在生成详细分析报告...")

# 创建结果保存目录
os.makedirs('test_results', exist_ok=True)

# 反向映射：数字标签转回文本标签
reverse_category_labels = {v: k for k, v in category_labels.items()}
reverse_super_category_labels = {v: k for k, v in super_category_labels.items()}

# 转换预测结果为文本标签
test_pred_cat_text = [reverse_category_labels[pred] for pred in test_pred_cat]
test_true_cat_text = [reverse_category_labels[true] for true in test_true_cat]
test_pred_super_cat_text = [reverse_super_category_labels[pred] for pred in test_pred_super_cat]
test_true_super_cat_text = [reverse_super_category_labels[true] for true in test_true_super_cat]

# 保存预测结果
results_df = pd.DataFrame({
    'text': test_data['text'].tolist(),
    'true_category': test_true_cat_text,
    'pred_category': test_pred_cat_text,
    'true_super_category': test_true_super_cat_text,
    'pred_super_category': test_pred_super_cat_text,
    'category_correct': test_true_cat == test_pred_cat,
    'super_category_correct': test_true_super_cat == test_pred_super_cat,
    'hierarchical_correct': (test_true_cat == test_pred_cat) & (test_true_super_cat == test_pred_super_cat)
})

results_df.to_csv('test_results/test_predictions.csv', index=False, encoding='utf-8-sig')

# 生成分类报告
print("\n类别分类报告:")
print(classification_report(test_true_cat_text, test_pred_cat_text))

print("\n超类别分类报告:")
print(classification_report(test_true_super_cat_text, test_pred_super_cat_text))

# 保存性能指标汇总
performance_summary = {
    'metric': ['Category Accuracy', 'Category F1', 'Super Category Accuracy', 
               'Super Category F1', 'Hierarchical F1', 'Exact Match Accuracy', 
               'Partial Match Accuracy'],
    'score': [test_category_accuracy, test_category_f1, test_super_category_accuracy,
              test_super_category_f1, test_hierarchical_f1, test_exact_accuracy, 
              test_partial_accuracy]
}

performance_df = pd.DataFrame(performance_summary)
performance_df.to_csv('test_results/performance_summary.csv', index=False)

print(f"\n结果已保存到 test_results/ 目录:")
print("- test_predictions.csv: 详细预测结果")
print("- performance_summary.csv: 性能指标汇总")

print("\n测试集验证完成！")