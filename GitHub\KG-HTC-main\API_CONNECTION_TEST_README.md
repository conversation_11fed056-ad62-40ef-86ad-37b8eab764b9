# OpenAI API连接测试指南

本文档介绍如何使用提供的测试脚本来验证OpenAI兼容接口和向量数据库的连接状态。

## 📁 测试脚本文件

| 文件名 | 功能 | 适用场景 |
|--------|------|----------|
| `test_openai_api_connection.py` | 完整的OpenAI API测试 | 全面测试推理和向量化模型 |
| `quick_api_test.py` | 快速API连接测试 | 快速验证基本连接 |
| `test_vector_db_connection.py` | 向量数据库测试 | 测试VectorDB类功能 |
| `.env.example.api_test` | 配置示例文件 | 环境变量配置参考 |

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install requests python-dotenv

# 如果要测试向量数据库
pip install chromadb sentence-transformers
```

### 2. 配置环境变量

复制配置示例文件：
```bash
cp .env.example.api_test .env
```

编辑 `.env` 文件，填入您的API配置：

#### 官方OpenAI配置
```bash
OPENAI_API_KEY=sk-your-openai-api-key-here
```

#### Azure OpenAI配置
```bash
AZURE_ENDPOINT=https://your-resource.openai.azure.com/
API_KEY=your-azure-api-key-here
API_VERSION=2023-12-01-preview
DEPLOYMENT_NAME=gpt-35-turbo
EMBEDDING_DEPLOYMENT_NAME=text-embedding-ada-002
```

#### 自定义兼容接口配置
```bash
CUSTOM_BASE_URL=http://localhost:8000/v1
CUSTOM_API_KEY=dummy-key
CUSTOM_INFERENCE_MODEL=qwen2:7b
CUSTOM_EMBEDDING_MODEL=bge-large-zh-v1.5
```

### 3. 运行测试

#### 快速测试（推荐）
```bash
python quick_api_test.py
```

#### 完整测试
```bash
python test_openai_api_connection.py
```

#### 向量数据库测试
```bash
python test_vector_db_connection.py
```

## 🔧 支持的接口类型

### 1. 官方OpenAI API
- **地址**: `https://api.openai.com/v1`
- **认证**: Bearer Token
- **模型**: gpt-3.5-turbo, text-embedding-3-small等

### 2. Azure OpenAI
- **地址**: `https://{resource}.openai.azure.com/`
- **认证**: api-key header
- **模型**: 部署名称（如gpt-35-turbo）

### 3. 兼容接口
支持OpenAI API格式的第三方服务：

#### vLLM
```bash
# 启动vLLM服务
python -m vllm.entrypoints.openai.api_server \
    --model Qwen/Qwen2-7B-Instruct \
    --port 8000

# 配置
CUSTOM_BASE_URL=http://localhost:8000/v1
CUSTOM_INFERENCE_MODEL=Qwen/Qwen2-7B-Instruct
```

#### Ollama
```bash
# 启动Ollama服务
ollama serve

# 拉取模型
ollama pull qwen2:7b

# 配置
CUSTOM_BASE_URL=http://localhost:11434/v1
CUSTOM_INFERENCE_MODEL=qwen2:7b
```

#### 其他兼容服务
- **LocalAI**: 本地部署的OpenAI兼容服务
- **FastChat**: 支持多种开源模型的API服务
- **Text Generation WebUI**: 带API接口的Web界面

## 📊 测试结果解读

### 成功示例
```
✅ 推理模型连接成功!
   响应时间: 1.23秒
   模型回复: API connection successful!

✅ 向量化模型连接成功!
   响应时间: 0.85秒
   向量维度: 1536
```

### 常见错误及解决方案

#### 1. 连接错误
```
❌ 推理模型连接失败!
   错误: Connection refused
```
**解决方案**:
- 检查服务是否启动
- 确认端口号正确
- 检查防火墙设置

#### 2. 认证错误
```
❌ 推理模型连接失败!
   状态码: 401
   错误信息: Invalid API key
```
**解决方案**:
- 检查API密钥是否正确
- 确认密钥格式（Bearer vs api-key）
- 验证密钥是否有效且有余额

#### 3. 模型不存在
```
❌ 推理模型连接失败!
   状态码: 404
   错误信息: Model not found
```
**解决方案**:
- 检查模型名称是否正确
- 确认模型是否已部署
- 验证部署名称（Azure）

#### 4. 超时错误
```
❌ 推理模型连接失败!
   错误: Request timeout
```
**解决方案**:
- 检查网络连接
- 增加超时时间
- 确认服务响应正常

## 🛠️ 高级配置

### 1. 代理设置
如果需要通过代理访问：
```bash
export HTTP_PROXY=http://proxy.company.com:8080
export HTTPS_PROXY=http://proxy.company.com:8080
```

### 2. 自定义超时
修改脚本中的超时设置：
```python
response = requests.post(url, json=data, headers=headers, timeout=60)  # 60秒超时
```

### 3. 调试模式
设置环境变量启用详细日志：
```bash
export DEBUG_MODE=true
```

## 📝 测试报告

测试完成后会生成以下文件：
- `openai_api_test_results.json`: 完整测试结果
- `vector_db_test_results.json`: 向量数据库测试结果

### 报告内容
```json
{
  "inference_model": {
    "status": "成功",
    "response_time": "1.23秒",
    "model": "gpt-3.5-turbo",
    "response_content": "API connection successful!"
  },
  "embedding_model": {
    "status": "成功",
    "response_time": "0.85秒",
    "embedding_dimension": 1536
  },
  "summary": {
    "total_tests": 2,
    "passed": 2,
    "failed": 0
  }
}
```

## 🔍 故障排除

### 1. 检查网络连接
```bash
# 测试基本连接
curl -I https://api.openai.com/v1/models

# 测试自定义接口
curl -I http://localhost:8000/v1/models
```

### 2. 验证API密钥
```bash
# OpenAI官方API
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models

# Azure OpenAI
curl -H "api-key: $API_KEY" \
     "$AZURE_ENDPOINT/openai/models?api-version=$API_VERSION"
```

### 3. 检查服务状态
```bash
# 检查端口占用
netstat -tlnp | grep :8000

# 检查进程
ps aux | grep vllm
ps aux | grep ollama
```

## 💡 最佳实践

1. **优先使用快速测试**: 先运行 `quick_api_test.py` 验证基本连接
2. **逐步测试**: 先测试推理模型，再测试向量化模型
3. **保存配置**: 将工作的配置保存到 `.env` 文件
4. **监控资源**: 注意API调用次数和费用
5. **定期测试**: 定期运行测试确保服务稳定

## 📞 技术支持

如果遇到问题：
1. 查看测试结果文件中的详细错误信息
2. 检查服务提供商的文档和状态页面
3. 确认网络和防火墙配置
4. 验证API密钥和权限设置
