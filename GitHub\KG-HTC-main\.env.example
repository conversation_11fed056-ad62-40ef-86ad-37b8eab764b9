# Neo4j数据库配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password123

# Azure OpenAI配置（如果使用GPT模型）
API_VERSION=2023-12-01-preview
AZURE_ENDPOINT=https://your-resource.openai.azure.com/
API_KEY=your-api-key-here
DEPLOYMENT_NAME=gpt-35-turbo

# OpenAI配置（如果使用OpenAI API）
OPENAI_API_KEY=your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# BGE模型配置（本地向量化）
BGE_MODEL_NAME=BAAI/bge-large-zh-v1.5

# 数据路径配置
DATA_ROOT=./dataset
DATABASE_ROOT=./database
