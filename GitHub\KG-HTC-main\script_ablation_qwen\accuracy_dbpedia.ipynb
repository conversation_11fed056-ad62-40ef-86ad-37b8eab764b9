{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from sklearn.metrics import f1_score, accuracy_score\n", "from sklearn.metrics import classification_report\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "# Calculate confusion matrix for the most confused classes\n", "from sklearn.metrics import confusion_matrix\n", "import numpy as np\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# read the json file\n", "df = pd.read_json(\"../dataset/open_llm/dbpedia_qwen7b.json\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.6994322162962701, 0.8606)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"l1\"] = df[\"l1\"].str.lower()\n", "df[\"qwen7b_l1\"] = df[\"qwen7b_l1\"].str.lower()\n", "df[\"l1\"] = df[\"l1\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l1\"] = df[\"qwen7b_l1\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l1\"] = df[\"qwen7b_l1\"].apply(lambda x: x if x in df[\"l1\"].values else df[\"l1\"].sample(1, random_state=42).values[0])\n", "f1_l1 = f1_score(df[\"qwen7b_l1\"], df[\"l1\"], average=\"macro\")\n", "acc_l1 = accuracy_score(df[\"qwen7b_l1\"], df[\"l1\"])\n", "f1_l1, acc_l1"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.5752255759538674, 0.6328)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"l2\"] = df[\"l2\"].str.lower()\n", "df[\"qwen7b_l2\"] = df[\"qwen7b_l2\"].str.lower()\n", "df[\"l2\"] = df[\"l2\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l2\"] = df[\"qwen7b_l2\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l2\"] = df[\"qwen7b_l2\"].apply(lambda x: x if x in df[\"l2\"].values else df[\"l2\"].sample(1, random_state=42).values[0])\n", "f1_l2 = f1_score(df[\"qwen7b_l2\"], df[\"l2\"], average=\"macro\")\n", "acc_l2 = accuracy_score(df[\"qwen7b_l2\"], df[\"l2\"])\n", "f1_l2, acc_l2"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.7993500523054101, 0.8078)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"l3\"] = df[\"l3\"].str.lower()\n", "df[\"qwen7b_l3\"] = df[\"qwen7b_l3\"].str.lower()\n", "df[\"l3\"] = df[\"l3\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l3\"] = df[\"qwen7b_l3\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l3\"] = df[\"qwen7b_l3\"].apply(lambda x: x if x in df[\"l3\"].values else df[\"l3\"].sample(1, random_state=42).values[0])\n", "\n", "f1_l3 = f1_score(df[\"qwen7b_l3\"], df[\"l3\"], average=\"macro\")\n", "acc_l3 = accuracy_score(df[\"qwen7b_l3\"], df[\"l3\"])\n", "f1_l3, acc_l3"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# read the json file\n", "df = pd.read_json(\"../dataset/open_llm/dbpedia_qwen7b_only.json\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.47260637259866844, 0.6328)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"l1\"] = df[\"l1\"].str.lower()\n", "df[\"qwen7b_l1\"] = df[\"qwen7b_l1\"].str.lower()\n", "df[\"l1\"] = df[\"l1\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l1\"] = df[\"qwen7b_l1\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l1\"] = df[\"qwen7b_l1\"].apply(lambda x: x if x in df[\"l1\"].values else df[\"l1\"].sample(1, random_state=42).values[0])\n", "f1_l1 = f1_score(df[\"qwen7b_l1\"], df[\"l1\"], average=\"macro\")\n", "acc_l1 = accuracy_score(df[\"qwen7b_l1\"], df[\"l1\"])\n", "f1_l1, acc_l1"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.46638377798772046, 0.602)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"l2\"] = df[\"l2\"].str.lower()\n", "df[\"qwen7b_l2\"] = df[\"qwen7b_l2\"].str.lower()\n", "df[\"l2\"] = df[\"l2\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l2\"] = df[\"qwen7b_l2\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l2\"] = df[\"qwen7b_l2\"].apply(lambda x: x if x in df[\"l2\"].values else df[\"l2\"].sample(1, random_state=42).values[0])\n", "f1_l2 = f1_score(df[\"qwen7b_l2\"], df[\"l2\"], average=\"macro\")\n", "acc_l2 = accuracy_score(df[\"qwen7b_l2\"], df[\"l2\"])\n", "f1_l2, acc_l2"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.6911494619952807, 0.7048)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"l3\"] = df[\"l3\"].str.lower()\n", "df[\"qwen7b_l3\"] = df[\"qwen7b_l3\"].str.lower()\n", "df[\"l3\"] = df[\"l3\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l3\"] = df[\"qwen7b_l3\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l3\"] = df[\"qwen7b_l3\"].apply(lambda x: x if x in df[\"l3\"].values else df[\"l3\"].sample(1, random_state=42).values[0])\n", "\n", "f1_l3 = f1_score(df[\"qwen7b_l3\"], df[\"l3\"], average=\"macro\")\n", "acc_l3 = accuracy_score(df[\"qwen7b_l3\"], df[\"l3\"])\n", "f1_l3, acc_l3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ollama", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}