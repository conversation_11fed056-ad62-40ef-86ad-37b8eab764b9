#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化测试新的 QdrantVectorDB 实现
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """最小化测试"""
    print("🚀 最小化测试开始")
    
    try:
        # 导入模块
        print("1. 导入模块...")
        from src.qdrant_vector_db import QdrantVectorDB
        print("✓ 模块导入成功")
        
        # 初始化
        print("2. 初始化向量数据库...")
        vector_db = QdrantVectorDB(
            collection_name="minimal_test",
            use_memory=True,
            auto_persist=False
        )
        print("✓ 初始化成功")
        
        # 获取集合信息
        print("3. 获取集合信息...")
        info = vector_db.get_collection_info()
        print(f"✓ 集合信息: {info['main_collection']['name']}")
        
        print("\n🎉 最小化测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
