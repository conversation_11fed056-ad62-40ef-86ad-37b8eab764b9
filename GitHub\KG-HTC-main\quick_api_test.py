#!/usr/bin/env python3
"""
快速API连接测试脚本
简化版本，用于快速验证OpenAI兼容接口的连接状态
"""

import os
import requests
import json
import time
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_chat_completion(base_url, api_key, model, is_azure=False):
    """测试聊天完成接口"""
    print(f"\n🔍 测试推理模型: {model}")
    
    if is_azure:
        url = f"{base_url}/openai/deployments/{model}/chat/completions"
        headers = {
            "api-key": api_key,
            "Content-Type": "application/json"
        }
        params = {"api-version": os.getenv("API_VERSION", "2023-12-01-preview")}
    else:
        url = f"{base_url}/chat/completions"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        params = {}
    
    data = {
        "model": model,
        "messages": [{"role": "user", "content": "Say 'API connection successful!'"}],
        "max_tokens": 20,
        "temperature": 0
    }
    
    try:
        start_time = time.time()
        response = requests.post(url, json=data, headers=headers, params=params, timeout=30)
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
            print(f"✅ 推理模型连接成功!")
            print(f"   响应时间: {response_time:.2f}秒")
            print(f"   模型回复: {content.strip()}")
            return True
        else:
            print(f"❌ 推理模型连接失败!")
            print(f"   状态码: {response.status_code}")
            print(f"   错误信息: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ 推理模型连接失败!")
        print(f"   错误: {str(e)}")
        return False

def test_embeddings(base_url, api_key, model, is_azure=False):
    """测试嵌入接口"""
    print(f"\n🔍 测试向量化模型: {model}")
    
    if is_azure:
        url = f"{base_url}/openai/deployments/{model}/embeddings"
        headers = {
            "api-key": api_key,
            "Content-Type": "application/json"
        }
        params = {"api-version": os.getenv("API_VERSION", "2023-12-01-preview")}
    else:
        url = f"{base_url}/embeddings"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        params = {}
    
    data = {
        "model": model,
        "input": ["test embedding"],
        "encoding_format": "float"
    }
    
    try:
        start_time = time.time()
        response = requests.post(url, json=data, headers=headers, params=params, timeout=30)
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            embeddings = result.get("data", [])
            if embeddings:
                dim = len(embeddings[0].get("embedding", []))
                print(f"✅ 向量化模型连接成功!")
                print(f"   响应时间: {response_time:.2f}秒")
                print(f"   向量维度: {dim}")
                return True
            else:
                print(f"❌ 向量化模型响应格式错误!")
                return False
        else:
            print(f"❌ 向量化模型连接失败!")
            print(f"   状态码: {response.status_code}")
            print(f"   错误信息: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ 向量化模型连接失败!")
        print(f"   错误: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 OpenAI API快速连接测试")
    print("=" * 50)
    
    # 检测配置类型
    configs = []
    
    
    # 自定义接口
    if os.getenv("CUSTOM_BASE_URL"):
        configs.append({
            "name": "自定义接口",
            "base_url": os.getenv("CUSTOM_BASE_URL"),
            "api_key": os.getenv("CUSTOM_API_KEY", "dummy-key"),
            "inference_model": os.getenv("CUSTOM_INFERENCE_MODEL", "gpt-3.5-turbo"),
            "embedding_model": os.getenv("CUSTOM_EMBEDDING_MODEL", "text-embedding-ada-002"),
            "is_azure": False
        })
    
    if not configs:
        print("❌ 未找到任何API配置!")
        print("请检查 .env 文件中的配置")
        print("参考 .env.example.api_test 文件进行配置")
        return
    
    # 测试所有配置
    total_tests = 0
    passed_tests = 0
    
    for config in configs:
        print(f"\n📡 测试配置: {config['name']}")
        print(f"   API地址: {config['base_url']}")
        
        # 测试推理模型
        if test_chat_completion(
            config['base_url'], 
            config['api_key'], 
            config['inference_model'],
            config['is_azure']
        ):
            passed_tests += 1
        total_tests += 1
        
        # 测试向量化模型
        if config['embedding_model']:
            if test_embeddings(
                config['base_url'],
                config['api_key'],
                config['embedding_model'],
                config['is_azure']
            ):
                passed_tests += 1
            total_tests += 1
        else:
            print(f"\n⚠️  跳过向量化模型测试 (未配置)")
    
    # 总结
    print(f"\n" + "=" * 50)
    print(f"📊 测试总结")
    print(f"   总测试数: {total_tests}")
    print(f"   通过数: {passed_tests}")
    print(f"   失败数: {total_tests - passed_tests}")
    
    if total_tests > 0:
        success_rate = (passed_tests / total_tests) * 100
        print(f"   成功率: {success_rate:.1f}%")
        
        if success_rate == 100:
            print(f"🎉 所有测试通过! API连接正常")
        elif success_rate >= 50:
            print(f"⚠️  部分测试通过，请检查失败的配置")
        else:
            print(f"❌ 大部分测试失败，请检查网络和配置")
    
    print(f"\n💡 提示:")
    print(f"   - 确保API密钥有效且有足够余额")
    print(f"   - 检查网络连接和防火墙设置")
    print(f"   - 参考 .env.example.api_test 配置环境变量")

if __name__ == "__main__":
    main()
