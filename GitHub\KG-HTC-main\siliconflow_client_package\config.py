"""
SiliconFlow API 配置文件
集中管理API密钥、模型配置和其他设置
"""

import os
from typing import Dict, Any


class SiliconFlowConfig:
    """
    SiliconFlow API 配置管理类
    支持从环境变量、配置文件或直接设置获取配置信息
    """
    
    # 默认配置
    DEFAULT_CONFIG = {
        # API 基础配置
        "base_url": "https://api.siliconflow.cn/v1",
        "api_key": "",  # 需要用户设置
        
        # 推理模型配置
        "chat_models": {
            "default": "Qwen/Qwen2.5-7B-Instruct",
            "available": [
                "Qwen/Qwen2.5-7B-Instruct",
                "Qwen/Qwen2.5-14B-Instruct",
                "Qwen/Qwen2.5-32B-Instruct",
                "Qwen/Qwen2.5-72B-Instruct",
                "meta-llama/Meta-Llama-3.1-8B-Instruct",
                "meta-llama/Meta-Llama-3.1-70B-Instruct",
                "deepseek-ai/DeepSeek-V2.5",
                "01-ai/Yi-1.5-9B-Chat-16K",
                "01-ai/Yi-1.5-34B-Chat-16K"
            ]
        },
        
        # 嵌入模型配置
        "embedding_models": {
            "default": "BAAI/bge-m3",
            "available": [
                "BAAI/bge-m3",
                "BAAI/bge-large-zh-v1.5",
                "BAAI/bge-large-en-v1.5",
                "sentence-transformers/all-MiniLM-L6-v2",
                "jinaai/jina-embeddings-v2-base-zh",
                "jinaai/jina-embeddings-v2-base-en"
            ]
        },
        
        # 默认参数配置
        "default_chat_params": {
            "temperature": 0.7,
            "top_p": 1.0,
            "max_tokens": 2048,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "stream": False
        },
        
        "default_embedding_params": {
            "encoding_format": "float"
        },
        
        # 请求配置
        "request_config": {
            "timeout": 30,  # 请求超时时间（秒）
            "max_retries": 3,  # 最大重试次数
            "verify_ssl": False,  # 是否验证SSL证书
            "disable_warnings": True  # 是否禁用SSL警告
        },
        
        # 日志配置
        "logging": {
            "enabled": True,
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        }
    }
    
    def __init__(self, config_dict: Dict[str, Any] = None):
        """
        初始化配置管理器
        
        Args:
            config_dict (Dict[str, Any], optional): 自定义配置字典
        """
        self.config = self.DEFAULT_CONFIG.copy()
        
        # 从环境变量加载配置
        self._load_from_env()
        
        # 应用自定义配置
        if config_dict:
            self._update_config(config_dict)
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        # API密钥
        env_api_key = os.getenv("SILICONFLOW_API_KEY")
        if env_api_key:
            self.config["api_key"] = env_api_key
        
        # 基础URL
        env_base_url = os.getenv("SILICONFLOW_BASE_URL")
        if env_base_url:
            self.config["base_url"] = env_base_url
        
        # 默认聊天模型
        env_chat_model = os.getenv("SILICONFLOW_CHAT_MODEL")
        if env_chat_model:
            self.config["chat_models"]["default"] = env_chat_model
        
        # 默认嵌入模型
        env_embedding_model = os.getenv("SILICONFLOW_EMBEDDING_MODEL")
        if env_embedding_model:
            self.config["embedding_models"]["default"] = env_embedding_model
    
    def _update_config(self, config_dict: Dict[str, Any]):
        """
        递归更新配置
        
        Args:
            config_dict (Dict[str, Any]): 要更新的配置字典
        """
        def deep_update(base_dict, update_dict):
            for key, value in update_dict.items():
                if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value
        
        deep_update(self.config, config_dict)
    
    def get(self, key: str, default=None):
        """
        获取配置值，支持点号分隔的嵌套键
        
        Args:
            key (str): 配置键，支持 "chat_models.default" 格式
            default: 默认值
            
        Returns:
            配置值或默认值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """
        设置配置值，支持点号分隔的嵌套键
        
        Args:
            key (str): 配置键
            value (Any): 配置值
        """
        keys = key.split('.')
        config = self.config
        
        # 导航到最后一级的父字典
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置最终值
        config[keys[-1]] = value
    
    def get_api_key(self) -> str:
        """获取API密钥"""
        return self.config["api_key"]
    
    def get_base_url(self) -> str:
        """获取基础URL"""
        return self.config["base_url"]
    
    def get_default_chat_model(self) -> str:
        """获取默认聊天模型"""
        return self.config["chat_models"]["default"]
    
    def get_default_embedding_model(self) -> str:
        """获取默认嵌入模型"""
        return self.config["embedding_models"]["default"]
    
    def get_available_chat_models(self) -> list:
        """获取可用的聊天模型列表"""
        return self.config["chat_models"]["available"]
    
    def get_available_embedding_models(self) -> list:
        """获取可用的嵌入模型列表"""
        return self.config["embedding_models"]["available"]
    
    def get_default_chat_params(self) -> Dict[str, Any]:
        """获取默认聊天参数"""
        return self.config["default_chat_params"].copy()
    
    def get_default_embedding_params(self) -> Dict[str, Any]:
        """获取默认嵌入参数"""
        return self.config["default_embedding_params"].copy()
    
    def get_request_config(self) -> Dict[str, Any]:
        """获取请求配置"""
        return self.config["request_config"].copy()
    
    def validate(self) -> tuple[bool, str]:
        """
        验证配置的有效性
        
        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        # 检查API密钥
        if not self.config["api_key"]:
            return False, "API密钥未设置，请设置环境变量 SILICONFLOW_API_KEY 或在配置中指定"
        
        # 检查基础URL
        if not self.config["base_url"]:
            return False, "基础URL未设置"
        
        # 检查默认模型是否在可用列表中
        default_chat_model = self.config["chat_models"]["default"]
        available_chat_models = self.config["chat_models"]["available"]
        if default_chat_model not in available_chat_models:
            return False, f"默认聊天模型 '{default_chat_model}' 不在可用模型列表中"
        
        default_embedding_model = self.config["embedding_models"]["default"]
        available_embedding_models = self.config["embedding_models"]["available"]
        if default_embedding_model not in available_embedding_models:
            return False, f"默认嵌入模型 '{default_embedding_model}' 不在可用模型列表中"
        
        return True, "配置验证通过"
    
    def to_dict(self) -> Dict[str, Any]:
        """返回完整的配置字典"""
        return self.config.copy()
    
    def print_config(self):
        """打印当前配置（隐藏敏感信息）"""
        import json
        
        # 创建配置副本并隐藏敏感信息
        safe_config = self.to_dict()
        if safe_config["api_key"]:
            safe_config["api_key"] = f"{safe_config['api_key'][:8]}..." if len(safe_config['api_key']) > 8 else "***"
        
        print("📋 当前SiliconFlow配置:")
        print("=" * 50)
        print(json.dumps(safe_config, indent=2, ensure_ascii=False))


# 创建全局配置实例
config = SiliconFlowConfig()


def load_config_from_file(file_path: str) -> SiliconFlowConfig:
    """
    从JSON文件加载配置
    
    Args:
        file_path (str): 配置文件路径
        
    Returns:
        SiliconFlowConfig: 配置实例
    """
    import json
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        return SiliconFlowConfig(config_dict)
    except FileNotFoundError:
        print(f"⚠️  配置文件 {file_path} 不存在，使用默认配置")
        return SiliconFlowConfig()
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件格式错误: {e}")
        return SiliconFlowConfig()


def save_config_to_file(config_instance: SiliconFlowConfig, file_path: str):
    """
    将配置保存到JSON文件
    
    Args:
        config_instance (SiliconFlowConfig): 配置实例
        file_path (str): 保存路径
    """
    import json
    
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(config_instance.to_dict(), f, indent=2, ensure_ascii=False)
        print(f"✅ 配置已保存到 {file_path}")
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")


if __name__ == "__main__":
    # 配置使用示例
    print("🔧 SiliconFlow 配置管理示例")
    print("=" * 50)
    
    # 创建配置实例
    my_config = SiliconFlowConfig()
    
    # 设置API密钥
    my_config.set("api_key", "your-api-key-here")
    
    # 验证配置
    is_valid, message = my_config.validate()
    print(f"配置验证: {message}")
    
    # 打印配置
    my_config.print_config()
    
    # 获取特定配置
    print(f"\n默认聊天模型: {my_config.get_default_chat_model()}")
    print(f"可用聊天模型: {my_config.get_available_chat_models()}")
    print(f"默认嵌入模型: {my_config.get_default_embedding_model()}")
