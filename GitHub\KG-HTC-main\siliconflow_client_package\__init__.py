"""
SiliconFlow API 客户端包
基于原始测试代码封装的推理模型和嵌入模型调用类
提供兼容 OpenAI 的接口设计

主要模块:
- siliconflow_client: 核心客户端类
- config: 配置管理模块

主要类:
- SiliconFlowClient: 统一客户端（推荐使用）
- SiliconFlowChatClient: 推理模型客户端
- SiliconFlowEmbeddingClient: 嵌入模型客户端
- SiliconFlowConfig: 配置管理类

使用示例:
    from siliconflow_client_package import SiliconFlowClient
    
    # 创建客户端
    client = SiliconFlowClient(api_key="your-api-key")
    
    # 推理模型调用
    response = client.chat.simple_chat("你好")
    
    # 嵌入模型调用
    vector = client.embeddings.get_embedding_vector("测试文本")
"""

__version__ = "1.0.0"
__author__ = "基于原始测试代码封装"
__description__ = "SiliconFlow API 客户端包，兼容 OpenAI 接口设计"

# 导入主要类，方便用户直接使用
try:
    from .siliconflow_client import (
        SiliconFlowClient,
        SiliconFlowChatClient, 
        SiliconFlowEmbeddingClient
    )
    from .config import SiliconFlowConfig
    
    # 定义包的公共接口
    __all__ = [
        'SiliconFlowClient',
        'SiliconFlowChatClient',
        'SiliconFlowEmbeddingClient', 
        'SiliconFlowConfig'
    ]
    
except ImportError as e:
    # 如果导入失败，提供友好的错误信息
    import warnings
    warnings.warn(f"部分模块导入失败: {e}. 请检查依赖是否正确安装。", ImportWarning)
    
    __all__ = []

# 包信息
def get_version():
    """获取包版本"""
    return __version__

def get_info():
    """获取包信息"""
    return {
        "name": "siliconflow_client_package",
        "version": __version__,
        "author": __author__,
        "description": __description__,
        "modules": __all__
    }

# 便捷函数
def create_client(api_key=None, **kwargs):
    """
    便捷函数：创建 SiliconFlow 客户端
    
    Args:
        api_key (str, optional): API 密钥
        **kwargs: 其他参数
        
    Returns:
        SiliconFlowClient: 客户端实例
    """
    try:
        return SiliconFlowClient(api_key=api_key, **kwargs)
    except NameError:
        raise ImportError("SiliconFlowClient 未能正确导入，请检查依赖")

def quick_test(api_key=None):
    """
    便捷函数：快速测试连接
    
    Args:
        api_key (str, optional): API 密钥
        
    Returns:
        dict: 测试结果
    """
    try:
        client = create_client(api_key=api_key)
        return client.test_connection()
    except Exception as e:
        return {"error": str(e), "chat": False, "embeddings": False}
