{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from sklearn.metrics import f1_score, accuracy_score\n", "from sklearn.metrics import classification_report\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "# Calculate confusion matrix for the most confused classes\n", "from sklearn.metrics import confusion_matrix\n", "import numpy as np\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# read the json file\n", "df = pd.read_json(\"../dataset/open_llm/wos_qwen7b.json\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.7153570240603401, 0.7276)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"Domain\"] = df[\"Domain\"].str.lower()\n", "df[\"qwen7b_l1\"] = df[\"qwen7b_l1\"].str.lower()\n", "df[\"Domain\"] = df[\"Domain\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l1\"] = df[\"qwen7b_l1\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l1\"] = df[\"qwen7b_l1\"].apply(lambda x: x if x in df[\"Domain\"].values else df[\"Domain\"].sample(1, random_state=42).values[0])\n", "f1_l1 = f1_score(df[\"qwen7b_l1\"], df[\"Domain\"], average=\"macro\")\n", "acc_l1 = accuracy_score(df[\"qwen7b_l1\"], df[\"Domain\"])\n", "f1_l1, acc_l1"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.5097783810611833, 0.5036)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"area\"] = df[\"area\"].str.lower()\n", "df[\"qwen7b_l2\"] = df[\"qwen7b_l2\"].str.lower()\n", "df[\"area\"] = df[\"area\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l2\"] = df[\"qwen7b_l2\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l2\"] = df[\"qwen7b_l2\"].apply(lambda x: x if x in df[\"area\"].values else df[\"area\"].sample(1, random_state=42).values[0])\n", "f1_l2 = f1_score(df[\"qwen7b_l2\"], df[\"area\"], average=\"macro\")\n", "acc_l2 = accuracy_score(df[\"qwen7b_l2\"], df[\"area\"])\n", "f1_l2, acc_l2"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# read the json file\n", "df = pd.read_json(\"../dataset/open_llm/wos_qwen7b_only.json\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.5502059781330896, 0.616)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"Domain\"] = df[\"Domain\"].str.lower()\n", "df[\"qwen7b_l1\"] = df[\"qwen7b_l1\"].str.lower()\n", "df[\"Domain\"] = df[\"Domain\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l1\"] = df[\"qwen7b_l1\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l1\"] = df[\"qwen7b_l1\"].apply(lambda x: x if x in df[\"Domain\"].values else df[\"Domain\"].sample(1, random_state=42).values[0])\n", "f1_l1 = f1_score(df[\"qwen7b_l1\"], df[\"Domain\"], average=\"macro\")\n", "acc_l1 = accuracy_score(df[\"qwen7b_l1\"], df[\"Domain\"])\n", "f1_l1, acc_l1"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.3670910527519334, 0.3768)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"area\"] = df[\"area\"].str.lower()\n", "df[\"qwen7b_l2\"] = df[\"qwen7b_l2\"].str.lower()\n", "df[\"area\"] = df[\"area\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l2\"] = df[\"qwen7b_l2\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"qwen7b_l2\"] = df[\"qwen7b_l2\"].apply(lambda x: x if x in df[\"area\"].values else df[\"area\"].sample(1, random_state=42).values[0])\n", "f1_l2 = f1_score(df[\"qwen7b_l2\"], df[\"area\"], average=\"macro\")\n", "acc_l2 = accuracy_score(df[\"qwen7b_l2\"], df[\"area\"])\n", "f1_l2, acc_l2"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.6357257229217411, 0.6576)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# read the json file\n", "df_l2 = pd.read_json(\"../dataset/wos/llm_only_gpt3_l2.json\")\n", "df_l2 = df_l2.sample(n=5000, random_state=42)\n", "df_l1 = pd.read_json(\"../dataset/wos/llm_only_gpt3_l1.json\")\n", "df_l1 = df_l1.sample(n=5000, random_state=42)\n", "df_l1[\"Domain\"] = df_l1[\"Domain\"].str.lower()\n", "df_l1[\"gpt3_only_l1\"] = df_l1[\"gpt3_only_l1\"].str.lower()\n", "df_l1[\"Domain\"] = df_l1[\"Domain\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df_l1[\"gpt3_only_l1\"] = df_l1[\"gpt3_only_l1\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df_l1[\"gpt3_only_l1\"] = df_l1[\"gpt3_only_l1\"].apply(lambda x: x if x in df_l1[\"Domain\"].values else df_l1[\"Domain\"].sample(1, random_state=42).values[0])\n", "f1_l1 = f1_score(df_l1[\"gpt3_only_l1\"], df_l1[\"Domain\"], average=\"macro\")\n", "acc_l1 = accuracy_score(df_l1[\"gpt3_only_l1\"], df_l1[\"Domain\"])\n", "f1_l1, acc_l1"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.550273569928933, 0.631)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df_l2[\"area\"] = df_l2[\"area\"].str.lower()\n", "df_l2[\"gpt3_only_l2\"] = df_l2[\"gpt3_only_l2\"].str.lower()\n", "df_l2[\"area\"] = df_l2[\"area\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df_l2[\"gpt3_only_l2\"] = df_l2[\"gpt3_only_l2\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df_l2[\"gpt3_only_l2\"] = df_l2[\"gpt3_only_l2\"].apply(lambda x: x if x in df_l2[\"area\"].values else df_l2[\"area\"].sample(1, random_state=42).values[0])\n", "f1_l2 = f1_score(df_l2[\"gpt3_only_l2\"], df_l2[\"area\"], average=\"macro\")\n", "acc_l2 = accuracy_score(df_l2[\"gpt3_only_l2\"], df_l2[\"area\"])\n", "f1_l2, acc_l2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ollama", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}