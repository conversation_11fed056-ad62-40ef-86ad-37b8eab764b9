import chromadb.utils.embedding_functions as embedding_functions
import chromadb
from typing import Optional, List
import os
import dotenv
from sentence_transformers import SentenceTransformer
import numpy as np
from openai import OpenAI

import httpx
# 1. 配置自定义 httpx Client，禁用 SSL 验证（仅测试用）
custom_httpx_client = httpx.Client(
    verify=False,  # 忽略 SSL 证书验证（测试环境临时使用）
    timeout=httpx.Timeout(10.0)  # 可选：设置超时时间
)

OPENAI_API_KEY="sk-kxqclwblxrbprnibbwxfachyqxnivhocpisdjiciniqckvll"
OPENAI_BASE_URL="https://api.siliconflow.cn/v1/"
DEPLOYMENT_NAME="Qwen/Qwen2.5-7B-Instruct"
# 向量化模型名称
OPENAI_EMBEDDING_MODEL="BAAI/bge-m3"

class CustomBGEEmbeddingFunction:
    """
    自定义BGE本地嵌入函数
    
    使用sentence-transformers库加载BGE模型，实现本地文本向量化
    BGE (BAAI General Embedding) 是一个优秀的中文/英文双语embedding模型
    """
    def __init__(self, model_name: str = "BAAI/bge-large-zh-v1.5"):
        """
        初始化BGE嵌入模型
        
        Args:
            model_name: 模型名称，默认使用"BAAI/bge-large-zh-v1.5"
                       可选其他模型如"BAAI/bge-base-zh-v1.5"(更小更快)
                       或"BAAI/bge-large-en-v1.5"(英文版本)
        """
        # 加载模型到内存中
        self.model = SentenceTransformer(model_name)
    
    def __call__(self, input: List[str]) -> List[List[float]]:
        """
        将输入文本转换为向量表示
        
        Args:
            input: 文本列表
            
        Returns:
            文本向量列表，每个向量是一个浮点数列表
        """
        # normalize_embeddings=True确保所有向量被归一化到单位长度，便于余弦相似度计算
        embeddings = self.model.encode(input, normalize_embeddings=True)
        return embeddings.tolist()  # 转换为Python列表以便序列化

class CustomOpenAIEmbeddingFunction:
    """
    自定义OpenAI嵌入函数
    
    使用OpenAI库直接调用嵌入模型API
    支持OpenAI官方API和兼容OpenAI接口的第三方服务
    """
    def __init__(self, api_key: str, base_url: str, model_name: str):
        """
        初始化OpenAI嵌入客户端
        
        Args:
            api_key: API密钥
            base_url: API基础URL
            model_name: 嵌入模型名称
        """
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url,
        )
        self.model_name = model_name
        self.name = f"custom_openai_{model_name}"  # 添加name属性
    
    def __call__(self, input: List[str]) -> List[List[float]]:
        """
        将输入文本转换为向量表示
        
        Args:
            input: 文本列表
            
        Returns:
            文本向量列表，每个向量是一个浮点数列表
        """
        try:
            response = self.client.embeddings.create(
                model=self.model_name,
                input=input
            )
            return [data.embedding for data in response.data]
        except Exception as e:
            print(f"嵌入模型调用失败: {e}")
            raise e
    
    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        获取文本嵌入向量的便捷方法
        
        Args:
            texts: 文本列表
            
        Returns:
            嵌入向量列表
        """
        return self.__call__(texts)
    
    def get_single_embedding(self, text: str) -> List[float]:
        """
        获取单个文本的嵌入向量
        
        Args:
            text: 单个文本
            
        Returns:
            单个嵌入向量
        """
        result = self.__call__([text])
        return result[0] if result else []
    
    def test_connection(self) -> bool:
        """
        测试API连接是否正常
        
        Returns:
            连接状态，True表示正常，False表示异常
        """
        try:
            test_result = self.get_single_embedding("测试连接")
            return len(test_result) > 0
        except Exception as e:
            print(f"连接测试失败: {e}")
            return False

class VectorDB:
    """
    向量数据库管理类
    
    封装了ChromaDB的操作，支持本地BGE模型和OpenAI API两种向量化方式
    提供了添加、查询、删除、更新等功能
    """
    def __init__(self, database_path: str, collection_name: str, embedding_type: str = "openai"):
        """
        初始化向量数据库
        
        Args:
            database_path: 数据库存储路径
            collection_name: 集合名称，用于区分不同的数据集
            embedding_type: 嵌入类型，可选值:
                          - "local": 使用本地BGE模型，无需联网，速度取决于本地硬件
                          - "openai": 使用OpenAI API，需要联网和API密钥，通常更准确
        """
        self._collection_name = collection_name
        self._database_path = database_path
        self._embedding_type = embedding_type
        
        # 根据类型选择嵌入函数
        if embedding_type == "local":
            # 本地BGE模型
            self._embed_func = CustomBGEEmbeddingFunction(
                model_name=os.getenv("BGE_MODEL_NAME", "BAAI/bge-large-zh-v1.5")
            )
        elif embedding_type == "openai":
            # 使用ChromaDB内置的OpenAI函数
            self._embed_func = embedding_functions.OpenAIEmbeddingFunction(
                api_key=OPENAI_API_KEY,
                api_base=OPENAI_BASE_URL,
                model_name=OPENAI_EMBEDDING_MODEL,
            )
        elif embedding_type == "custom_openai":
            # 使用自定义OpenAI函数
            self._embed_func = CustomOpenAIEmbeddingFunction(
                api_key=OPENAI_API_KEY,
                base_url=OPENAI_BASE_URL,
                model_name=OPENAI_EMBEDDING_MODEL
            )
        else:
            raise ValueError(f"不支持的嵌入类型: {embedding_type}，请使用 'local'、'openai' 或 'custom_openai'")

        # 初始化ChromaDB客户端，使用持久化存储
        self._client = chromadb.PersistentClient(path=self._database_path)
        
        # 获取或创建集合，配置嵌入函数和相似度空间
        self._collection = self._client.get_or_create_collection(
            name=self._collection_name,
            metadata={"hnsw:space": "cosine"},  # 使用余弦相似度空间
            embedding_function=self._embed_func  # 设置嵌入函数
        )

    def batch_add(self, texts: List[str], metadatas: Optional[List[dict]] = None):
            """
            批量添加文档到向量数据库
            
            Args:
                texts: 文本列表，每个文本将被转换为向量
                metadatas: 元数据列表，与texts一一对应，用于存储额外信息
                        如分类级别、来源等，便于后续过滤查询
            """
            if not texts:
                return  # 空列表直接返回
                
            # 获取当前集合中的文档数量，用于生成连续的ID
            current_num = len(self._collection.get()["documents"]) if self._collection.get()["documents"] else 0
            
            # 循环调用单个添加方法
            for i, text in enumerate(texts):
                metadata = metadatas[i] if metadatas and i < len(metadatas) else None
                self.add_single(text, metadata, custom_id=f"{current_num + i:06d}")
        
    def add_single(self, text: str, metadata: Optional[dict] = None, custom_id: Optional[str] = None):
        """
        添加单个文档到向量数据库
        
        Args:
            text: 文本内容
            metadata: 文档元数据，如分类信息
            custom_id: 可选的自定义ID，如果不提供则自动生成
        """
        if not text:
            return  # 空文本直接返回
            
        # 获取当前集合中的文档数量，用于生成ID
        current_num = len(self._collection.get()["documents"]) if self._collection.get()["documents"] else 0
        
        # 使用提供的ID或生成新ID
        doc_id = custom_id if custom_id is not None else f"{current_num:06d}"
        
        # 添加单个文档到集合
        self._collection.add(
            ids=[doc_id],  # 文档ID
            documents=[text],  # 文档内容
            metadatas=[metadata] if metadata is not None else None  # 元数据
        )
        
    def _query_by_text(
        self, 
        query_text: str, 
        n_results: int = 10, 
        where: Optional[dict] = None
    ) -> dict:
        """
        根据文本查询相似文档的内部方法
        
        Args:
            query_text: 查询文本
            n_results: 返回结果数量
            where: 元数据过滤条件，如{"level": "Category1"}
            
        Returns:
            查询结果字典，包含documents, metadatas, distances等字段
        """
        # 调用ChromaDB的查询方法
        return self._collection.query(
            query_texts=[query_text],  # 查询文本
            n_results=n_results,  # 返回结果数量
            where=where,  # 元数据过滤条件
            # 包含的返回字段
            include=["metadatas", "embeddings", "documents", "distances"],
        )
    
    def query_l1(self, query_text: str, n_results: int = 10) -> dict:
        """
        查询一级分类
        
        Args:
            query_text: 查询文本
            n_results: 返回结果数量
            
        Returns:
            与查询文本最相似的一级分类列表
        """
        # 使用元数据过滤，只查询一级分类
        return self._query_by_text(
            query_text,
            n_results=n_results,
        )
    
    def query_l2(self, query_text: str, n_results: int = 10) -> dict:
        """
        查询二级分类
        
        Args:
            query_text: 查询文本
            n_results: 返回结果数量
            
        Returns:
            与查询文本最相似的二级分类列表
        """
        # 使用元数据过滤，只查询二级分类
        return self._query_by_text(
            query_text,
            n_results=n_results,
            where={"level": "Category2"}  # 只查询二级分类
        )
    
    def query_l3(self, query_text: str, n_results: int = 10) -> dict:
        """
        查询三级分类
        
        Args:
            query_text: 查询文本
            n_results: 返回结果数量
            
        Returns:
            与查询文本最相似的三级分类列表
        """
        # 使用元数据过滤，只查询三级分类
        return self._query_by_text(
            query_text,
            n_results=n_results,
            where={"level": "Category3"}  # 只查询三级分类
        )
    
    def query_all_levels(self, query_text: str, n_results: int = 10) -> dict:
        """
        查询所有级别的分类
        
        Args:
            query_text: 查询文本
            n_results: 返回结果数量
            
        Returns:
            与查询文本最相似的所有分类列表，不区分级别
        """
        # 不使用元数据过滤，查询所有级别
        return self._query_by_text(query_text, n_results=n_results)
    
    def delete_by_ids(self, ids: List[str]):
        """
        根据ID删除文档
        
        Args:
            ids: 要删除的文档ID列表
        """
        # 调用ChromaDB的删除方法，根据ID删除
        self._collection.delete(ids=ids)
    
    def delete_by_metadata(self, where: dict):
        """
        根据元数据删除文档
        
        Args:
            where: 元数据过滤条件，如{"level": "Category1"}
                  将删除所有匹配条件的文档
        """
        # 调用ChromaDB的删除方法，根据元数据过滤条件删除
        self._collection.delete(where=where)
    
    def update_metadata(self, ids: List[str], metadatas: List[dict]):
        """
        更新文档元数据
        
        Args:
            ids: 要更新的文档ID列表
            metadatas: 新的元数据列表，与ids一一对应
        """
        # 调用ChromaDB的更新方法，更新指定ID的文档元数据
        self._collection.update(ids=ids, metadatas=metadatas)
    
    def get_collection_info(self) -> dict:
        """
        获取集合信息
        
        Returns:
            包含集合名称、文档数量和嵌入类型的字典
        """
        # 获取集合数据
        data = self._collection.get()
        
        # 返回集合信息字典
        return {
            "name": self._collection_name,  # 集合名称
            "count": len(data["documents"]) if data["documents"] else 0,  # 文档数量
            "embedding_type": self._embedding_type  # 嵌入类型
        }
    
    def clear_collection(self):
        """
        清空集合
        
        删除并重新创建集合，清除所有数据
        """
        # 删除集合
        self._client.delete_collection(self._collection_name)
        
        # 重新创建集合
        self._collection = self._client.get_or_create_collection(
            name=self._collection_name,
            metadata={"hnsw:space": "cosine"},  # 使用余弦相似度空间
            embedding_function=self._embed_func  # 设置嵌入函数
        )





