import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from statsmodels.tsa.seasonal import seasonal_decompose
from sklearn.preprocessing import StandardScaler

# 加载数据
df = pd.read_excel('1.xlsx')

# 1. 数据清洗
# 确保时间字段格式正确
df['ORDERED_DATE'] = pd.to_datetime(df['ORDERED_DATE'], errors='coerce')
df['APD'] = pd.to_datetime(df['APD'], errors='coerce')
df['SCHEDULED_START_DATE'] = pd.to_datetime(df['SCHEDULED_START_DATE'], errors='coerce')
df['DATE_COMPLETED'] = pd.to_datetime(df['DATE_COMPLETED'], errors='coerce')

# 检查缺失值
print("缺失值统计:")
print(df.isnull().sum())

# 填补缺失值
df['ORDERED_DATE'] = df['ORDERED_DATE'].ffill()
df['APD'] = df['APD'].ffill()
df['SCHEDULED_START_DATE'] = df['SCHEDULED_START_DATE'].ffill()
df['DATE_COMPLETED'] = df['DATE_COMPLETED'].ffill()

import matplotlib.pyplot as plt

# 设置字体，解决中文显示问题
plt.rcParams["font.family"] = ["SimHei", "Microsoft YaHei"]
# 解决负号显示问题（可选）
plt.rcParams["axes.unicode_minus"] = False  # 正确显示负号

# 2. 时间序列趋势分析
# 2.1 订单数量随时间的变化：按月分组
df['ORDERED_MONTH'] = df['ORDERED_DATE'].dt.to_period('M')
monthly_orders = df.groupby('ORDERED_MONTH').size()

# 可视化每月订单数量
plt.figure(figsize=(12, 6))
monthly_orders.plot(kind='line', title='Monthly Orders Over Time')
plt.ylabel('订单数量')
plt.xlabel('时间（月份）')
plt.show()


monthly_orders.index = monthly_orders.index.to_timestamp()



# 2. 若索引是字符串（如 '2023-01'），转换为 datetime：
# monthly_orders.index = pd.to_datetime(monthly_orders.index)

# --------------------------
# 季节性分解与绘图（保持不变）
# --------------------------
monthly_orders_decompose = seasonal_decompose(monthly_orders, model='additive', period=12)

plt.figure(figsize=(12, 8))
monthly_orders_decompose.plot()

plt.show()

# 4. 生产完成日期（DATE_COMPLETED）的时间序列分析
df['COMPLETION_MONTH'] = df['DATE_COMPLETED'].dt.to_period('M')
monthly_completion = df.groupby('COMPLETION_MONTH').size()

# 可视化每月生产完成数量
plt.figure(figsize=(12, 6))
monthly_completion.plot(kind='line', title='Monthly Completed Orders Over Time')
plt.ylabel('生产完成数量')
plt.xlabel('时间（月份）')
plt.show()


# 5. 按季度分析交货周期（APD与ORDERED_DATE之间的差异）
df['Quarter'] = df['APD'].dt.to_period('Q')
df['Order_to_Delivery_Diff'] = (df['APD'] - df['ORDERED_DATE']).dt.days
quarterly_diff = df.groupby('Quarter')['Order_to_Delivery_Diff'].mean()

# 可视化季度交货周期
plt.figure(figsize=(12, 6))
quarterly_diff.plot(kind='bar', title='Quarterly Average Delivery Time')
plt.ylabel('平均交货周期（天）')
plt.xlabel('季度')
plt.show()

# 6. 交货时间的波动性分析（标准差）
delivery_diff_std = df.groupby('Quarter')['Order_to_Delivery_Diff'].std()

# 可视化交货周期的波动性
plt.figure(figsize=(12, 6))
delivery_diff_std.plot(kind='bar', title='Quarterly Delivery Time Volatility')
plt.ylabel('交货周期波动性（天）')
plt.xlabel('季度')
plt.show()

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from itertools import combinations


# --- 1. 数据加载与预处理 ---
def load_and_preprocess_data(file_path, sheet_name=0):
    """
    从指定的Excel文件加载数据并进行预处理。
    
    参数:
    - file_path (str): Excel文件的路径。
    - sheet_name (str or int): 要读取的工作表名称或索引。
    
    返回:
    - DataFrame: 清洗和预处理后的pandas DataFrame。
    """
    print(f"正在从 '{file_path}' 加载数据...")
    try:
        df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
        print("数据加载成功。")
    except FileNotFoundError:
        print(f"错误：文件未找到 '{file_path}'。将使用模拟数据进行演示。")
        return create_mock_data_for_analysis()

    print("\n--- 开始数据清洗与预处理 ---")
    
    # 确保关键列存在
    required_cols = ['CUSTOMER_NAME', 'ORDERED_ITEM', 'DESCRIPTION', 'CPD', 'APD']
    if not all(col in df.columns for col in required_cols):
        print(f"错误：数据中缺少必要的列。需要以下列: {required_cols}")
        print("将使用模拟数据进行演示。")
        return create_mock_data_for_analysis()

    # 处理缺失值
    df['CUSTOMER_NAME'].fillna('未知客户', inplace=True)
    df['ORDERED_ITEM'].fillna('未知商品', inplace=True)
    df['DESCRIPTION'].fillna('', inplace=True) # 文本缺失值用空字符串填充
    
    # 转换日期格式，忽略无法转换的错误
    for col in ['CPD', 'APD', 'ORDERED_DATE']:
        if col in df.columns:
            df[col] = pd.to_datetime(df[col], errors='coerce')
    
    # 删除日期转换后仍为空的行
    df.dropna(subset=['CPD', 'APD'], inplace=True)
    
    print("数据清洗与预处理完成。")
    return df
def create_mock_data_for_analysis(num_records=200):
    """为相关性分析创建一个更具针对性的模拟数据集"""
    print("创建用于分析的模拟数据...")
    customers = ['客户A公司', '客户B科技', '客户C集团', '客户D电子', '客户E国际']
    items = {
        'AI-N100': '高性能AI推理网卡，适用于数据中心',
        'AI-N200': '边缘计算专用AI加速网卡',
        'PHY-G1': '千兆物理层以太网卡，稳定可靠',
        'PHY-G2': '双端口千兆物理层网卡',
        'ACC-01': '通用服务器配件'
    }
    
    data = []
    for i in range(num_records):
        cust = np.random.choice(customers)
        item_key = np.random.choice(list(items.keys()))
        order_date = datetime(2023, 1, 1) + timedelta(days=np.random.randint(0, 360))
        cpd = order_date + timedelta(days=30)
        apd = cpd + timedelta(days=np.random.randint(-5, 10))
        
        data.append({
            'ORDER_NUMBER': f'ORD-{2023000 + i}',
            'CUSTOMER_NAME': cust,
            'ORDERED_ITEM': item_key,
            'DESCRIPTION': items[item_key],
            'ORDERED_DATE': order_date,
            'CPD': cpd,
            'APD': apd,
            'AMOUNT': np.random.uniform(1000, 20000)
        })
    print("模拟数据创建完成。")
    return pd.DataFrame(data)
    # 设置matplotlib以正确显示中文
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
    
    # --- 请在这里修改您的Excel文件路径 ---
    # 示例: excel_file_path = 'C:/Users/<USER>/Documents/订单数据.xlsx'
excel_file_path = '1.xlsx'  # 请替换为您的文件路径

    # 1. 加载和预处理数据
df_cleaned = load_and_preprocess_data(excel_file_path)

def analyze_delivery_performance(df):
    """
    分析供应链交付周期。
    """
    print("\n--- 开始供应链交付周期分析 ---")
    
    # 1. 计算交付延迟天数
    df['DELIVERY_DELAY_DAYS'] = (df['APD'] - df['CPD']).dt.days
    
    # 2. 统计描述
    print("交货延迟天数统计描述:")
    print(df['DELIVERY_DELAY_DAYS'].describe())
    
    # 3. 可视化
    plt.figure(figsize=(12, 6))
    sns.histplot(df['DELIVERY_DELAY_DAYS'], bins=20, kde=True)
    plt.title('交货延迟天数分布', fontsize=16)
    plt.xlabel('延迟天数 (负数为提前)', fontsize=12)
    plt.ylabel('订单频数', fontsize=12)
    plt.axvline(0, color='red', linestyle='--', label='承诺交货日期')
    plt.legend()
    plt.show()
analyze_delivery_performance(df_cleaned)


# -*- coding: utf-8 -*-
"""
完整脚本：多维数据相关性分析与可视化
依赖包：pandas, numpy, seaborn, matplotlib, sklearn, keras, statsmodels
"""

import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler, MinMaxScaler, LabelEncoder
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
from sklearn.metrics import pairwise_distances
from difflib import SequenceMatcher
from keras.models import Sequential
from keras.layers import LSTM, Dense
from statsmodels.graphics.tsaplots import plot_acf, plot_ccf

# 1. 数据加载与清洗
df = pd.read_excel("1.xlsx")
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
# 2. 零部件状态 vs 在制品数量分析
def component_wip_analysis():
    data = df[['COMPONENTS_STATUS_CODE', 'WIP_QTY']].dropna()
    data['WIP_QTY'] = pd.to_numeric(data['WIP_QTY'], errors='coerce')
    grouped = data.groupby('COMPONENTS_STATUS_CODE')['WIP_QTY'].agg(['mean', 'sum', 'count']).reset_index()

    plt.figure(figsize=(10, 5))
    sns.barplot(data=grouped, x='COMPONENTS_STATUS_CODE', y='mean', palette='viridis')
    plt.title('各零部件状态下在制品数量均值')
    plt.ylabel("平均WIP数量")
    plt.xlabel("零部件状态")
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()

# 3. 行业聚类分析
def industry_clustering():
    df_ind = df[['客户行业', 'AMOUNT', 'ORDER_NUMBER', 'ATA', 'ORDERED_DATE']].dropna()
    df_ind['ORDERED_DATE'] = pd.to_datetime(df_ind['ORDERED_DATE'])
    df_ind['ATA'] = pd.to_datetime(df_ind['ATA'])
    df_ind['周期'] = (df_ind['ATA'] - df_ind['ORDERED_DATE']).dt.days
    agg = df_ind.groupby('客户行业').agg({
        'AMOUNT': 'sum',
        'ORDER_NUMBER': 'nunique',
        '周期': 'mean'
    }).rename(columns={'ORDER_NUMBER': '订单数', 'AMOUNT': '总金额', '周期': '平均周期'}).dropna()

    X = StandardScaler().fit_transform(agg)
    kmeans = KMeans(n_clusters=4, random_state=42)
    agg['cluster'] = kmeans.fit_predict(X)

    pca = PCA(n_components=2)
    X_pca = pca.fit_transform(X)
    agg['PC1'], agg['PC2'] = X_pca[:, 0], X_pca[:, 1]

    plt.figure(figsize=(8, 6))
    sns.scatterplot(data=agg, x='PC1', y='PC2', hue='cluster', palette='Set2', s=100)
    for i in range(agg.shape[0]):
        plt.text(agg['PC1'].iloc[i], agg['PC2'].iloc[i], agg.index[i], fontsize=9)
    plt.title("行业聚类可视化（PCA）")
    plt.show()

# 4. 时间序列模型预测订单周期
def order_delay_prediction():
    df_t = df[['ORDERED_DATE', 'ATA']].dropna()
    df_t['ORDERED_DATE'] = pd.to_datetime(df_t['ORDERED_DATE'])
    df_t['ATA'] = pd.to_datetime(df_t['ATA'])
    df_t['周期'] = (df_t['ATA'] - df_t['ORDERED_DATE']).dt.days
    df_t = df_t[['ORDERED_DATE', '周期']].sort_values('ORDERED_DATE')

    scaler = MinMaxScaler()
    data_scaled = scaler.fit_transform(df_t['周期'].values.reshape(-1, 1))

    X, y = [], []
    window_size = 7
    for i in range(len(data_scaled) - window_size):
        X.append(data_scaled[i:i+window_size])
        y.append(data_scaled[i+window_size])
    X, y = np.array(X), np.array(y)

    model = Sequential([
        LSTM(50, activation='relu', input_shape=(window_size, 1)),
        Dense(1)
    ])
    model.compile(optimizer='adam', loss='mse')
    model.fit(X, y, epochs=20, batch_size=8, verbose=0)

    pred = model.predict(X)
    predicted = scaler.inverse_transform(pred)

    plt.plot(df_t['ORDERED_DATE'][window_size:], df_t['周期'][window_size:], label='真实周期')
    plt.plot(df_t['ORDERED_DATE'][window_size:], predicted, label='预测周期')
    plt.legend()
    plt.title('订单延迟周期预测')
    plt.xlabel("日期")
    plt.ylabel("周期（天）")
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()

# 5. 可视化：热力图、折线图、聚类图
def visualizations():
    df['ASD-月'] = pd.to_datetime(df['ASD-月'], errors='coerce')
    monthly = df.groupby(df['ASD-月'].dt.to_period('M'))['ORDERED_QUANTITY'].sum()
    monthly.index = monthly.index.astype(str)

    monthly.plot(kind='line', marker='o', figsize=(10, 4))
    plt.title("每月商品订购数量")
    plt.xlabel("月份")
    plt.ylabel("订购数量")
    plt.grid(True)
    plt.tight_layout()
    plt.show()

    df_corr = df[['AMOUNT', 'WIP_QTY', 'ORDERED_QUANTITY']].dropna()
    plt.figure(figsize=(10, 6))
    sns.heatmap(df_corr.corr(), annot=True, cmap='coolwarm')
    plt.title("主要数值字段相关性热力图")
    plt.show()

# 主函数
if __name__ == '__main__':
    #component_wip_analysis()
    industry_clustering()
    #order_delay_prediction()
    visualizations()


from openai import OpenAI
from openai.types.chat import ChatCompletion
import os
import httpx
# 1. 配置自定义 httpx Client，禁用 SSL 验证（仅测试用）
custom_httpx_client = httpx.Client(
    verify=False,  # 忽略 SSL 证书验证（测试环境临时使用）
    timeout=httpx.Timeout(10.0)  # 可选：设置超时时间
)



class LLM:
    def __init__(
        self, 
        temperature: float = 0.4,
        max_tokens: int = 1024,
        top_p: float = 0.4,
    ):
        self._client = OpenAI(   
            api_key="sk-eztjrtjejivxgeczhadkxixmshpoqfipdkcxhqqisehqvinl",
            base_url="https://api.siliconflow.cn/v1/",
            http_client=custom_httpx_client  # 使用自定义的 httpx Client
        )
        self._temperature = temperature
        self._max_tokens = max_tokens
        self._top_p = top_p

    def chat(self, messages: list[dict]) -> ChatCompletion:
        try:
            response = self._client.chat.completions.create(
                model="Qwen/Qwen3-8B",
                messages=messages,
                temperature=self._temperature,
                max_tokens=self._max_tokens,
                top_p=self._top_p,
            )
            
            response = response.choices[0].message.content
            
        except Exception as e:
            response = None

        return response
    
    def construct_messages(self, sys_msg: str, user_msg: str) -> list[dict]:
        messages = [
            {"role": "system", "content": sys_msg},
            {"role": "user", "content": user_msg},
        ]

        return messages

llm=LLM()
l2_prompt = f"""Classify this NASA Earth science dataset into one of these topic categories: solid earth,gravity/gravitational field.\n

Dataset:
12-Hourly Interpolated Surface Velocity from Buoys.\n

You must output ONLY the topic category name from the list above, nothing else. Do not add explanations or additional text.

Topic Category:"""
            
messages_2 = llm.construct_messages("You are a NASA Earth science dataset classifier. Output only the exact category name.\n 请直接给出最终答案，不要展示推理过程\n", l2_prompt)
pred_l2_raw = llm.chat(messages_2)
print(pred_l2_raw)

#!/usr/bin/env python3
"""
LangChain Ollama API 测试
"""

import asyncio
from langchain_ollama import ChatOllama, OllamaEmbeddings
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser

class LangChainOllamaTest:
    def __init__(self, base_url="http://**********:11434", model="deepseek-r1:1.5b"):
        self.base_url = base_url
        self.model = model
        
        # 初始化ChatOllama
        self.llm = ChatOllama(
            base_url=base_url,
            model=model,
            temperature=0.7,
            num_predict=50  # 限制输出长度
        )
        
        # 初始化嵌入模型
        try:
            self.embeddings = OllamaEmbeddings(
                base_url=base_url,
                model="nomic-embed-text"  # 从你的模型列表中选择
            )
        except:
            self.embeddings = None
            print("⚠️ 嵌入模型初始化失败，跳过嵌入测试")

    def test_basic_chat(self):
        """测试基础聊天功能"""
        print("=== 测试基础聊天 ===")
        
        try:
            # 简单消息测试
            message = HumanMessage(content="你好，请简单介绍一下你自己")
            response = self.llm.invoke([message])
            
            print("✅ 基础聊天测试成功")
            print(f"用户: 你好，请简单介绍一下你自己")
            print(f"助手: {response.content}")
            return True
            
        except Exception as e:
            print(f"❌ 基础聊天测试失败: {e}")
            return False

    def test_system_prompt(self):
        """测试系统提示词"""
        print("\n=== 测试系统提示词 ===")
        
        try:
            messages = [
                SystemMessage(content="你是一个专业的Python编程助手"),
                HumanMessage(content="如何创建一个简单的列表？")
            ]
            
            response = self.llm.invoke(messages)
            
            print("✅ 系统提示词测试成功")
            print(f"系统: 你是一个专业的Python编程助手")
            print(f"用户: 如何创建一个简单的列表？")
            print(f"助手: {response.content}")
            return True
            
        except Exception as e:
            print(f"❌ 系统提示词测试失败: {e}")
            return False

    def test_prompt_template(self):
        """测试提示词模板"""
        print("\n=== 测试提示词模板 ===")
        
        try:
            # 创建提示词模板
            prompt = ChatPromptTemplate.from_messages([
                ("system", "你是一个{role}，请用{language}回答问题"),
                ("human", "{question}")
            ])
            
            # 创建链
            chain = prompt | self.llm | StrOutputParser()
            
            # 调用链
            response = chain.invoke({
                "role": "数学老师",
                "language": "中文",
                "question": "什么是勾股定理？"
            })
            
            print("✅ 提示词模板测试成功")
            print(f"角色: 数学老师")
            print(f"语言: 中文")
            print(f"问题: 什么是勾股定理？")
            print(f"回答: {response}")
            return True
            
        except Exception as e:
            print(f"❌ 提示词模板测试失败: {e}")
            return False

    def test_streaming(self):
        """测试流式输出"""
        print("\n=== 测试流式输出 ===")
        
        try:
            message = HumanMessage(content="请用一句话介绍人工智能")
            
            print("流式输出: ", end="", flush=True)
            for chunk in self.llm.stream([message]):
                print(chunk.content, end="", flush=True)
            print("\n✅ 流式输出测试成功")
            return True
            
        except Exception as e:
            print(f"❌ 流式输出测试失败: {e}")
            return False

    async def test_async_chat(self):
        """测试异步聊天"""
        print("\n=== 测试异步聊天 ===")
        
        try:
            message = HumanMessage(content="异步调用测试")
            response = await self.llm.ainvoke([message])
            
            print("✅ 异步聊天测试成功")
            print(f"异步回复: {response.content}")
            return True
            
        except Exception as e:
            print(f"❌ 异步聊天测试失败: {e}")
            return False

    def test_embeddings(self):
        """测试嵌入功能"""
        print("\n=== 测试嵌入功能 ===")
        
        if not self.embeddings:
            print("⚠️ 跳过嵌入测试（嵌入模型未初始化）")
            return False
        
        try:
            # 测试单个文本嵌入
            text = "这是一个测试文本"
            embedding = self.embeddings.embed_query(text)
            
            print("✅ 嵌入功能测试成功")
            print(f"文本: {text}")
            print(f"嵌入维度: {len(embedding)}")
            print(f"嵌入向量前5个值: {embedding[:5]}")
            
            # 测试批量嵌入
            texts = ["文本1", "文本2", "文本3"]
            embeddings = self.embeddings.embed_documents(texts)
            
            print(f"批量嵌入测试: {len(embeddings)} 个向量")
            return True
            
        except Exception as e:
            print(f"❌ 嵌入功能测试失败: {e}")
            return False

    def test_batch_processing(self):
        """测试批量处理"""
        print("\n=== 测试批量处理 ===")
        
        try:
            messages_batch = [
                [HumanMessage(content="1+1等于几？")],
                [HumanMessage(content="2+2等于几？")],
                [HumanMessage(content="3+3等于几？")]
            ]
            
            responses = self.llm.batch(messages_batch)
            
            print("✅ 批量处理测试成功")
            for i, response in enumerate(responses):
                print(f"问题{i+1}: {messages_batch[i][0].content}")
                print(f"回答{i+1}: {response.content}")
            return True
            
        except Exception as e:
            print(f"❌ 批量处理测试失败: {e}")
            return False

    async def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("LangChain Ollama API 完整测试")
        print("=" * 60)
        print(f"测试地址: {self.base_url}")
        print(f"测试模型: {self.model}")
        
        results = {}
        
        # 同步测试
        results["basic_chat"] = self.test_basic_chat()
        results["system_prompt"] = self.test_system_prompt()
        results["prompt_template"] = self.test_prompt_template()
        results["streaming"] = self.test_streaming()
        results["embeddings"] = self.test_embeddings()
        results["batch_processing"] = self.test_batch_processing()
        
        # 异步测试
        results["async_chat"] = await self.test_async_chat()
        
        # 输出测试结果
        print("\n" + "=" * 60)
        print("测试结果汇总")
        print("=" * 60)
        
        passed = 0
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name:<20} {status}")
            if result:
                passed += 1
        
        print(f"\n总体结果: {passed}/{total} 项测试通过")
        
        if passed == total:
            print("🎉 所有测试通过！LangChain Ollama集成正常工作")
        elif passed > 0:
            print("⚠️ 部分测试通过，请检查失败的测试项")
        else:
            print("❌ 所有测试失败，请检查Ollama服务和配置")

# 修复异步运行问题 - 在Jupyter中使用await而不是asyncio.run()

async def main():
    """主函数"""
    # 使用你的Ollama服务器地址和可用模型
    tester = LangChainOllamaTest(
        base_url="http://**********:11434",
        model="deepseek-r1:1.5b"  # 使用较快的模型
    )
    
    # 运行所有测试
    await tester.run_all_tests()

# 在Jupyter中直接使用await
await main()

import requests
import json

# 基础配置
OLLAMA_URL = "http://**********:11434"
MODEL_NAME = "deepseek-r1:70b"  # 使用更小的模型
TIMEOUT = 60

def test_ollama_api():
    # 测试1: 获取模型列表
    try:
        response = requests.get(f"{OLLAMA_URL}/api/tags", timeout=10)
        print(f"模型列表测试: {'✅' if response.ok else '❌'} {response.status_code}")
        if response.ok:
            models = response.json().get("models", [])
            print(f"可用模型: {[m.get('name') for m in models[:3]]}")
    except Exception as e:
        print(f"模型列表测试 ❌ 失败: {str(e)}")
    
    # 测试2: 原生接口测试 - 使用更简单的prompt
    try:
        data = {
            "model": MODEL_NAME,
            "prompt": "Hi",  # 更简单的prompt
            "stream": False,
            "options": {
                "num_predict": 5  # 只生成5个token
            }
        }
        print(f"\n发送请求到: {OLLAMA_URL}/api/generate")
        print(f"请求数据: {json.dumps(data, indent=2)}")
        
        response = requests.post(
            f"{OLLAMA_URL}/api/generate",
            json=data,
            timeout=TIMEOUT,
            headers={"Content-Type": "application/json"}
        )
        print(f"原生接口测试: {'✅' if response.ok else '❌'} {response.status_code}")
        if response.ok:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"原生接口测试 ❌ 失败: {str(e)}")

if __name__ == "__main__":
    test_ollama_api()

import requests

# 配置参数
api_key = "app-XQZO6aApSmTvpJNh6ALHdoxU"  # 替换为实际 API 密钥
base_url = "http://**********:8080/v1"  # 替换为实际端点
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

# 发送对话请求（blocking 模式）
data = {
    "query": "生成一个标题：Dify API 调用教程",
    "response_mode": "blocking",
    "user": "user-123"
}

response = requests.post(f"{base_url}/chat-messages", headers=headers, json=data)

if response.ok:
    result = response.json()
    print("生成结果：", result.get("answer"))
else:
    print("错误：", response.status_code, response.text)

import requests
import json

url = "http://**********:8080/v1/chat-messages"
api_key = "app-XQZO6aApSmTvpJNh6ALHdoxU"  # 替换为实际的API密钥

headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

payload = {
    "inputs": {},
    "query": "What are the specs of the iPhone 13 Pro Max?",
    "response_mode": "blocking",
    "conversation_id": "",
    "user": "abc-123",
     "files": [
      {
        "type": "image",
        "transfer_method": "remote_url",
        "url": "https://cloud.dify.ai/logo/logo-site.png"
      }
    ]
}

try:
    # 发送流式请求
    response = requests.post(url, headers=headers, json=payload, stream=True)
    response.raise_for_status()  # 检查请求是否成功
    
    # 处理流式响应
    for line in response.iter_lines():
        if line:
            # 移除"data: "前缀
            if line.startswith(b"data: "):
                data = line[6:].decode("utf-8")
                if data != "[DONE]":
                    try:
                        parsed = json.loads(data)
                        print(parsed.get("answer", ""))
                    except json.JSONDecodeError:
                        print(f"Failed to parse: {data}")
except requests.exceptions.RequestException as e:
    print(f"Request failed: {e}")

import requests
import json

url = "http://**********:8080/v1/completion-messages"
api_key = "app-Q97ObkBJxwrmFs1X8ZzZHqSK"  # 替换为实际的API密钥

headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

payload = {
    "inputs": {"query": "Meowth is the coolest, lots of play value. Great to to use kids imagination!"},
    "response_mode": "blocking",
    "user": "abc-123"
}

try:
    # 发送流式请求
    response = requests.post(url, headers=headers, json=payload)
    response.raise_for_status()  # 检查请求是否成功
    print(response)
    # 处理流式响应
    for line in response.iter_lines():
        if line:
            # 移除"data: "前缀
            if line.startswith(b"data: "):
                data = line[6:].decode("utf-8")
                if data != "[DONE]":
                    try:
                        parsed = json.loads(data)
                        print(f"Chunk: {parsed.get('answer', '')}")
                    except json.JSONDecodeError:
                        print(f"Failed to parse: {data}")
except requests.exceptions.RequestException as e:
    print(f"Request failed: {e}")

import requests

url = "http://**********:8080/v1/completion-messages"
api_key = "app-Q97ObkBJxwrmFs1X8ZzZHqSK"  # 替换为实际的API密钥

headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

payload = {
    "inputs": {"query": "Hello, world!"},
    "response_mode": "blocking",  # 修改为阻塞模式
    "user": "abc-123"
}

try:
    # 发送阻塞请求
    response = requests.post(url, headers=headers, json=payload)
    response.raise_for_status()  # 检查请求是否成功
    
    # 直接解析完整响应
    result = response.json()
    print(f"回复 ID: {result.get('id')}")
    print(f"回答内容: {result.get('answer')}")
    print(f"创建时间: {result.get('created_at')}")
    
except requests.exceptions.RequestException as e:
    print(f"请求失败: {e}")
except ValueError as e:  # JSON 解析错误
    print(f"解析响应失败: {e}")

import requests

def query_ai_assistant(query):
    """
    查询AI助手并返回处理后的回答，分离思考过程和最终回答
    
    参数:
        query (str): 用户查询内容
        
    返回:
        dict: 包含以下键的字典:
            - 'success': 请求是否成功(bool)
            - 'answer': 最终回答内容(str)
            - 'thinking': 思考过程内容(str)
            - 'error': 错误信息(如果失败)
            - 'metadata': 包含id和创建时间的元数据(dict)
    """
    url = "http://**********:8080/v1/completion-messages"
    api_key = "app-Q97ObkBJxwrmFs1X8ZzZHqSK"  # 替换为实际的API密钥

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    payload = {
        "inputs": {"query": query},
        "response_mode": "blocking",
        "user": "abc-123"
    }

    result = {
        'answer': '',
        'thinking': '',
        'metadata': {}
    }


    response = requests.post(url, headers=headers, json=payload)
    response.raise_for_status()
        
    data = response.json()
    full_answer = data.get('answer', '')
    print(full_answer)
        
        # 分离思考过程和最终回答
    thinking_start = full_answer.find('<think>')
    thinking_end = full_answer.find('</think>')
        
    if thinking_start != -1 and thinking_end != -1:
        result['thinking'] = full_answer[thinking_start+7:thinking_end].strip()
        result['answer'] = full_answer[thinking_end+8:].strip()
    else:
        result['answer'] = full_answer
        
        # 添加元数据
    result['metadata'] = {
            'id': data.get('id'),
            'created_at': data.get('created_at')
        }


    return result


# 使用示例
if __name__ == "__main__":
    response = query_ai_assistant("Hello, world!")
    print("思考过程:", response['thinking'])
    print("最终回答:", response['answer'])
    print("元数据:", response['metadata'])
