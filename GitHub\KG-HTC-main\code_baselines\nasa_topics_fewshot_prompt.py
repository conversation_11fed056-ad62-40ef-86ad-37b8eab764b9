import pandas as pd
import numpy as np
from pathlib import Path
from tqdm import tqdm
import sys
import json
import time
import random

# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

from src.llm import LLM

def create_few_shot_examples(df, shots_per_class=3, level='Cat2', random_state=42):
    """
    为每个类别创建少样本示例
    
    Args:
        df: 数据集
        shots_per_class: 每个类别的示例数
        level: 分类级别 (Cat1, Cat2, Cat3)
        random_state: 随机种子
    """
    random.seed(random_state)
    np.random.seed(random_state)
    
    examples = {}
    unique_classes = df[level].unique()
    
    for class_name in unique_classes:
        class_samples = df[df[level] == class_name]
        
        # 随机选择示例
        n_samples = min(shots_per_class, len(class_samples))
        if n_samples > 0:
            sampled = class_samples.sample(n=n_samples, random_state=random_state)
            examples[class_name] = sampled[['Text', level]].to_dict('records')
    
    return examples

def build_few_shot_prompt(query_text, examples_dict, candidates, level='Cat2'):
    """
    构建少样本学习提示
    
    Args:
        query_text: 查询文本
        examples_dict: 示例字典
        candidates: 候选类别
        level: 分类级别
    """
    
    # 构建示例部分
    examples_text = []
    for class_name, examples in examples_dict.items():
        if class_name in candidates:  # 只包含候选类别的示例
            for example in examples:
                text_snippet = example['Text'][:300] + "..." if len(example['Text']) > 300 else example['Text']
                examples_text.append(f"Text: {text_snippet}\nCategory: {example[level]}")
    
    # 随机打乱示例顺序
    random.shuffle(examples_text)
    
    # 限制示例数量避免超出token限制
    max_examples = 10
    if len(examples_text) > max_examples:
        examples_text = examples_text[:max_examples]
    
    examples_str = "\n\n".join(examples_text)
    
    # 构建完整提示
    prompt = f"""You are an expert in classifying NASA Earth science datasets. Here are some examples:

{examples_str}

Now classify the following dataset into one of these categories: {', '.join(candidates)}

Text: {query_text}
Category:"""
    
    return prompt

def run_fewshot_prompt_experiment():
    """
    运行少样本提示学习实验
    """
    
    print("=== NASA Topics少样本提示学习实验 ===")
    
    # 读取数据
    train_path = "dataset/nasa_topics/nasa_topics_train.csv"
    test_path = "dataset/nasa_topics/nasa_topics_val.csv"
    
    try:
        train_df = pd.read_csv(train_path)
        test_df = pd.read_csv(test_path)
        print(f"训练集: {len(train_df)} 条记录")
        print(f"测试集: {len(test_df)} 条记录")
    except Exception as e:
        print(f"读取数据失败: {e}")
        return
    
    # 数据预处理
    def preprocess_data(df):
        df = df.dropna(subset=['Text', 'Cat1', 'Cat2', 'Cat3'])
        df = df[df['Cat1'] != "unknown"]
        df = df[df['Cat2'] != "unknown"]
        df = df[df['Cat3'] != "unknown"]
        return df
    
    train_df = preprocess_data(train_df)
    test_df = preprocess_data(test_df)
    
    # 限制测试数据量
    if len(test_df) > 100:
        test_df = test_df.sample(n=100, random_state=42)
    
    print(f"实验数据 - 训练集: {len(train_df)}, 测试集: {len(test_df)}")
    
    # 初始化LLM
    try:
        llm = LLM()
        print("✓ LLM初始化成功")
    except Exception as e:
        print(f"✗ LLM初始化失败: {e}")
        return
    
    # 不同的少样本配置
    few_shot_configs = [
        {"shots_per_class": 1, "name": "1-shot"},
        {"shots_per_class": 2, "name": "2-shot"},
        {"shots_per_class": 3, "name": "3-shot"},
        {"shots_per_class": 5, "name": "5-shot"}
    ]
    
    all_results = {}
    
    for config in few_shot_configs:
        print(f"\n=== {config['name']} 提示学习实验 ===")
        
        try:
            # 为每个级别创建示例
            examples_l1 = create_few_shot_examples(train_df, config["shots_per_class"], 'Cat1')
            examples_l2 = create_few_shot_examples(train_df, config["shots_per_class"], 'Cat2')
            examples_l3 = create_few_shot_examples(train_df, config["shots_per_class"], 'Cat3')
            
            print(f"L1示例类别数: {len(examples_l1)}")
            print(f"L2示例类别数: {len(examples_l2)}")
            print(f"L3示例类别数: {len(examples_l3)}")
            
            # 获取所有候选类别
            all_l1 = train_df['Cat1'].unique().tolist()
            all_l2 = train_df['Cat2'].unique().tolist()
            all_l3 = train_df['Cat3'].unique().tolist()
            
            # 开始分类实验
            results = []
            success_count = 0
            error_count = 0
            total_time = 0
            
            for idx, row in tqdm(test_df.iterrows(), total=len(test_df), desc=f"{config['name']}分类"):
                try:
                    start_time = time.time()
                    
                    # 准备查询文本
                    query_text = row['Text'][:1500]  # 限制长度
                    
                    # L1级别预测
                    l1_prompt = build_few_shot_prompt(
                        query_text, examples_l1, all_l1, 'Cat1'
                    )
                    pred_l1 = llm.generate(l1_prompt, max_tokens=50, temperature=0.3)
                    pred_l1 = pred_l1.strip().lower()
                    
                    # L2级别预测
                    l2_prompt = build_few_shot_prompt(
                        query_text, examples_l2, all_l2[:15], 'Cat2'  # 限制候选数量
                    )
                    pred_l2 = llm.generate(l2_prompt, max_tokens=50, temperature=0.3)
                    pred_l2 = pred_l2.strip().lower()
                    
                    # L3级别预测
                    l3_prompt = build_few_shot_prompt(
                        query_text, examples_l3, all_l3[:20], 'Cat3'  # 限制候选数量
                    )
                    pred_l3 = llm.generate(l3_prompt, max_tokens=50, temperature=0.3)
                    pred_l3 = pred_l3.strip().lower()
                    
                    inference_time = time.time() - start_time
                    total_time += inference_time
                    
                    # 保存结果
                    result = {
                        'Title': row['Title'],
                        'Text': row['Text'],
                        'Cat1': row['Cat1'],
                        'Cat2': row['Cat2'],
                        'Cat3': row['Cat3'],
                        f'fewshot_{config["shots_per_class"]}_pred_l1': pred_l1,
                        f'fewshot_{config["shots_per_class"]}_pred_l2': pred_l2,
                        f'fewshot_{config["shots_per_class"]}_pred_l3': pred_l3,
                        'method': f'fewshot_prompt_{config["shots_per_class"]}',
                        'inference_time': inference_time,
                        'shots_per_class': config["shots_per_class"]
                    }
                    
                    results.append(result)
                    success_count += 1
                    
                except Exception as e:
                    error_count += 1
                    print(f"\n处理第 {idx+1} 条记录时出错: {e}")
                    
                    result = {
                        'Title': row['Title'],
                        'Text': row['Text'],
                        'Cat1': row['Cat1'],
                        'Cat2': row['Cat2'],
                        'Cat3': row['Cat3'],
                        f'fewshot_{config["shots_per_class"]}_pred_l1': "error",
                        f'fewshot_{config["shots_per_class"]}_pred_l2': "error",
                        f'fewshot_{config["shots_per_class"]}_pred_l3': "error",
                        'method': f'fewshot_prompt_{config["shots_per_class"]}',
                        'error_message': str(e),
                        'shots_per_class': config["shots_per_class"]
                    }
                    results.append(result)
                
                # API限制延迟
                time.sleep(0.3)
            
            # 计算性能指标
            if success_count > 0:
                valid_results = [r for r in results if r[f'fewshot_{config["shots_per_class"]}_pred_l1'] != 'error']
                
                if valid_results:
                    # 计算准确率
                    correct_l1 = sum(1 for r in valid_results 
                                   if r[f'fewshot_{config["shots_per_class"]}_pred_l1'].lower() == r['Cat1'].lower())
                    correct_l2 = sum(1 for r in valid_results 
                                   if r[f'fewshot_{config["shots_per_class"]}_pred_l2'].lower() == r['Cat2'].lower())
                    correct_l3 = sum(1 for r in valid_results 
                                   if r[f'fewshot_{config["shots_per_class"]}_pred_l3'].lower() == r['Cat3'].lower())
                    
                    acc_l1 = correct_l1 / len(valid_results)
                    acc_l2 = correct_l2 / len(valid_results)
                    acc_l3 = correct_l3 / len(valid_results)
                    
                    # 层次化准确率
                    hierarchical_correct = sum(1 for r in valid_results 
                                             if (r[f'fewshot_{config["shots_per_class"]}_pred_l1'].lower() == r['Cat1'].lower() and
                                                 r[f'fewshot_{config["shots_per_class"]}_pred_l2'].lower() == r['Cat2'].lower() and
                                                 r[f'fewshot_{config["shots_per_class"]}_pred_l3'].lower() == r['Cat3'].lower()))
                    hierarchical_acc = hierarchical_correct / len(valid_results)
                    
                    avg_time = total_time / len(valid_results)
                    
                    print(f"\n{config['name']} 结果:")
                    print(f"  成功率: {success_count/len(results)*100:.2f}%")
                    print(f"  L1准确率: {acc_l1:.4f} ({correct_l1}/{len(valid_results)})")
                    print(f"  L2准确率: {acc_l2:.4f} ({correct_l2}/{len(valid_results)})")
                    print(f"  L3准确率: {acc_l3:.4f} ({correct_l3}/{len(valid_results)})")
                    print(f"  层次化准确率: {hierarchical_acc:.4f} ({hierarchical_correct}/{len(valid_results)})")
                    print(f"  平均推理时间: {avg_time:.2f}秒")
                    
                    # 保存配置结果
                    all_results[config['name']] = {
                        'accuracy': {
                            'L1': acc_l1,
                            'L2': acc_l2,
                            'L3': acc_l3,
                            'Hierarchical': hierarchical_acc
                        },
                        'statistics': {
                            'total_samples': len(results),
                            'valid_samples': len(valid_results),
                            'success_rate': success_count / len(results),
                            'average_inference_time': avg_time
                        },
                        'shots_per_class': config["shots_per_class"]
                    }
            
            # 保存单个配置的详细结果
            config_output_path = f"dataset/nasa_topics/fewshot_prompt_{config['shots_per_class']}_results.json"
            with open(config_output_path, "w", encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            print(f"✗ {config['name']} 实验失败: {e}")
    
    # 保存汇总结果
    if all_results:
        summary_path = "dataset/nasa_topics/fewshot_prompt_summary.json"
        with open(summary_path, "w", encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n=== 少样本提示学习性能对比 ===")
        for config_name, metrics in all_results.items():
            print(f"\n{config_name}:")
            print(f"  L1: {metrics['accuracy']['L1']:.4f}")
            print(f"  L2: {metrics['accuracy']['L2']:.4f}")
            print(f"  L3: {metrics['accuracy']['L3']:.4f}")
            print(f"  层次化: {metrics['accuracy']['Hierarchical']:.4f}")
            print(f"  推理时间: {metrics['statistics']['average_inference_time']:.2f}秒")
        
        print(f"\n✓ 少样本提示学习结果已保存: {summary_path}")

if __name__ == "__main__":
    run_fewshot_prompt_experiment()
