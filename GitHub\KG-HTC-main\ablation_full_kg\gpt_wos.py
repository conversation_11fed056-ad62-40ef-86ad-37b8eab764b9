import pandas as pd
from pathlib import Path
from tqdm import tqdm
import sys
import json
import random

# 将项目根目录添加到Python路径中，以便导入自定义模块
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

# 导入自定义模块
from src.llm import LLM  # 大语言模型接口
from src.graph_db import GraphDB  # 图数据库操作类
from src.pipeline import Pipeline  # 主要处理流水线


# 配置参数字典，包含数据路径、输出路径、模板等关键配置
config = {
    "data_name": "wos",  # 数据集名称（Web of Science）
    "data_path": f"dataset/wos/Meta-data/Data.xlsx",  # 输入数据文件路径
    "output_path": "dataset/wos/ablation_full_kg.json",  # 结果输出路径
    "vectdb_path": "database/wos",  # 向量数据库路径
    "template": {
        # 系统提示词和用户提示词模板文件路径
        "sys": "prompts/system/wos/llm_graph.txt",
        "user": "prompts/user/wos/llm_graph.txt"
    },
    "query_params": {
        "l2_top_k": 141,  # L2级别查询返回的top-k结果数量
    }
}

# 读取Excel数据文件并进行预处理
df = pd.read_excel(config["data_path"])
# 随机采样5000条数据，设置随机种子确保结果可复现
df = df.sample(n=5000, random_state=42)
# 将DataFrame转换为字典列表格式，便于后续处理
ds = df.to_dict(orient="records")

# 初始化核心组件
llm = LLM()  # 初始化大语言模型
graph_db = GraphDB()  # 初始化图数据库连接
pipeline = Pipeline(llm, config)  # 初始化处理流水线

# 存储推理结果的列表
inference_list = []

# 遍历数据集中的每条记录进行处理
for idx in tqdm(range(len(ds))):
    # 复制当前数据记录，避免修改原始数据
    data = ds[idx].copy()

    # 提取摘要文本作为向量数据库查询的输入
    query_txt_vecdb = data["Abstract"]
    
    # 使用流水线查询相关节点
    # 这一步会在知识图谱中找到与摘要文本最相关的节点
    retrieved_nodes = pipeline.query_related_nodes(query_txt_vecdb)

    # 构建子图结构
    sub_graph = []
    # 遍历检索到的L2级别节点
    for l2 in retrieved_nodes["l2"]:
        # 查询每个L2节点对应的L1父节点
        l1 = graph_db.query_l1_from_l2(l2)
        # 构建"L1 -> L2"的层次关系表示
        sub_graph.append(f"{l1} -> {l2}")

    # 获取所有可能的L1级别分类（领域分类）
    potential_level1 = df["Domain"].unique()
    
    # 使用LLM预测L1级别分类
    # 基于摘要文本、候选分类和子图信息进行预测
    pred_level1 = pipeline.predict_level(
        query_txt_vecdb,  # 查询文本
        potential_level1,  # 候选L1分类
        sub_graph  # 相关子图信息
    ).lower().replace(' ', '').replace('*', '').replace('\'', '').replace('\"', '')
    # 对预测结果进行标准化处理：转小写、去除空格和特殊字符

    # 获取所有可能的L2级别分类（细分领域）
    potential_level2 = df["area"].unique()
    
    # 使用LLM预测L2级别分类
    pred_level2 = pipeline.predict_level(
        query_txt_vecdb,  # 查询文本
        potential_level2,  # 候选L2分类
        sub_graph  # 相关子图信息
    ).lower().replace(' ', '').replace('*', '').replace('\'', '').replace('\"', '')
    # 同样进行标准化处理
    
    # 将预测结果添加到数据记录中
    data["gpt3_graph_l1"], data["gpt3_graph_l2"] = pred_level1, pred_level2
    
    # 将处理后的数据添加到结果列表
    inference_list.append(data)

    # 实时保存结果到JSON文件，防止程序中断导致数据丢失
    with open(config["output_path"], "w") as f:
        json.dump(inference_list, f, indent=4)

    # break  # 调试时可取消注释，只处理一条数据
