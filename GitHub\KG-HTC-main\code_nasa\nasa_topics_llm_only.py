import pandas as pd
from pathlib import Path
from tqdm import tqdm
import sys
import json
import time
from sklearn.metrics import accuracy_score, f1_score

# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

from src.llm import LLM
from difflib import get_close_matches

def find_best_match(label, candidates, cutoff=0.9):
    # 先尝试精确匹配
    if label in candidates:
        return label
    # 模糊匹配（返回最相似的1个结果，相似度 > cutoff）
    matches = get_close_matches(label, candidates, n=1, cutoff=cutoff)
    return matches[0] if matches else None

def clean_label(label, candidate_tags):
    label = label.strip()
    # 如果是 "-na-"，直接保留
    if label == "-na-":
        return label
    # 尝试匹配候选标签
    matched = find_best_match(label, candidate_tags)
    if matched:
        return matched
    # 无法匹配时，去除开头的 "-" 再试
    cleaned = label.lstrip("-")
    matched = find_best_match(cleaned, candidate_tags)
    if matched:
        return matched
    # 仍然无法匹配，返回清理后的（或标记为未知）
    return cleaned  # 或 return "unknown"


def evaluate_results(results):
    """
    评估LLM-Only实验结果
    """
    print("\n=== 评估结果 ===")
    
    # 过滤有效结果
    valid_results = [r for r in results if r.get("llm_only_l1") != "error" and 
                     r.get("llm_only_l2") != "error" and 
                     r.get("llm_only_l3") != "error"]
    
    if not valid_results:
        print("没有有效的预测结果")
        return
    
    print(f"有效预测记录数: {len(valid_results)}/{len(results)}")
    
    # 准备真实标签和预测标签
    true_l1 = [r['Cat1'].lower() for r in valid_results]
    true_l2 = [r['Cat2'].lower() for r in valid_results]
    true_l3 = [r['Cat3'].lower() for r in valid_results]
    
    pred_l1 = [r['llm_only_l1'] for r in valid_results]
    pred_l2 = [r['llm_only_l2'] for r in valid_results]
    pred_l3 = [r['llm_only_l3'] for r in valid_results]
    
    # 计算各层级准确率
    acc_l1 = accuracy_score(true_l1, pred_l1)
    acc_l2 = accuracy_score(true_l2, pred_l2)
    acc_l3 = accuracy_score(true_l3, pred_l3)
    
    # 计算各层级F1分数
    f1_l1_macro = f1_score(true_l1, pred_l1, average='macro', zero_division=0)
    f1_l2_macro = f1_score(true_l2, pred_l2, average='macro', zero_division=0)
    f1_l3_macro = f1_score(true_l3, pred_l3, average='macro', zero_division=0)
    
    f1_l1_micro = f1_score(true_l1, pred_l1, average='micro', zero_division=0)
    f1_l2_micro = f1_score(true_l2, pred_l2, average='micro', zero_division=0)
    f1_l3_micro = f1_score(true_l3, pred_l3, average='micro', zero_division=0)
    
    # 计算层次化准确率（所有层级都正确）
    hierarchical_correct = sum(1 for i in range(len(valid_results)) 
                              if true_l1[i] == pred_l1[i] and 
                                 true_l2[i] == pred_l2[i] and 
                                 true_l3[i] == pred_l3[i])
    hierarchical_acc = hierarchical_correct / len(valid_results)
    
    # 计算部分层次化准确率
    l1_l2_correct = sum(1 for i in range(len(valid_results)) 
                       if true_l1[i] == pred_l1[i] and true_l2[i] == pred_l2[i])
    l1_l2_acc = l1_l2_correct / len(valid_results)
    
    # 打印结果
    print("\n1. 准确率 (Accuracy):")
    print(f"   L1: {acc_l1:.4f} ({acc_l1*100:.2f}%)")
    print(f"   L2: {acc_l2:.4f} ({acc_l2*100:.2f}%)")
    print(f"   L3: {acc_l3:.4f} ({acc_l3*100:.2f}%)")
    print(f"   L1_L2: {l1_l2_acc:.4f} ({l1_l2_acc*100:.2f}%)")
    print(f"   Hierarchical: {hierarchical_acc:.4f} ({hierarchical_acc*100:.2f}%)")
    
    print("\n2. F1分数 - Macro平均:")
    print(f"   L1: {f1_l1_macro:.4f}")
    print(f"   L2: {f1_l2_macro:.4f}")
    print(f"   L3: {f1_l3_macro:.4f}")
    
    print("\n3. F1分数 - Micro平均:")
    print(f"   L1: {f1_l1_micro:.4f}")
    print(f"   L2: {f1_l2_micro:.4f}")
    print(f"   L3: {f1_l3_micro:.4f}")
    
    # 保存评估报告
    metrics = {
        'accuracy': {
            'L1': acc_l1,
            'L2': acc_l2,
            'L3': acc_l3,
            'L1_L2': l1_l2_acc,
            'Hierarchical': hierarchical_acc
        },
        'f1_macro': {
            'L1': f1_l1_macro,
            'L2': f1_l2_macro,
            'L3': f1_l3_macro
        },
        'f1_micro': {
            'L1': f1_l1_micro,
            'L2': f1_l2_micro,
            'L3': f1_l3_micro
        },
        'statistics': {
            'total_samples': len(results),
            'valid_samples': len(valid_results),
            'success_rate': len(valid_results) / len(results)
        }
    }
    
    # 保存详细报告
    report_path = "D:/Project/GitHub/KG-HTC-main/code_nasa/dataset/nasa/llm_only_evaluation_report.txt"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("NASA Topics数据集LLM-Only性能评估报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("1. 基本信息:\n")
        f.write(f"   总记录数: {len(results)}\n")
        f.write(f"   有效记录数: {len(valid_results)}\n")
        f.write(f"   成功率: {len(valid_results)/len(results)*100:.2f}%\n\n")
        
        f.write("2. 准确率:\n")
        for level, acc in metrics['accuracy'].items():
            f.write(f"   {level}: {acc:.4f} ({acc*100:.2f}%)\n")
        
        f.write("\n3. F1分数 (Macro):\n")
        for level, f1 in metrics['f1_macro'].items():
            f.write(f"   {level}: {f1:.4f}\n")
        
        f.write("\n4. F1分数 (Micro):\n")
        for level, f1 in metrics['f1_micro'].items():
            f.write(f"   {level}: {f1:.4f}\n")
    
    print(f"\n详细报告已保存: {report_path}")
    
    return metrics

def run_llm_only_experiment():
    """
    运行LLM-Only实验：仅使用大语言模型，无知识图谱增强
    """
    
    # 配置参数
    config = {
        "data_name": "nasa_topics_llm_only",
        "data_path": "D:/Project/GitHub/KG-HTC-main/code_nasa/dataset/nasa/nasa_val.csv",
        "output_path": "D:/Project/GitHub/KG-HTC-main/code_nasa/dataset/nasa/llm_only_results.json",
        "template": {
            "sys": "prompts/system/nasa_topics/llm_only.txt",
            "user": "prompts/user/nasa_topics/llm_only.txt"
        }
    }
    
    print("=== NASA Topics LLM-Only实验 ===")
    print("实验设计: 仅使用LLM进行分类，不使用知识图谱或向量检索")

    # 确保输出目录存在
    output_dir = Path(config["output_path"]).parent
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 读取数据
    try:
        df = pd.read_csv(config["data_path"])
        print(f"成功读取数据: {len(df)} 条记录")
    except Exception as e:
        print(f"读取数据失败: {e}")
        return
    
    # 数据预处理
    df = df.dropna(subset=['Title', 'Text', 'Cat1', 'Cat2', 'Cat3'])
    df = df[df['Cat1'] != "unknown"]
    df = df[df['Cat2'] != "unknown"]
    df = df[df['Cat3'] != "unknown"]
    
    # 限制实验数据量与主实验保持一致
    max_samples = 500
    if len(df) > max_samples:
        df = df.sample(n=max_samples, random_state=42)
    
    ds = df.to_dict(orient="records")
    print(f"实验数据量: {len(ds)} 条记录")
    
    # 初始化LLM
    try:
        llm = LLM()
        print("✓ LLM初始化成功")
    except Exception as e:
        print(f"✗ LLM初始化失败: {e}")
        return
    
    # 准备所有可能的分类选项
    all_l1 = df['Cat1'].unique().tolist()
    all_l2 = df['Cat2'].unique().tolist()
    all_l3 = df['Cat3'].unique().tolist()
    
    print(f"分类选项: L1={len(all_l1)}, L2={len(all_l2)}, L3={len(all_l3)}")
    
    # 开始实验
    print("\n=== 开始LLM-Only分类 ===")
    results = []
    success_count = 0
    error_count = 0
    
    for idx in tqdm(range(len(ds)), desc="LLM-Only分类"):
        data = ds[idx].copy()
        
        try:
            # 构建查询文本
            title = data.get('Title', '').strip()
            text = data.get('Text', '').strip()
            #query_text = f"Title: {title}\n"
            query_text = f"Title: {title}\nDescription: {text[:500]}"
#             # L1级别预测
#             l1_prompt = f"""Classify this NASA Earth science dataset into one of these categories: {', '.join(all_l1)}.

# Dataset:
# {query_text}

# You must output ONLY the category name from the list above, nothing else. Do not add explanations or additional text.

# Category:"""
            
#             messages = llm.construct_messages("You are a NASA Earth science dataset classifier. Output only the exact category name.", l1_prompt)
#             pred_l1_raw = llm.chat(messages)
#             pred_l1 = extract_label_from_response(pred_l1_raw, all_l1)
#             print(f"L1预测: {pred_l1}")
            
            # L2级别预测
            l2_prompt = f"""Classify this NASA Earth science dataset into one of these topic categories: {', '.join(all_l2)}.

            Dataset:
            {query_text}

            You must output ONLY the topic category name from the list above, nothing else. Do not add explanations or additional text.

            Topic Category:"""
            
            messages_2 = llm.construct_messages("You are a NASA Earth science dataset classifier. Output only the exact category name.\nPlease provide the final answer directly without showing the reasoning process.",
                                                 l2_prompt)
            pred_l2_raw = llm.chat(messages_2)
            pred_l2 = pred_l2_raw.strip().lower()
            
            
            # L3级别预测
            l3_prompt = f"""Classify this NASA Earth science dataset into one of these specific terms: {', '.join(all_l3)}.

            Dataset:
            {query_text}

            You must output ONLY the specific term from the list above, nothing else. Do not add explanations or additional text.

            Specific Term:"""
            
            messages_3 = llm.construct_messages("You are a NASA Earth science dataset classifier. Output only the exact term.\n Please provide the final answer directly without showing the reasoning process.",
                                                 l3_prompt)
            pred_l3_raw = llm.chat(messages_3)
            pred_l3 = pred_l3_raw.strip().lower()
            pred_l3=clean_label(pred_l3,all_l3)
            
            # 保存结果
            data["llm_only_l1"] = "earth science"
            data["llm_only_l2"] = pred_l2
            data["llm_only_l3"] = pred_l3
            data["method"] = "llm_only"
            
            results.append(data)
            
            # 检查是否有有效预测
            if  pred_l2 != "error" and pred_l3 != "error":
                success_count += 1
            else:
                error_count += 1
            
        except Exception as e:
            error_count += 1
            print(f"\n处理第 {idx+1} 条记录时出错: {e}")
            
            data["llm_only_l1"] = "error"
            data["llm_only_l2"] = "error"
            data["llm_only_l3"] = "error"
            data["error_message"] = str(e)
            data["method"] = "llm_only"
            results.append(data)
    
    # 保存结果
    try:
        with open(config["output_path"], "w", encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"✓ LLM-Only结果已保存: {config['output_path']}")
    except Exception as e:
        print(f"✗ 保存结果失败: {e}")
    
    # 统计信息
    print(f"\n=== LLM-Only实验统计 ===")
    print(f"成功: {success_count}, 失败: {error_count}")
    print(f"成功率: {success_count/len(results)*100:.2f}%")
    
    # 添加评估
    if results:
        evaluate_results(results)

if __name__ == "__main__":
    run_llm_only_experiment()



