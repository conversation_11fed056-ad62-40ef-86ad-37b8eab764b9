#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版向量数据库快速测试脚本

用于快速验证增强版 EnhancedQdrantVectorDB 类的基本功能，
包括初始化、数据添加、查询等核心操作。

作者：AI Assistant
日期：2025-01-28
版本：2.0
"""

import os
import sys
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    try:
        from src.enhanced_qdrant_vector_db import EnhancedQdrantVectorDB
        
        # 初始化向量数据库
        vector_db = EnhancedQdrantVectorDB(
            collection_name="quick_test",
            use_memory=True,
            auto_persist=False,  # 测试时关闭持久化
            embedding_config={
                "batch_size": 5,
                "max_retries": 2
            }
        )
        
        print("✓ 向量数据库初始化成功")
        
        # 添加测试数据
        test_texts = [
            "人工智能是计算机科学的一个分支",
            "机器学习是人工智能的子领域",
            "深度学习使用神经网络进行学习"
        ]
        
        test_metadatas = [
            {"category": "ai", "level": "Category1"},
            {"category": "ml", "level": "Category2"},
            {"category": "dl", "level": "Category3"}
        ]
        
        result = vector_db.batch_add_texts(test_texts, test_metadatas)
        
        if result.get("success"):
            print(f"✓ 成功添加 {result['added_count']} 条数据")
        else:
            print(f"✗ 添加数据失败: {result.get('error')}")
            return False
        
        # 测试查询
        query_result = vector_db.query("人工智能", n_results=3)
        
        if len(query_result) > 0:
            print(f"✓ 查询成功，返回 {len(query_result)} 个结果")
            print(f"  第一个结果相似度: {query_result.scores[0]:.3f}")
        else:
            print("✗ 查询失败，没有返回结果")
            return False
        
        # 测试层级查询
        l1_result = vector_db.query_hierarchical("人工智能", "Category1", n_results=2)
        print(f"✓ 层级查询成功，Category1 返回 {len(l1_result)} 个结果")
        
        # 测试统计信息
        stats = vector_db.get_statistics()
        print(f"✓ 统计信息获取成功，总文档数: {stats['basic_info']['total_documents']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_csv_loading():
    """测试CSV数据加载"""
    print("\n🧪 测试CSV数据加载...")
    
    try:
        import pandas as pd
        from src.enhanced_qdrant_vector_db import EnhancedQdrantVectorDB
        
        # 创建测试CSV数据
        test_data = {
            'Title': ['标题1', '标题2', '标题3'],
            'Text': [
                '这是第一个测试文本，用于验证CSV加载功能。',
                '这是第二个测试文本，包含不同的内容。',
                '这是第三个测试文本，用于测试查询功能。'
            ],
            'Cat1': ['category1', 'category2', 'category1'],
            'Cat2': ['subcategory1', 'subcategory2', 'subcategory3'],
            'Cat3': ['item1', 'item2', 'item3']
        }
        
        # 保存为CSV文件
        df = pd.DataFrame(test_data)
        csv_path = "quick_test_data.csv"
        df.to_csv(csv_path, index=False, encoding='utf-8')
        
        # 初始化向量数据库
        vector_db = EnhancedQdrantVectorDB(
            collection_name="csv_test",
            use_memory=True,
            auto_persist=False
        )
        
        # 加载CSV数据
        result = vector_db.load_csv_data(
            csv_path=csv_path,
            text_column="Text",
            hierarchical_columns={
                "Category1": "Cat1",
                "Category2": "Cat2",
                "Category3": "Cat3"
            }
        )
        
        if result.get("success"):
            print(f"✓ CSV加载成功，处理了 {result['processed_rows']} 行数据")
            print(f"  文本列: {result['text_column']}")
        else:
            print(f"✗ CSV加载失败: {result.get('error')}")
            return False
        
        # 测试查询加载的数据
        query_result = vector_db.query("测试文本", n_results=3)
        print(f"✓ 查询加载的数据成功，返回 {len(query_result)} 个结果")
        
        # 清理临时文件
        if os.path.exists(csv_path):
            os.remove(csv_path)
        
        return True
        
    except Exception as e:
        print(f"✗ CSV加载测试失败: {e}")
        traceback.print_exc()
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🧪 测试向后兼容性...")
    
    try:
        from src.enhanced_qdrant_vector_db import EnhancedQdrantVectorDB
        
        # 初始化向量数据库
        vector_db = EnhancedQdrantVectorDB(
            collection_name="compat_test",
            use_memory=True,
            auto_persist=False
        )
        
        # 使用旧的batch_add接口
        titles = ["标题1", "标题2"]
        texts = ["文本内容1", "文本内容2"]
        metadatas = [
            {"level": "Category1", "topic": "test1"},
            {"level": "Category2", "topic": "test2"}
        ]
        
        result = vector_db.batch_add(titles, texts, metadatas)
        
        if result.get("success"):
            print(f"✓ 旧接口batch_add成功，添加了 {result['added_count']} 条数据")
        else:
            print(f"✗ 旧接口batch_add失败: {result.get('error')}")
            return False
        
        # 使用旧的查询接口
        l1_result = vector_db.query_l1("文本")
        l2_result = vector_db.query_l2("内容")
        all_result = vector_db.query_all_levels("测试")
        
        print(f"✓ 旧查询接口测试成功:")
        print(f"  L1查询: {len(l1_result['documents'][0])} 个结果")
        print(f"  L2查询: {len(l2_result['documents'][0])} 个结果")
        print(f"  全部查询: {len(all_result['documents'][0])} 个结果")
        
        return True
        
    except Exception as e:
        print(f"✗ 向后兼容性测试失败: {e}")
        traceback.print_exc()
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理...")
    
    try:
        from src.enhanced_qdrant_vector_db import EnhancedQdrantVectorDB
        
        # 测试无效配置
        try:
            vector_db = EnhancedQdrantVectorDB(
                collection_name="error_test",
                use_memory=True,
                auto_persist=False,
                embedding_config={
                    "api_key": "invalid_key",
                    "base_url": "https://invalid.url",
                    "max_retries": 1
                }
            )
            print("⚠ 无效配置测试：系统应该能处理无效配置")
        except Exception as e:
            print(f"✓ 无效配置正确抛出异常: {type(e).__name__}")
        
        # 测试空数据处理
        vector_db = EnhancedQdrantVectorDB(
            collection_name="empty_test",
            use_memory=True,
            auto_persist=False
        )
        
        # 尝试添加空数据
        result = vector_db.batch_add_texts([], [])
        if not result.get("success"):
            print("✓ 空数据处理正确")
        
        # 尝试查询空数据库
        query_result = vector_db.query("测试查询", n_results=5)
        if len(query_result) == 0:
            print("✓ 空数据库查询处理正确")
        
        return True
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 增强版向量数据库快速测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("基本功能", test_basic_functionality()))
    test_results.append(("CSV数据加载", test_csv_loading()))
    test_results.append(("向后兼容性", test_backward_compatibility()))
    test_results.append(("错误处理", test_error_handling()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！增强版向量数据库工作正常。")
        return True
    else:
        print("⚠ 部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
