{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from sklearn.metrics import f1_score, accuracy_score\n", "from sklearn.metrics import classification_report\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "# Calculate confusion matrix for the most confused classes\n", "from sklearn.metrics import confusion_matrix\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# read the json file\n", "df = pd.read_json(\"../dataset/wos/ablation_full_kg.json\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.7623780240871163, 0.7758)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"Domain\"] = df[\"Domain\"].str.lower()\n", "df[\"gpt3_graph_l1\"] = df[\"gpt3_graph_l1\"].str.lower()\n", "df[\"Domain\"] = df[\"Domain\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_l1\"] = df[\"gpt3_graph_l1\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_l1\"] = df[\"gpt3_graph_l1\"].apply(lambda x: x if x in df[\"Domain\"].values else df[\"Domain\"].sample(1, random_state=42).values[0])\n", "f1_l1 = f1_score(df[\"gpt3_graph_l1\"], df[\"Domain\"], average=\"macro\")\n", "acc_l1 = accuracy_score(df[\"gpt3_graph_l1\"], df[\"Domain\"])\n", "f1_l1, acc_l1"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.6164287199303778, 0.6294)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"area\"] = df[\"area\"].str.lower()\n", "df[\"gpt3_graph_l2\"] = df[\"gpt3_graph_l2\"].str.lower()\n", "df[\"area\"] = df[\"area\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_l2\"] = df[\"gpt3_graph_l2\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_l2\"] = df[\"gpt3_graph_l2\"].apply(lambda x: x if x in df[\"area\"].values else df[\"area\"].sample(1, random_state=42).values[0])\n", "f1_l2 = f1_score(df[\"gpt3_graph_l2\"], df[\"area\"], average=\"macro\")\n", "acc_l2 = accuracy_score(df[\"gpt3_graph_l2\"], df[\"area\"])\n", "f1_l2, acc_l2"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# read the json file\n", "df = pd.read_json(\"../dataset/wos/llm_graph_gpt3_l2.json\")\n", "df = df.sample(n=5000, random_state=42)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.7492401651268584, 0.7632)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"Domain\"] = df[\"Domain\"].str.lower()\n", "df[\"gpt3_graph_l1\"] = df[\"gpt3_graph_l1\"].str.lower()\n", "df[\"Domain\"] = df[\"Domain\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_l1\"] = df[\"gpt3_graph_l1\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_l1\"] = df[\"gpt3_graph_l1\"].apply(lambda x: x if x in df[\"Domain\"].values else df[\"Domain\"].sample(1, random_state=42).values[0])\n", "\n", "f1_l1 = f1_score(df[\"gpt3_graph_l1\"], df[\"Domain\"], average=\"macro\")\n", "acc_l1 = accuracy_score(df[\"gpt3_graph_l1\"], df[\"Domain\"])\n", "f1_l1, acc_l1"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.6515162154605886, 0.6602)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"area\"] = df[\"area\"].str.lower()\n", "df[\"gpt3_graph_l2\"] = df[\"gpt3_graph_l2\"].str.lower()\n", "df[\"area\"] = df[\"area\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_l2\"] = df[\"gpt3_graph_l2\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_l2\"] = df[\"gpt3_graph_l2\"].apply(lambda x: x if x in df[\"area\"].values else df[\"area\"].sample(1, random_state=42).values[0])\n", "f1_l2 = f1_score(df[\"gpt3_graph_l2\"], df[\"area\"], average=\"macro\")\n", "acc_l2 = accuracy_score(df[\"gpt3_graph_l2\"], df[\"area\"])\n", "f1_l2, acc_l2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ollama", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}