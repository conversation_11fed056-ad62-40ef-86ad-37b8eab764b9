"""
SiliconFlow API 客户端
基于原始测试代码封装的推理模型和嵌入模型调用类
提供兼容 OpenAI 的接口设计

主要功能:
1. SiliconFlowChatClient - 推理模型客户端，支持多轮对话和参数配置
2. SiliconFlowEmbeddingClient - 嵌入模型客户端，支持文本向量化
3. SiliconFlowClient - 统一客户端，兼容OpenAI接口设计
4. 集成配置管理，支持环境变量和配置文件
5. 完善的错误处理和重试机制
"""

import requests
import urllib3
import time
import logging
from requests.exceptions import RequestException, Timeout, ConnectionError
from typing import List, Dict, Any, Optional, Union
import json

# 尝试导入配置模块，如果不存在则使用默认配置
try:
    from config import SiliconFlowConfig, config as default_config
except ImportError:
    # 如果配置模块不存在，创建一个简单的配置类
    class SiliconFlowConfig:
        def __init__(self):
            self.config = {
                "api_key": "",
                "base_url": "https://api.siliconflow.cn/v1",
                "request_config": {"timeout": 30, "max_retries": 3, "verify_ssl": False}
            }

        def get_api_key(self): return self.config["api_key"]
        def get_base_url(self): return self.config["base_url"]
        def get_request_config(self): return self.config["request_config"]

    default_config = SiliconFlowConfig()

# 禁用不安全请求的警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SiliconFlowChatClient:
    """
    SiliconFlow 推理模型客户端
    用于调用聊天完成接口，支持多轮对话和各种参数配置

    特性:
    - 支持多种聊天模型
    - 可配置的参数（温度、top_p、最大tokens等）
    - 自动重试机制
    - 详细的错误处理和日志记录
    """

    def __init__(self, api_key: str = None, base_url: str = None, config: SiliconFlowConfig = None):
        """
        初始化推理模型客户端

        Args:
            api_key (str, optional): SiliconFlow API 密钥，如果未提供则从配置获取
            base_url (str, optional): API 基础URL，如果未提供则从配置获取
            config (SiliconFlowConfig, optional): 配置实例，如果未提供则使用默认配置
        """
        # 使用提供的配置或默认配置
        self.config = config or default_config

        # 设置API密钥和基础URL
        self.api_key = api_key or self.config.get_api_key()
        self.base_url = base_url or self.config.get_base_url()
        self.chat_endpoint = "/chat/completions"

        # 获取请求配置
        self.request_config = self.config.get_request_config()

        # 设置请求头
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        # 验证配置
        if not self.api_key:
            logger.warning("⚠️  API密钥未设置，请确保在使用前设置正确的密钥")

    def _make_request_with_retry(self, url: str, payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        带重试机制的请求方法

        Args:
            url (str): 请求URL
            payload (Dict[str, Any]): 请求载荷

        Returns:
            Optional[Dict[str, Any]]: 响应结果或None
        """
        max_retries = self.request_config.get("max_retries", 3)
        timeout = self.request_config.get("timeout", 30)
        verify_ssl = self.request_config.get("verify_ssl", False)

        for attempt in range(max_retries + 1):
            try:
                logger.debug(f"🔄 发送请求 (尝试 {attempt + 1}/{max_retries + 1}): {url}")

                response = requests.post(
                    url,
                    headers=self.headers,
                    json=payload,
                    timeout=timeout,
                    verify=verify_ssl
                )

                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:  # 速率限制
                    if attempt < max_retries:
                        wait_time = 2 ** attempt  # 指数退避
                        logger.warning(f"⏳ 遇到速率限制，等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                        continue
                else:
                    logger.error(f"❌ 请求失败，状态码: {response.status_code}")
                    logger.error(f"错误响应: {response.text}")
                    return None

            except (Timeout, ConnectionError) as e:
                if attempt < max_retries:
                    wait_time = 2 ** attempt
                    logger.warning(f"🔌 连接超时，等待 {wait_time} 秒后重试... ({str(e)})")
                    time.sleep(wait_time)
                    continue
                else:
                    logger.error(f"❌ 连接失败: {str(e)}")
                    return None
            except RequestException as e:
                logger.error(f"❌ 请求异常: {str(e)}")
                return None

        return None
    
    def chat_completions(
        self,
        messages: List[Dict[str, str]],
        model: str = None,
        temperature: float = None,
        max_tokens: Optional[int] = None,
        top_p: float = None,
        frequency_penalty: float = None,
        presence_penalty: float = None,
        stream: bool = None,
        **kwargs
    ) -> Optional[Dict[str, Any]]:
        """
        调用聊天完成接口

        Args:
            messages (List[Dict]): 对话消息列表，格式: [{"role": "user", "content": "消息内容"}]
            model (str, optional): 使用的模型名称，如果未提供则使用配置中的默认模型
            temperature (float, optional): 控制输出随机性，0-2之间
            max_tokens (int, optional): 最大生成token数量
            top_p (float, optional): 核采样参数
            frequency_penalty (float, optional): 频率惩罚
            presence_penalty (float, optional): 存在惩罚
            stream (bool, optional): 是否流式输出
            **kwargs: 其他模型参数

        Returns:
            Dict[str, Any]: API响应结果，失败时返回None
        """
        # 获取默认参数
        try:
            default_params = self.config.get_default_chat_params()
            default_model = self.config.get_default_chat_model()
        except:
            # 如果配置不可用，使用硬编码默认值
            default_params = {
                "temperature": 0.7,
                "top_p": 1.0,
                "frequency_penalty": 0.0,
                "presence_penalty": 0.0,
                "stream": False
            }
            default_model = "Qwen/Qwen2.5-7B-Instruct"

        # 构建请求载荷，使用提供的参数或默认值
        payload = {
            "model": model or default_model,
            "messages": messages,
            "temperature": temperature if temperature is not None else default_params.get("temperature", 0.7),
            "top_p": top_p if top_p is not None else default_params.get("top_p", 1.0),
            "frequency_penalty": frequency_penalty if frequency_penalty is not None else default_params.get("frequency_penalty", 0.0),
            "presence_penalty": presence_penalty if presence_penalty is not None else default_params.get("presence_penalty", 0.0),
            "stream": stream if stream is not None else default_params.get("stream", False)
        }

        # 添加可选参数
        if max_tokens is not None:
            payload["max_tokens"] = max_tokens
        elif "max_tokens" in default_params:
            payload["max_tokens"] = default_params["max_tokens"]

        # 添加其他参数
        payload.update(kwargs)

        logger.info(f"🤖 正在调用推理模型: {payload['model']}")
        logger.info(f"📝 消息数量: {len(messages)}")
        logger.debug(f"🔧 请求参数: {payload}")

        # 使用重试机制发送请求
        url = f"{self.base_url}{self.chat_endpoint}"
        result = self._make_request_with_retry(url, payload)

        if result:
            logger.info("✅ 推理请求成功完成")
            # 记录使用统计
            if "usage" in result:
                usage = result["usage"]
                logger.info(f"📊 Token使用: 输入={usage.get('prompt_tokens', 'N/A')}, "
                          f"输出={usage.get('completion_tokens', 'N/A')}, "
                          f"总计={usage.get('total_tokens', 'N/A')}")
        else:
            logger.error("❌ 推理请求失败")

        return result
    
    def simple_chat(self, content: str, model: str = "Qwen/Qwen2.5-7B-Instruct") -> Optional[str]:
        """
        简化的单轮对话接口
        
        Args:
            content (str): 用户输入内容
            model (str): 使用的模型名称
            
        Returns:
            str: 模型回复内容，失败时返回None
        """
        messages = [{"role": "user", "content": content}]
        result = self.chat_completions(messages, model=model)
        
        if result and "choices" in result and len(result["choices"]) > 0:
            return result["choices"][0]["message"]["content"]
        return None


class SiliconFlowEmbeddingClient:
    """
    SiliconFlow 嵌入模型客户端
    用于调用文本向量化接口，支持批量文本处理

    特性:
    - 支持多种嵌入模型
    - 批量文本处理优化
    - 自动重试机制
    - 向量缓存（可选）
    """

    def __init__(self, api_key: str = None, base_url: str = None, config: SiliconFlowConfig = None):
        """
        初始化嵌入模型客户端

        Args:
            api_key (str, optional): SiliconFlow API 密钥，如果未提供则从配置获取
            base_url (str, optional): API 基础URL，如果未提供则从配置获取
            config (SiliconFlowConfig, optional): 配置实例，如果未提供则使用默认配置
        """
        # 使用提供的配置或默认配置
        self.config = config or default_config

        # 设置API密钥和基础URL
        self.api_key = api_key or self.config.get_api_key()
        self.base_url = base_url or self.config.get_base_url()
        self.embeddings_endpoint = "/embeddings"

        # 获取请求配置
        self.request_config = self.config.get_request_config()

        # 设置请求头
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        # 验证配置
        if not self.api_key:
            logger.warning("⚠️  API密钥未设置，请确保在使用前设置正确的密钥")

    def _make_request_with_retry(self, url: str, payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        带重试机制的请求方法

        Args:
            url (str): 请求URL
            payload (Dict[str, Any]): 请求载荷

        Returns:
            Optional[Dict[str, Any]]: 响应结果或None
        """
        max_retries = self.request_config.get("max_retries", 3)
        timeout = self.request_config.get("timeout", 30)
        verify_ssl = self.request_config.get("verify_ssl", False)

        for attempt in range(max_retries + 1):
            try:
                logger.debug(f"🔄 发送嵌入请求 (尝试 {attempt + 1}/{max_retries + 1}): {url}")

                response = requests.post(
                    url,
                    headers=self.headers,
                    json=payload,
                    timeout=timeout,
                    verify=verify_ssl
                )

                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:  # 速率限制
                    if attempt < max_retries:
                        wait_time = 2 ** attempt  # 指数退避
                        logger.warning(f"⏳ 遇到速率限制，等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                        continue
                else:
                    logger.error(f"❌ 嵌入请求失败，状态码: {response.status_code}")
                    logger.error(f"错误响应: {response.text}")
                    return None

            except (Timeout, ConnectionError) as e:
                if attempt < max_retries:
                    wait_time = 2 ** attempt
                    logger.warning(f"🔌 连接超时，等待 {wait_time} 秒后重试... ({str(e)})")
                    time.sleep(wait_time)
                    continue
                else:
                    logger.error(f"❌ 连接失败: {str(e)}")
                    return None
            except RequestException as e:
                logger.error(f"❌ 请求异常: {str(e)}")
                return None

        return None
    
    def create_embeddings(
        self,
        input_text: Union[str, List[str]],
        model: str = None,
        encoding_format: str = None,
        **kwargs
    ) -> Optional[Dict[str, Any]]:
        """
        创建文本嵌入向量

        Args:
            input_text (Union[str, List[str]]): 输入文本，可以是单个字符串或字符串列表
            model (str, optional): 使用的嵌入模型名称，如果未提供则使用配置中的默认模型
            encoding_format (str, optional): 编码格式，默认为 float
            **kwargs: 其他模型参数

        Returns:
            Dict[str, Any]: API响应结果，包含嵌入向量，失败时返回None
        """
        # 获取默认参数
        try:
            default_params = self.config.get_default_embedding_params()
            default_model = self.config.get_default_embedding_model()
        except:
            # 如果配置不可用，使用硬编码默认值
            default_params = {"encoding_format": "float"}
            default_model = "BAAI/bge-m3"

        # 构建请求载荷，使用提供的参数或默认值
        payload = {
            "model": model or default_model,
            "input": input_text,
            "encoding_format": encoding_format or default_params.get("encoding_format", "float")
        }

        # 添加其他参数
        payload.update(kwargs)

        # 确定输入文本数量
        text_count = len(input_text) if isinstance(input_text, list) else 1
        logger.info(f"🔤 正在调用嵌入模型: {payload['model']}")
        logger.info(f"📄 文本数量: {text_count}")
        logger.debug(f"🔧 请求参数: {payload}")

        # 使用重试机制发送请求
        url = f"{self.base_url}{self.embeddings_endpoint}"
        result = self._make_request_with_retry(url, payload)

        if result:
            logger.info("✅ 嵌入请求成功完成")
            # 记录向量信息
            if "data" in result and len(result["data"]) > 0:
                vector_dim = len(result["data"][0].get("embedding", []))
                logger.info(f"📊 向量维度: {vector_dim}, 处理文本数: {len(result['data'])}")
        else:
            logger.error("❌ 嵌入请求失败")

        return result
    
    def get_embedding_vector(self, text: str, model: str = None) -> Optional[List[float]]:
        """
        获取单个文本的嵌入向量

        Args:
            text (str): 输入文本
            model (str, optional): 使用的嵌入模型名称，如果未提供则使用默认模型

        Returns:
            List[float]: 嵌入向量，失败时返回None
        """
        result = self.create_embeddings(text, model=model)

        if result and "data" in result and len(result["data"]) > 0:
            return result["data"][0]["embedding"]
        return None

    def get_text_similarity(self, text1: str, text2: str, model: str = None) -> Optional[float]:
        """
        计算两个文本的余弦相似度

        Args:
            text1 (str): 第一个文本
            text2 (str): 第二个文本
            model (str, optional): 使用的嵌入模型名称

        Returns:
            float: 余弦相似度值（0-1之间），失败时返回None
        """
        import math

        # 获取两个文本的向量
        vec1 = self.get_embedding_vector(text1, model)
        vec2 = self.get_embedding_vector(text2, model)

        if not vec1 or not vec2:
            logger.error("❌ 无法获取文本向量，相似度计算失败")
            return None

        # 计算余弦相似度
        try:
            dot_product = sum(a * b for a, b in zip(vec1, vec2))
            magnitude1 = math.sqrt(sum(a * a for a in vec1))
            magnitude2 = math.sqrt(sum(a * a for a in vec2))

            if magnitude1 == 0 or magnitude2 == 0:
                return 0.0

            similarity = dot_product / (magnitude1 * magnitude2)
            logger.debug(f"📊 文本相似度计算完成: {similarity:.4f}")
            return similarity

        except Exception as e:
            logger.error(f"❌ 相似度计算异常: {str(e)}")
            return None


class SiliconFlowClient:
    """
    SiliconFlow 统一客户端
    兼容 OpenAI 接口设计，提供推理和嵌入功能的统一访问入口

    特性:
    - 统一的接口设计，兼容 OpenAI API
    - 集成推理和嵌入功能
    - 配置管理和连接测试
    - 完善的错误处理
    """

    def __init__(self, api_key: str = None, base_url: str = None, config: SiliconFlowConfig = None):
        """
        初始化统一客户端

        Args:
            api_key (str, optional): SiliconFlow API 密钥，如果未提供则从配置获取
            base_url (str, optional): API 基础URL，如果未提供则从配置获取
            config (SiliconFlowConfig, optional): 配置实例，如果未提供则使用默认配置
        """
        # 使用提供的配置或默认配置
        self.config = config or default_config

        # 设置API密钥和基础URL
        self.api_key = api_key or self.config.get_api_key()
        self.base_url = base_url or self.config.get_base_url()

        # 初始化子客户端
        self.chat = SiliconFlowChatClient(self.api_key, self.base_url, self.config)
        self.embeddings = SiliconFlowEmbeddingClient(self.api_key, self.base_url, self.config)

        # 验证配置
        if not self.api_key:
            logger.warning("⚠️  API密钥未设置，请确保在使用前设置正确的密钥")
    
    def test_connection(self) -> Dict[str, bool]:
        """
        测试API连接状态

        Returns:
            Dict[str, bool]: 各个接口的连接状态
        """
        logger.info("🚀 SiliconFlow API 连接测试")
        print("🚀 SiliconFlow API 连接测试")
        print("=" * 50)

        results = {}

        # 测试推理接口
        logger.info("🤖 测试推理模型连接...")
        chat_result = self.chat.simple_chat("你好")
        results["chat"] = chat_result is not None

        if results["chat"]:
            logger.info("✅ 推理模型连接正常")
        else:
            logger.error("❌ 推理模型连接失败")

        print("-" * 50)

        # 测试嵌入接口
        logger.info("🔤 测试嵌入模型连接...")
        embedding_result = self.embeddings.get_embedding_vector("测试文本")
        results["embeddings"] = embedding_result is not None

        if results["embeddings"]:
            logger.info("✅ 嵌入模型连接正常")
        else:
            logger.error("❌ 嵌入模型连接失败")

        print("=" * 50)
        print("📊 连接测试总结")
        print(f"推理模型: {'✅ 正常' if results['chat'] else '❌ 异常'}")
        print(f"嵌入模型: {'✅ 正常' if results['embeddings'] else '❌ 异常'}")

        # 记录总体状态
        overall_status = all(results.values())
        if overall_status:
            logger.info("🎉 所有接口连接正常")
        else:
            logger.warning("⚠️  部分接口连接异常")

        return results

    def get_available_models(self) -> Dict[str, List[str]]:
        """
        获取可用的模型列表

        Returns:
            Dict[str, List[str]]: 包含聊天和嵌入模型列表的字典
        """
        try:
            return {
                "chat_models": self.config.get_available_chat_models(),
                "embedding_models": self.config.get_available_embedding_models()
            }
        except:
            # 如果配置不可用，返回默认模型列表
            return {
                "chat_models": ["Qwen/Qwen2.5-7B-Instruct"],
                "embedding_models": ["BAAI/bge-m3"]
            }

    def get_model_info(self) -> Dict[str, str]:
        """
        获取当前使用的模型信息

        Returns:
            Dict[str, str]: 当前模型信息
        """
        try:
            return {
                "default_chat_model": self.config.get_default_chat_model(),
                "default_embedding_model": self.config.get_default_embedding_model(),
                "base_url": self.base_url
            }
        except:
            return {
                "default_chat_model": "Qwen/Qwen2.5-7B-Instruct",
                "default_embedding_model": "BAAI/bge-m3",
                "base_url": self.base_url
            }
