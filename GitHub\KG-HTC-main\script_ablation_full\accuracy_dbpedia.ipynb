{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from sklearn.metrics import f1_score, accuracy_score\n", "from sklearn.metrics import classification_report\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "# Calculate confusion matrix for the most confused classes\n", "from sklearn.metrics import confusion_matrix\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# read the json file\n", "df = pd.read_json(\"../dataset/dbpedia/ablation_full_kg.json\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.9138135540040923, 0.9748)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"l1\"] = df[\"l1\"].str.lower()\n", "df[\"gpt3_graph_l1\"] = df[\"gpt3_graph_l1\"].str.lower()\n", "df[\"gpt3_graph_l1\"] = df[\"gpt3_graph_l1\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_l1\"] = df[\"gpt3_graph_l1\"].apply(lambda x: x if x in df[\"l1\"].values else df[\"l1\"].sample(1, random_state=42).values[0])\n", "f1_l1 = f1_score(df[\"gpt3_graph_l1\"], df[\"l1\"], average=\"macro\")\n", "acc_l1 = accuracy_score(df[\"gpt3_graph_l1\"], df[\"l1\"])\n", "f1_l1, acc_l1"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.6704411482641156, 0.5388)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"l2\"] = df[\"l2\"].str.lower()\n", "df[\"gpt3_graph_l2\"] = df[\"gpt3_graph_l2\"].str.lower()\n", "df[\"gpt3_graph_l2\"] = df[\"gpt3_graph_l2\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_l2\"] = df[\"gpt3_graph_l2\"].apply(lambda x: x if x in df[\"l2\"].values else df[\"l2\"].sample(1, random_state=42).values[0])\n", "f1_l2 = f1_score(df[\"gpt3_graph_l2\"], df[\"l2\"], average=\"macro\")\n", "acc_l2 = accuracy_score(df[\"gpt3_graph_l2\"], df[\"l2\"])\n", "f1_l2, acc_l2"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.8865352244465726, 0.8884)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"l3\"] = df[\"l3\"].str.lower()\n", "df[\"gpt3_graph_l3\"] = df[\"gpt3_graph_l3\"].str.lower()\n", "df[\"gpt3_graph_l3\"] = df[\"gpt3_graph_l3\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_l3\"] = df[\"gpt3_graph_l3\"].apply(lambda x: x if x in df[\"l3\"].values else df[\"l3\"].sample(1, random_state=42).values[0])\n", "f1_l3 = f1_score(df[\"gpt3_graph_l3\"], df[\"l3\"], average=\"macro\")\n", "acc_l3 = accuracy_score(df[\"gpt3_graph_l3\"], df[\"l3\"])\n", "f1_l3, acc_l3"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# read the json file\n", "df = pd.read_json(\"../dataset/dbpedia/llm_graph_gpt3_l3.json\")\n", "df = df.sample(n=5000, random_state=42)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.8865921580594122, 0.9772)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"l1\"] = df[\"l1\"].str.lower()\n", "df[\"gpt3_graph_l1\"] = df[\"gpt3_graph_l1\"].str.lower()\n", "df[\"gpt3_graph_l1\"] = df[\"gpt3_graph_l1\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_l1\"] = df[\"gpt3_graph_l1\"].apply(lambda x: x if x in df[\"l1\"].values else df[\"l1\"].sample(1, random_state=42).values[0])\n", "f1_l1 = f1_score(df[\"gpt3_graph_l1\"], df[\"l1\"], average=\"macro\")\n", "acc_l1 = accuracy_score(df[\"gpt3_graph_l1\"], df[\"l1\"])\n", "f1_l1, acc_l1"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.8112790617219435, 0.837)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# change the l2\tas small letter\n", "df[\"l2\"] = df[\"l2\"].str.lower()\n", "df[\"gpt3_graph_l2\"] = df[\"gpt3_graph_l2\"].str.lower()\n", "# for each row, remove *, ', \" from gpt3_graph_l2\n", "df[\"gpt3_graph_l2\"] = df[\"gpt3_graph_l2\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "# if gpt3_graph_l2 is not in l2, set it to a random label from l2\n", "df[\"gpt3_graph_l2\"] = df[\"gpt3_graph_l2\"].apply(lambda x: x if x in df[\"l2\"].values else df[\"l2\"].sample(1, random_state=42).values[0])\n", "f1_l2 = f1_score(df[\"gpt3_graph_l2\"], df[\"l2\"], average=\"macro\")\n", "acc_l2 = accuracy_score(df[\"gpt3_graph_l2\"], df[\"l2\"])\n", "f1_l2, acc_l2"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.9025194354244129, 0.909)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# change the l3\tas small letter\n", "df[\"l3\"] = df[\"l3\"].str.lower()\n", "df[\"gpt3_graph_l3\"] = df[\"gpt3_graph_l3\"].str.lower()\n", "# for each row, remove *, ', \" from gpt3_graph_l3\n", "df[\"gpt3_graph_l3\"] = df[\"gpt3_graph_l3\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "# if gpt3_graph_l3 is not in l3, set it to a random label from l3\n", "df[\"gpt3_graph_l3\"] = df[\"gpt3_graph_l3\"].apply(lambda x: x if x in df[\"l3\"].values else df[\"l3\"].sample(1, random_state=42).values[0])\n", "\n", "f1_l3 = f1_score(df[\"gpt3_graph_l3\"], df[\"l3\"], average=\"macro\")\n", "acc_l3 = accuracy_score(df[\"gpt3_graph_l3\"], df[\"l3\"])\n", "f1_l3, acc_l3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ollama", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}