import pandas as pd
from pathlib import Path
from tqdm import tqdm
import sys
import json
import time

# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

from src.llm import LLM
from src.vector_db import VectorDB

def run_vector_only_experiment():
    """
    运行Vector-Only实验：仅使用向量检索，无知识图谱结构
    """
    
    # 配置参数
    config = {
        "data_name": "nasa_topics_vector_only",
        "data_path": "dataset/nasa_topics/nasa_topics_val.csv",
        "output_path": "dataset/nasa_topics/vector_only_results.json",
        "vectdb_path": "database/nasa_topics",
        "query_params": {
            "l2_top_k": 12,
            "l3_top_k": 35
        }
    }
    
    print("=== NASA Topics Vector-Only实验 ===")
    print("实验设计: 仅使用向量检索筛选候选，不使用知识图谱结构")
    
    # 读取数据
    try:
        df = pd.read_csv(config["data_path"])
        print(f"成功读取数据: {len(df)} 条记录")
    except Exception as e:
        print(f"读取数据失败: {e}")
        return
    
    # 数据预处理
    df = df.dropna(subset=['Title', 'Text', 'Cat1', 'Cat2', 'Cat3'])
    df = df[df['Cat1'] != "unknown"]
    df = df[df['Cat2'] != "unknown"]
    df = df[df['Cat3'] != "unknown"]
    
    # 限制实验数据量
    max_samples = 500
    if len(df) > max_samples:
        df = df.sample(n=max_samples, random_state=42)
    
    ds = df.to_dict(orient="records")
    print(f"实验数据量: {len(ds)} 条记录")
    
    # 初始化组件
    try:
        llm = LLM()
        vector_db = VectorDB(
            database_path=config["vectdb_path"],
            collection_name="nasa_topics"
        )
        print("✓ LLM和向量数据库初始化成功")
    except Exception as e:
        print(f"✗ 初始化失败: {e}")
        return
    
    # 准备所有分类选项
    all_l1 = df['Cat1'].unique().tolist()
    
    # 开始实验
    print("\n=== 开始Vector-Only分类 ===")
    results = []
    success_count = 0
    error_count = 0
    
    for idx in tqdm(range(len(ds)), desc="Vector-Only分类"):
        data = ds[idx].copy()
        
        try:
            # 构建查询文本
            title = data.get('Title', '').strip()
            text = data.get('Text', '').strip()
            query_text = f"Title: {title}\nDescription: {text[:2000]}"
            
            # 向量检索L2和L3候选
            try:
                l2_results = vector_db.query(
                    query_text, 
                    n_results=config["query_params"]["l2_top_k"],
                    where={"level": "Category2"}
                )
                l2_candidates = l2_results if l2_results else []
                
                l3_results = vector_db.query(
                    query_text,
                    n_results=config["query_params"]["l3_top_k"], 
                    where={"level": "Category3"}
                )
                l3_candidates = l3_results if l3_results else []
                
            except Exception as e:
                print(f"向量检索失败: {e}")
                l2_candidates = []
                l3_candidates = []
            
            # L1级别预测（使用所有L1选项）
            l1_prompt = f"""Classify this NASA Earth science dataset into one of these categories: {', '.join(all_l1)}.

Dataset:
{query_text}

Category:"""
            
            pred_l1 = llm.generate(l1_prompt, max_tokens=50, temperature=0.3)
            pred_l1 = pred_l1.strip().lower()
            
            # L2级别预测（使用向量检索结果）
            if l2_candidates:
                l2_options = l2_candidates[:10]  # 限制选项数量
                l2_prompt = f"""Classify this NASA Earth science dataset into one of these topic categories: {', '.join(l2_options)}.

Retrieved similar topics: {', '.join(l2_candidates[:5])}

Dataset:
{query_text}

Topic Category:"""
            else:
                # 如果检索失败，使用所有L2选项
                all_l2 = df['Cat2'].unique().tolist()
                l2_options = all_l2
                l2_prompt = f"""Classify this NASA Earth science dataset into one of these topic categories: {', '.join(l2_options)}.

Dataset:
{query_text}

Topic Category:"""
            
            pred_l2 = llm.generate(l2_prompt, max_tokens=50, temperature=0.3)
            pred_l2 = pred_l2.strip().lower()
            
            # L3级别预测（使用向量检索结果）
            if l3_candidates:
                l3_options = l3_candidates[:15]  # 限制选项数量
                l3_prompt = f"""Classify this NASA Earth science dataset into one of these specific terms: {', '.join(l3_options)}.

Retrieved similar terms: {', '.join(l3_candidates[:5])}

Dataset:
{query_text}

Specific Term:"""
            else:
                # 如果检索失败，使用部分L3选项
                all_l3 = df['Cat3'].unique().tolist()
                l3_options = all_l3[:20]
                l3_prompt = f"""Classify this NASA Earth science dataset into one of these specific terms: {', '.join(l3_options)}.

Dataset:
{query_text}

Specific Term:"""
            
            pred_l3 = llm.generate(l3_prompt, max_tokens=50, temperature=0.3)
            pred_l3 = pred_l3.strip().lower()
            
            # 保存结果
            data["vector_only_l1"] = pred_l1
            data["vector_only_l2"] = pred_l2
            data["vector_only_l3"] = pred_l3
            data["method"] = "vector_only"
            data["retrieved_l2"] = l2_candidates[:5]
            data["retrieved_l3"] = l3_candidates[:5]
            data["l2_retrieval_count"] = len(l2_candidates)
            data["l3_retrieval_count"] = len(l3_candidates)
            
            results.append(data)
            success_count += 1
            
        except Exception as e:
            error_count += 1
            print(f"\n处理第 {idx+1} 条记录时出错: {e}")
            
            data["vector_only_l1"] = "error"
            data["vector_only_l2"] = "error"
            data["vector_only_l3"] = "error"
            data["error_message"] = str(e)
            data["method"] = "vector_only"
            results.append(data)
        
        # API限制延迟
        time.sleep(0.2)
    
    # 保存结果
    try:
        with open(config["output_path"], "w", encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"✓ Vector-Only结果已保存: {config['output_path']}")
    except Exception as e:
        print(f"✗ 保存结果失败: {e}")
    
    # 统计信息
    print(f"\n=== Vector-Only实验统计 ===")
    print(f"成功: {success_count}, 失败: {error_count}")
    print(f"成功率: {success_count/len(results)*100:.2f}%")
    
    # 检索统计
    if results:
        avg_l2_retrieval = sum(r.get('l2_retrieval_count', 0) for r in results) / len(results)
        avg_l3_retrieval = sum(r.get('l3_retrieval_count', 0) for r in results) / len(results)
        print(f"平均L2检索数量: {avg_l2_retrieval:.2f}")
        print(f"平均L3检索数量: {avg_l3_retrieval:.2f}")

if __name__ == "__main__":
    run_vector_only_experiment()
