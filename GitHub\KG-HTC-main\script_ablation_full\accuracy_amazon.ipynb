{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from sklearn.metrics import f1_score, accuracy_score\n", "from sklearn.metrics import classification_report\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "# Calculate confusion matrix for the most confused classes\n", "from sklearn.metrics import confusion_matrix\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# read the json file\n", "df = pd.read_json(\"../dataset/amazon/ablation_full_kg.json\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.9266108161801618, 0.9282)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"Cat1\"] = df[\"Cat1\"].str.lower()\n", "df[\"gpt3_graph_l1\"] = df[\"gpt3_graph_l1\"].str.lower()\n", "df[\"Cat1\"] = df[\"Cat1\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_l1\"] = df[\"gpt3_graph_l1\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_l1\"] = df[\"gpt3_graph_l1\"].apply(lambda x: x if x in df[\"Cat1\"].values else df[\"Cat1\"].sample(1, random_state=42).values[0])\n", "f1_l1 = f1_score(df[\"gpt3_graph_l1\"], df[\"Cat1\"], average=\"macro\")\n", "acc_l1 = accuracy_score(df[\"gpt3_graph_l1\"], df[\"Cat1\"])\n", "f1_l1, acc_l1"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.633140780722529, 0.7244)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"Cat2\"] = df[\"Cat2\"].str.lower()\n", "df[\"gpt3_graph_l2\"] = df[\"gpt3_graph_l2\"].str.lower()\n", "df[\"Cat2\"] = df[\"Cat2\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_l2\"] = df[\"gpt3_graph_l2\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_l2\"] = df[\"gpt3_graph_l2\"].apply(lambda x: x if x in df[\"Cat2\"].values else df[\"Cat2\"].sample(1, random_state=42).values[0])\n", "f1_l2 = f1_score(df[\"gpt3_graph_l2\"], df[\"Cat2\"], average=\"macro\")\n", "acc_l2 = accuracy_score(df[\"gpt3_graph_l2\"], df[\"Cat2\"])\n", "f1_l2, acc_l2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.4319133519386793, 0.5284)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"Cat3\"] = df[\"Cat3\"].str.lower()\n", "df[\"gpt3_graph_l3\"] = df[\"gpt3_graph_l3\"].str.lower()\n", "df[\"Cat3\"] = df[\"Cat3\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_l3\"] = df[\"gpt3_graph_l3\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_l3\"] = df[\"gpt3_graph_l3\"].apply(lambda x: x if x in df[\"Cat3\"].values else df[\"Cat3\"].sample(1, random_state=42).values[0])\n", "\n", "f1_l3 = f1_score(df[\"gpt3_graph_l3\"], df[\"Cat3\"], average=\"macro\")\n", "acc_l3 = accuracy_score(df[\"gpt3_graph_l3\"], df[\"Cat3\"])\n", "f1_l3, acc_l3"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# read the json file\n", "df = pd.read_json(\"../dataset/amazon/llm_graph_gpt3_h3.json\")\n", "df = df.sample(n=5000, random_state=42)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.9012358963164341, 0.9062)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"Cat1\"] = df[\"Cat1\"].str.lower()\n", "df[\"gpt3_graph\"] = df[\"gpt3_graph\"].str.lower()\n", "df[\"Cat1\"] = df[\"Cat1\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph\"] = df[\"gpt3_graph\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph\"] = df[\"gpt3_graph\"].apply(lambda x: x if x in df[\"Cat1\"].values else df[\"Cat1\"].sample(1, random_state=42).values[0])\n", "f1_l1 = f1_score(df[\"gpt3_graph\"], df[\"Cat1\"], average=\"macro\")\n", "acc_l1 = accuracy_score(df[\"gpt3_graph\"], df[\"Cat1\"])\n", "f1_l1, acc_l1"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.6519102758688894, 0.7454)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"Cat2\"] = df[\"Cat2\"].str.lower()\n", "df[\"gpt3_graph_h2\"] = df[\"gpt3_graph_h2\"].str.lower()\n", "df[\"Cat2\"] = df[\"Cat2\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_h2\"] = df[\"gpt3_graph_h2\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_h2\"] = df[\"gpt3_graph_h2\"].apply(lambda x: x if x in df[\"Cat2\"].values else df[\"Cat2\"].sample(1, random_state=42).values[0])\n", "f1_h2 = f1_score(df[\"gpt3_graph_h2\"], df[\"Cat2\"], average=\"macro\")\n", "acc_h2 = accuracy_score(df[\"gpt3_graph_h2\"], df[\"Cat2\"])\n", "f1_h2, acc_h2"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.46245671768104135, 0.5236)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"Cat3\"] = df[\"Cat3\"].str.lower()\n", "df[\"gpt3_graph_h3\"] = df[\"gpt3_graph_h3\"].str.lower()\n", "df[\"Cat3\"] = df[\"Cat3\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_h3\"] = df[\"gpt3_graph_h3\"].str.replace(\"*\", \"\").str.replace(\"'\", \"\").str.replace('\"', '').str.replace(' ', '')\n", "df[\"gpt3_graph_h3\"] = df[\"gpt3_graph_h3\"].apply(lambda x: x if x in df[\"Cat3\"].values else df[\"Cat3\"].sample(1, random_state=42).values[0])\n", "\n", "f1_h3 = f1_score(df[\"gpt3_graph_h3\"], df[\"Cat3\"], average=\"macro\")\n", "acc_h3 = accuracy_score(df[\"gpt3_graph_h3\"], df[\"Cat3\"])\n", "f1_h3, acc_h3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ollama", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}