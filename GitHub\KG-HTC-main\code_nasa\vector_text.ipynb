import pandas as pd
import sys
from pathlib import Path
from tqdm import tqdm
import logging

# 禁用 httpx 的详细日志
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)
# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

from src.qdrant_vector_db import QdrantVectorDB
from src.graph_db import GraphDB
from src.vector_db import VectorDB


def init_nasa_dataset():
    """
    初始化NASA数据集的知识图谱和向量数据库
    """
    
    # 配置参数
    config = {
        "data_name": "nasa-test",
        "data_path": "dataset/nasa/nasa_train.csv",
        "output_path": "dataset/nasa/llm_graph_gpt3.json",
        # 注意：QdrantVectorDB使用内存模式，不需要vectdb_path
        "vectdb_path": "database/nasa-test",
        "template": {
            "sys": "prompts/system/nasa/llm_graph.txt",
            "user": "prompts/user/nasa/llm_graph.txt"
        },
        "query_params": {
            "l2_top_k": 11,  # NASA数据集二级分类较多，增加检索数量
            "l3_top_k": 53   # NASA数据集三级分类很多，增加检索数量
        }
    }

    print("=== 初始化NASA数据集 ===")
    print(f"数据路径: {config['data_path']}")
    
    # 读取预处理后的数据
    try:
        df = pd.read_csv(config["data_path"])
        print(f"成功读取数据: {len(df)} 条记录")
    except FileNotFoundError:
        print(f"错误: 找不到数据文件 {config['data_path']}")
        print("请先运行 preprocess_nasa.py 进行数据预处理")
        return
    
    # 数据质量检查
    print("\n=== 数据质量检查 ===")
    required_columns = ['Title', 'Text', 'Cat1', 'Cat2', 'Cat3']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"错误: 缺少必要字段 {missing_columns}")
        return
    
    # 移除空值和unknown标签
    initial_count = len(df)
    df = df.dropna(subset=required_columns)
    df = df[df['Cat1'] != "unknown"]
    df = df[df['Cat2'] != "unknown"] 
    df = df[df['Cat3'] != "unknown"]
    df = df[df['Text'].notna()]
    df = df[df['Title'].notna()]
    
    print(f"数据清洗后: {len(df)} 条记录 (移除了 {initial_count - len(df)} 条)")
    
    
    # 初始化数据库连接
    print("\n=== 初始化数据库连接 ===")
    try:
        # QdrantVectorDB构造函数参数：host, port, collection_name, use_memory
        # 使用内存模式，无需database_path参数
        vector_db = VectorDB(
            database_path=config["vectdb_path"],
            collection_name=config["data_name"]
        )
        print("✓ 向量数据库连接成功")
    except Exception as e:
        print(f"✗ 向量数据库连接失败: {e}")
        return
    
    vector_db.batch_add(
        
    )
    
    
    # 构建向量数据库
    print("\n=== 构建向量数据库 ===")
    

    



init_nasa_dataset()
