import pandas as pd
import numpy as np
from sklearn.metrics import f1_score, accuracy_score, classification_report
from sklearn.metrics import confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import json
from pathlib import Path

def load_results(result_path: str):
    """加载实验结果"""
    try:
        with open(result_path, 'r', encoding='utf-8') as f:
            results = json.load(f)
        return pd.DataFrame(results)
    except Exception as e:
        print(f"加载结果文件失败: {e}")
        return None

def standardize_labels(df: pd.DataFrame):
    """标准化标签格式"""
    # 标准化真实标签
    df['Cat1_std'] = df['Cat1'].str.lower().str.replace(' ', '_').str.replace('/', '_')
    df['Cat2_std'] = df['Cat2'].str.lower().str.replace(' ', '_').str.replace('/', '_')
    df['Cat3_std'] = df['Cat3'].str.lower().str.replace(' ', '_').str.replace('/', '_')
    
    # 标准化预测标签
    df['pred_l1_std'] = df['gpt3_graph_l1'].str.lower().str.replace(' ', '_').str.replace('/', '_')
    df['pred_l2_std'] = df['gpt3_graph_l2'].str.lower().str.replace(' ', '_').str.replace('/', '_')
    df['pred_l3_std'] = df['gpt3_graph_l3'].str.lower().str.replace(' ', '_').str.replace('/', '_')
    
    return df

def calculate_metrics(df: pd.DataFrame):
    """计算各种评估指标"""
    # 过滤掉错误记录
    df_valid = df[df['gpt3_graph_l1'] != 'error'].copy()
    
    if len(df_valid) == 0:
        print("没有有效的预测结果")
        return None
    
    print(f"有效预测记录数: {len(df_valid)}/{len(df)}")
    
    # 标准化标签
    df_valid = standardize_labels(df_valid)
    
    # 计算准确率
    acc_l1 = accuracy_score(df_valid['Cat1_std'], df_valid['pred_l1_std'])
    acc_l2 = accuracy_score(df_valid['Cat2_std'], df_valid['pred_l2_std'])
    acc_l3 = accuracy_score(df_valid['Cat3_std'], df_valid['pred_l3_std'])
    
    # 计算F1分数
    f1_l1_macro = f1_score(df_valid['Cat1_std'], df_valid['pred_l1_std'], average='macro', zero_division=0)
    f1_l2_macro = f1_score(df_valid['Cat2_std'], df_valid['pred_l2_std'], average='macro', zero_division=0)
    f1_l3_macro = f1_score(df_valid['Cat3_std'], df_valid['pred_l3_std'], average='macro', zero_division=0)
    
    f1_l1_micro = f1_score(df_valid['Cat1_std'], df_valid['pred_l1_std'], average='micro', zero_division=0)
    f1_l2_micro = f1_score(df_valid['Cat2_std'], df_valid['pred_l2_std'], average='micro', zero_division=0)
    f1_l3_micro = f1_score(df_valid['Cat3_std'], df_valid['pred_l3_std'], average='micro', zero_division=0)
    
    # 层次化准确率（所有层级都正确）
    hierarchical_acc = ((df_valid['Cat1_std'] == df_valid['pred_l1_std']) & 
                       (df_valid['Cat2_std'] == df_valid['pred_l2_std']) & 
                       (df_valid['Cat3_std'] == df_valid['pred_l3_std'])).mean()
    
    metrics = {
        'accuracy': {
            'L1': acc_l1,
            'L2': acc_l2,
            'L3': acc_l3,
            'Hierarchical': hierarchical_acc
        },
        'f1_macro': {
            'L1': f1_l1_macro,
            'L2': f1_l2_macro,
            'L3': f1_l3_macro
        },
        'f1_micro': {
            'L1': f1_l1_micro,
            'L2': f1_l2_micro,
            'L3': f1_l3_micro
        }
    }
    
    return metrics, df_valid

def print_metrics(metrics):
    """打印评估指标"""
    print("\n=== NASA数据集KG-HTC性能评估 ===")
    print("\n1. 准确率 (Accuracy):")
    for level, acc in metrics['accuracy'].items():
        print(f"   {level}: {acc:.4f} ({acc*100:.2f}%)")
    
    print("\n2. F1分数 - Macro平均:")
    for level, f1 in metrics['f1_macro'].items():
        print(f"   {level}: {f1:.4f}")
    
    print("\n3. F1分数 - Micro平均:")
    for level, f1 in metrics['f1_micro'].items():
        print(f"   {level}: {f1:.4f}")

def analyze_errors(df_valid: pd.DataFrame):
    """错误分析"""
    print("\n=== 错误分析 ===")
    
    # L1级别错误分析
    l1_errors = df_valid[df_valid['Cat1_std'] != df_valid['pred_l1_std']]
    print(f"\nL1级别错误数量: {len(l1_errors)}/{len(df_valid)} ({len(l1_errors)/len(df_valid)*100:.2f}%)")
    
    if len(l1_errors) > 0:
        print("L1级别最常见的错误:")
        error_pairs = l1_errors.groupby(['Cat1_std', 'pred_l1_std']).size().sort_values(ascending=False)
        for (true_label, pred_label), count in error_pairs.head(5).items():
            print(f"   {true_label} -> {pred_label}: {count} 次")
    
    # L2级别错误分析
    l2_errors = df_valid[df_valid['Cat2_std'] != df_valid['pred_l2_std']]
    print(f"\nL2级别错误数量: {len(l2_errors)}/{len(df_valid)} ({len(l2_errors)/len(df_valid)*100:.2f}%)")
    
    # L3级别错误分析
    l3_errors = df_valid[df_valid['Cat3_std'] != df_valid['pred_l3_std']]
    print(f"\nL3级别错误数量: {len(l3_errors)}/{len(df_valid)} ({len(l3_errors)/len(df_valid)*100:.2f}%)")

def plot_confusion_matrix(df_valid: pd.DataFrame, level: str, save_path: str = None):
    """绘制混淆矩阵"""
    true_col = f'Cat{level}_std'
    pred_col = f'pred_l{level}_std'
    
    # 获取所有标签
    all_labels = sorted(list(set(df_valid[true_col].unique()) | set(df_valid[pred_col].unique())))
    
    # 如果标签太多，只显示前20个最常见的
    if len(all_labels) > 20:
        top_labels = df_valid[true_col].value_counts().head(20).index.tolist()
        df_subset = df_valid[df_valid[true_col].isin(top_labels)]
        all_labels = top_labels
    else:
        df_subset = df_valid
    
    # 计算混淆矩阵
    cm = confusion_matrix(df_subset[true_col], df_subset[pred_col], labels=all_labels)
    
    # 绘制热力图
    plt.figure(figsize=(12, 10))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=all_labels, yticklabels=all_labels)
    plt.title(f'Confusion Matrix - Level {level}')
    plt.xlabel('Predicted')
    plt.ylabel('True')
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"混淆矩阵已保存: {save_path}")
    
    plt.show()

def analyze_retrieval_performance(df_valid: pd.DataFrame):
    """分析检索性能"""
    print("\n=== 检索性能分析 ===")
    
    # 检查检索到的L2标签是否包含真实标签
    l2_retrieval_hits = 0
    l3_retrieval_hits = 0
    
    for _, row in df_valid.iterrows():
        if 'retrieved_l2' in row and isinstance(row['retrieved_l2'], list):
            if row['Cat2_std'] in [label.lower().replace(' ', '_') for label in row['retrieved_l2']]:
                l2_retrieval_hits += 1
        
        if 'retrieved_l3' in row and isinstance(row['retrieved_l3'], list):
            if row['Cat3_std'] in [label.lower().replace(' ', '_') for label in row['retrieved_l3']]:
                l3_retrieval_hits += 1
    
    print(f"L2检索命中率: {l2_retrieval_hits}/{len(df_valid)} ({l2_retrieval_hits/len(df_valid)*100:.2f}%)")
    print(f"L3检索命中率: {l3_retrieval_hits}/{len(df_valid)} ({l3_retrieval_hits/len(df_valid)*100:.2f}%)")
    
    # 分析子图大小
    if 'subgraph_size' in df_valid.columns:
        avg_subgraph_size = df_valid['subgraph_size'].mean()
        print(f"平均子图大小: {avg_subgraph_size:.2f}")

def main():
    """主函数"""
    # 结果文件路径
    result_path = "dataset/nasa/llm_graph_gpt3.json"
    
    # 检查文件是否存在
    if not Path(result_path).exists():
        print(f"结果文件不存在: {result_path}")
        print("请先运行 gpt_nasa.py 进行实验")
        return
    
    # 加载结果
    print("加载实验结果...")
    df = load_results(result_path)
    if df is None:
        return
    
    print(f"加载了 {len(df)} 条结果记录")
    
    # 计算指标
    print("计算评估指标...")
    result = calculate_metrics(df)
    if result is None:
        return
    
    metrics, df_valid = result
    
    # 打印指标
    print_metrics(metrics)
    
    # 错误分析
    analyze_errors(df_valid)
    
    # 检索性能分析
    analyze_retrieval_performance(df_valid)
    
    # 绘制混淆矩阵（可选）
    plot_choice = input("\n是否绘制混淆矩阵? (y/n): ").lower().strip()
    if plot_choice == 'y':
        for level in ['1', '2', '3']:
            plot_confusion_matrix(df_valid, level, f"nasa_confusion_matrix_l{level}.png")
    
    # 保存详细报告
    report_path = "nasa_evaluation_report.txt"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("NASA数据集KG-HTC性能评估报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("1. 基本信息:\n")
        f.write(f"   总记录数: {len(df)}\n")
        f.write(f"   有效记录数: {len(df_valid)}\n")
        f.write(f"   成功率: {len(df_valid)/len(df)*100:.2f}%\n\n")
        
        f.write("2. 准确率:\n")
        for level, acc in metrics['accuracy'].items():
            f.write(f"   {level}: {acc:.4f} ({acc*100:.2f}%)\n")
        
        f.write("\n3. F1分数 (Macro):\n")
        for level, f1 in metrics['f1_macro'].items():
            f.write(f"   {level}: {f1:.4f}\n")
    
    print(f"\n详细报告已保存: {report_path}")

if __name__ == "__main__":
    main()
