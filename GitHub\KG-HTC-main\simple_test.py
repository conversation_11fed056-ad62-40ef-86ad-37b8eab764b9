#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试新的 QdrantVectorDB 实现
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic():
    """基本功能测试"""
    print("=== 基本功能测试 ===")
    
    try:
        from src.qdrant_vector_db import QdrantVectorDB
        
        # 初始化向量数据库
        print("1. 初始化向量数据库...")
        vector_db = QdrantVectorDB(
            collection_name="simple_test",
            use_memory=True,
            auto_persist=False  # 关闭持久化以简化测试
        )
        print("✓ 初始化成功")
        
        # 添加测试数据
        print("2. 添加测试数据...")
        result = vector_db.add_text(
            text="这是一个测试文本，用于验证向量数据库功能。",
            metadata={"category": "test", "level": "Category1"}
        )
        print(f"✓ 添加结果: {result}")
        
        # 批量添加数据（向后兼容接口）
        print("3. 批量添加数据...")
        vector_db.batch_add(
            titles=["标题1", "标题2"],
            texts=["文本内容1", "文本内容2"],
            metadatas=[{"level": "Category2"}, {"level": "Category3"}]
        )
        print("✓ 批量添加成功")
        
        # 查询数据
        print("4. 查询数据...")
        query_result = vector_db.query("测试文本", n_results=3)
        print(f"✓ 查询成功，返回 {len(query_result['documents'][0])} 个结果")
        
        # 测试分级查询
        print("5. 测试分级查询...")
        l1_result = vector_db.query_l1("测试")
        l2_result = vector_db.query_l2("测试")
        l3_result = vector_db.query_l3("测试")
        print(f"✓ L1: {len(l1_result['documents'][0])} 个结果")
        print(f"✓ L2: {len(l2_result['documents'][0])} 个结果")
        print(f"✓ L3: {len(l3_result['documents'][0])} 个结果")
        
        # 获取集合信息
        print("6. 获取集合信息...")
        info = vector_db.get_collection_info()
        print(f"✓ 主集合: {info['main_collection']['count']} 条数据")
        print(f"✓ 向量维度: {info['main_collection']['vector_size']}")
        
        print("\n🎉 所有基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pipeline_integration():
    """测试与Pipeline的集成"""
    print("\n=== Pipeline集成测试 ===")
    
    try:
        # 模拟Pipeline的初始化方式
        from src.qdrant_vector_db import QdrantVectorDB
        
        config = {
            "data_name": "test_integration",
            "qdrant_host": None,
            "qdrant_port": 6333,
            "qdrant_use_memory": True
        }
        
        # 使用Pipeline的初始化方式
        vector_db = QdrantVectorDB(
            host=config.get("qdrant_host"),
            port=config.get("qdrant_port", 6333),
            collection_name=config.get("data_name", "default"),
            use_memory=config.get("qdrant_use_memory", True)
        )
        
        print("✓ Pipeline集成初始化成功")
        
        # 模拟添加分类数据
        labels_l1 = ["earth_science", "computer_science"]
        labels_l2 = ["atmosphere", "artificial_intelligence"]
        labels_l3 = ["aerosols", "machine_learning"]
        
        vector_db.batch_add(
            titles=labels_l1,
            texts=labels_l1,
            metadatas=[{"level": "Category1"}] * len(labels_l1)
        )
        
        vector_db.batch_add(
            titles=labels_l2,
            texts=labels_l2,
            metadatas=[{"level": "Category2"}] * len(labels_l2)
        )
        
        vector_db.batch_add(
            titles=labels_l3,
            texts=labels_l3,
            metadatas=[{"level": "Category3"}] * len(labels_l3)
        )
        
        print("✓ 分类数据添加成功")
        
        # 测试Pipeline中使用的查询方法
        l2_results = vector_db.query_l2("artificial intelligence", n_results=3)
        l3_results = vector_db.query_l3("machine learning", n_results=3)
        
        print(f"✓ L2查询结果: {l2_results['documents'][0]}")
        print(f"✓ L3查询结果: {l3_results['documents'][0]}")
        
        print("\n🎉 Pipeline集成测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ Pipeline集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行所有测试"""
    print("🚀 简单测试开始")
    print("=" * 50)
    
    success1 = test_basic()
    success2 = test_pipeline_integration()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！新的QdrantVectorDB实现工作正常。")
        return True
    else:
        print("\n❌ 部分测试失败，请检查实现。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
