"""
SiliconFlow 客户端包安装脚本
自动安装依赖并验证安装
"""

import subprocess
import sys
import os

def install_dependencies():
    """安装依赖包"""
    print("🔧 正在安装 SiliconFlow 客户端包依赖...")
    
    try:
        # 获取当前脚本所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        requirements_file = os.path.join(current_dir, "requirements.txt")
        
        if not os.path.exists(requirements_file):
            print("❌ 未找到 requirements.txt 文件")
            return False
        
        # 安装依赖
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", requirements_file
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 依赖安装成功!")
            return True
        else:
            print(f"❌ 依赖安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 安装过程中出现异常: {e}")
        return False

def verify_installation():
    """验证安装是否成功"""
    print("\n🧪 验证安装...")
    
    try:
        # 测试导入核心模块
        import requests
        import urllib3
        print("✅ 核心依赖导入成功")
        
        # 测试导入客户端类
        from siliconflow_client import SiliconFlowClient, SiliconFlowChatClient, SiliconFlowEmbeddingClient
        from config import SiliconFlowConfig
        print("✅ 客户端类导入成功")
        
        # 测试创建客户端实例（不需要API密钥）
        config = SiliconFlowConfig()
        print("✅ 配置类创建成功")
        
        print("\n🎉 安装验证通过!")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证过程中出现异常: {e}")
        return False

def show_usage_info():
    """显示使用信息"""
    print("\n📖 使用指南:")
    print("=" * 50)
    print("1. 设置 API 密钥:")
    print("   export SILICONFLOW_API_KEY='your-api-key-here'")
    print("\n2. 基本使用:")
    print("   from siliconflow_client_package import SiliconFlowClient")
    print("   client = SiliconFlowClient()")
    print("   response = client.chat.simple_chat('你好')")
    print("\n3. 运行测试:")
    print("   python quick_test_new_client.py")
    print("   python test_siliconflow.py")
    print("\n4. 查看示例:")
    print("   python example_usage.py")
    print("\n5. 查看文档:")
    print("   README.md - 包使用说明")
    print("   README_SiliconFlow.md - 详细API文档")
    print("   封装总结.md - 项目总结")

def main():
    """主安装流程"""
    print("🌟 SiliconFlow 客户端包安装程序")
    print("=" * 50)
    
    # 检查 Python 版本
    if sys.version_info < (3, 7):
        print("❌ 需要 Python 3.7 或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"✅ Python 版本: {sys.version}")
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 安装失败，请检查错误信息")
        return False
    
    # 验证安装
    if not verify_installation():
        print("\n❌ 验证失败，请检查安装")
        return False
    
    # 显示使用信息
    show_usage_info()
    
    print("\n🎉 安装完成!")
    print("现在您可以开始使用 SiliconFlow 客户端包了。")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
